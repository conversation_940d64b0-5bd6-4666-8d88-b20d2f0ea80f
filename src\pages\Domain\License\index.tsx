import { render } from "@testing-library/react"
import { <PERSON><PERSON>, <PERSON>, Divider, message, Modal, Popover, Select, Space, Table } from "antd"
import { useContext, useEffect, useState } from "react"
import "./index.less"
import type { ColumnsType } from 'antd/es/table';
import EditAndAdd from "./components/editAndEdit"
import ProviderContext from "../../providerContext"
import wordList from "../../../config/wordList"
import CustomTable from "../../../components/CustomTable"
import BugsLinkButton from "../../../components/BugsLinkButton"
import api from "../../../api"
import { title } from "process"
import moment from "moment";
//站点证书配置
const DomainLicense = (props: any) => {
    const Context=useContext(ProviderContext)
    const [nowEditId,setNowEditId]=useState("")
    const [pageInfo, setPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const columns:ColumnsType<any> = [
        {
            title: wordList["类型"][Context.lan] ,
            dataIndex: 'type',
            key: 'type',
            render: (type) => {
                return <div style={{
                    height: '30px',
                    lineHeight: '30px'
                }}>
                    {
                        {
                            1:  wordList["申请免费证书"][Context.lan],
                            0:  wordList["上传已有证书"][Context.lan]
                        }[type]
                    }
                </div>
            }
        },
        {
            title: wordList["域名"][Context.lan],
            key: 'domains',
            dataIndex: 'domains',
            width: 300,
            render: (_: any, record: any, index: any) => {
                
                return <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                }}>
                    <div
                        className="domain-column"
                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {
                            (record?.domains || []).length == 0 ?
                                "未配置匹配方式"
                                : (record.domains.length == 0 && record.domains[0] == '*'
                                    ? "通配所有域名" : (<Space>
                                        {
                                            record?.domains.map((item, index) => {
                                                return <>
                                                    {
                                                        index > 0
                                                            ? <Divider type="vertical"></Divider>
                                                            : null
                                                    }
                                                    {item}

                                                </>
                                            })
                                        }
                                    </Space>))
                        }
                    </div>
                </div>
            }
        },
        {
            title:"颁发机构",
            dataIndex:"issuer",
            key:"issuer",
            align:'center'
        },
        {
            title:  wordList["有效期至"][Context.lan],
            dataIndex: 'detail',
            key: 'detail',
            width:200,
            align:'center',
            render: (_: any, record: any) => {
                return moment(record?.not_after || '-').format("YYYY-MM-DD HH:mm:ss")
            }
        },
        {
            title:"关联站点",
            dataIndex:"related_sites",
            key:"related_sites",
            align:'center',
            render:(sites)=>{

                return (sites ?? []).join(" | ") || '-'
            }
        },
        {
            title: '',
            dataIndex: 'opetation',
            width: 30,
            render: (_,record) => {
                return <Popover trigger={"hover"}
                    content={
                        <div style={{
                            width: '70px'
                        }}>
                            <BugsLinkButton
                                size="large"
                                onClick={() => {
                                    setNowEditId(record?.id)
                                    setOpenEdit(
                                        {
                                            type:"edit",
                                            open:true
                                        }
                                    )
                                }}
                                style={{
                                    color: "#cc1212",
                                }}>{wordList["编辑"][Context.lan]}</BugsLinkButton>

                            {/*<BugsLinkButton
                                size="large"
                                style={{
                                    color: "#cc1212",
                                }}>
                                {wordList["续期"][Context.lan]}
                            </BugsLinkButton>*/}
                            <Divider style={{
                                marginTop: '10px',
                                marginBottom: '10px'
                            }}>

                            </Divider>
                            <BugsLinkButton
                                onClick={()=>{
                                    Modal.confirm({
                                        title:"操作确认",
                                        content:"确认删除此证书",
                                        onOk:()=>{
                                            return new Promise<void>((resolve,reject)=>{
                                                api.keyLicense.deleteLicense(record?.id || '-')
                                                .then(res=>{
                                                    if(res?.data?.data){
                                                        message.success("删除成功")
                                                        let page=pageInfo.page
                                                        if(data.length==1 && pageInfo.page>1){
                                                            //原本只剩一个，删除之后就应该到前一页
                                                            page-=1
                                                        }
                                                        getList(page,pageInfo.page_size)
                                                        resolve()
                                                    }else{
                                                        reject()
                                                    }
                                                })
                                            })
                                        }
                                    })
                                }}
                                size="large"
                                style={{
                                    color: "red",
                                }}>
                                
                                {wordList["删除"][Context.lan]}
                            </BugsLinkButton>
                        </div>
                    }>
                    <BugsLinkButton
                        style={{
                            border: 'none',
                            background: 'transparent'
                        }}
                        icon={
                            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="64884" width="24" height="24"><path d="M512 42.666667a469.333333 469.333333 0 1 0 469.333333 469.333333A469.333333 469.333333 0 0 0 512 42.666667z m0 864a394.666667 394.666667 0 1 1 394.666667-394.666667 395.146667 395.146667 0 0 1-394.666667 394.666667z" p-id="64885" fill="#cc1212"></path><path d="M304.906667 512m-66.666667 0a66.666667 66.666667 0 1 0 133.333333 0 66.666667 66.666667 0 1 0-133.333333 0Z" p-id="64886" fill="#cc1212"></path><path d="M512 512m-66.666667 0a66.666667 66.666667 0 1 0 133.333334 0 66.666667 66.666667 0 1 0-133.333334 0Z" p-id="64887" fill="#cc1212"></path><path d="M719.093333 512m-66.666666 0a66.666667 66.666667 0 1 0 133.333333 0 66.666667 66.666667 0 1 0-133.333333 0Z" p-id="64888" fill="#cc1212"></path></svg>
                        }></BugsLinkButton>
                </Popover>
            }
        }
    ]
    const [openEdit, setOpenEdit] = useState({
        type: 'add',
        open: false
    })
    const [data, setData] = useState<any>([
        /*{
            type: "free",
            domainRule: ['*', 'www.test.moule.cn'],
            detail: {
                organization: '谋乐网络',
                timend: '2024-12-31'
            }
        },
        {
            type: "upload",
            domainRule: ['*', 'www.test.moule.cn'],
            detail: {
                organization: '谋乐网络',
                timend: '2024-12-31'
            }
        }*/
    ])
    const [getting,setGetting]=useState(false)
    const getList=(page=1,pageSize=10)=>{
        setGetting(true)
        api.keyLicense.getLicense({
            pageSize,
            page
        })
        .then(res=>{
            setGetting(false)
            if(res?.data?.data?.rows){
                if(res.data.data.rows.length==0 && page>1){
                    getList(page-1,pageSize)
                    return
                }
                setData(res.data.data.rows )
                setPageInfo({
                    ...res.data.data.page_info
                })
            }
        })
    }
    useEffect(()=>{
        getList()
    },[])
    return <>
        {
            openEdit.open
                ? <EditAndAdd 
                update={()=>{
                    getList(pageInfo.page, pageInfo.page_size)
                }}
                type={
                    openEdit.type
                }
                target={nowEditId}
                close={() => {
                    setOpenEdit({
                        ...openEdit,
                        open: false
                    })
                }}></EditAndAdd>
                : null
        }
        <Card
            title={wordList["证书管理"][Context.lan]}
            bordered
            className="license-control"
            style={{
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: '100%',
                overflowY: 'auto'
            }}
            extra={
                <Button type="primary"
                    onClick={(e) => {
                        setOpenEdit({
                            type: 'add',
                            open: true
                        })
                    }}
                    style={{
                        background: "#cc1212",
                        borderRadius: '5px',
                        border: 'none'
                    }}>
                    {wordList["添加证书"][Context.lan]}
                </Button>
            }>
            <div title={wordList["证书列表"][Context.lan]}
               
                style={{
                    borderRadius: '10px',
                    width: '100%',
                    paddingLeft: 0,
                    paddingRight: 0,
                    height: 'auto',
                    minHeight: '300px',
                    maxHeight: 'calc(100vh - 200px)',
                    overflowY: 'auto'
                }}>

                <div style={{
                    width: '100%',
                    height: "100%",
                    paddingLeft: '20px',
                    paddingRight: '20px',
                    boxSizing: 'border-box',
                }}>
                    <CustomTable
                        columns={columns}
                        dataSource={data}
                        useBorder={true}
                        loading={getting}
                        onChange={(e) => {
                                   
                            getList(e.current, e.pageSize)
                        }}
                        pagination={{
                            pageSize: pageInfo.page_size,
                            current: pageInfo.page,
                            total: pageInfo.total,
                            showSizeChanger:true
                        }}
                        scroll={{ x: data.length==0?0:'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
                        size={'middle'}
                    ></CustomTable>
                </div>
            </div>
         

        </Card>

    </>
}
export default DomainLicense