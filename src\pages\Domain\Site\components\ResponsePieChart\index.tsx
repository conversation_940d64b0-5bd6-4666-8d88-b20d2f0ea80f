import { useContext, useState } from "react"
import wordList from "../../../../../config/wordList"
import ProviderContext from "../../../../providerContext"
import EChartsReact from 'echarts-for-react'
import * as echarts from "echarts"
import "./index.less"
/**时段访问拦截分析 */
const ResponsePieChart = (props) => {
    const Context = useContext(ProviderContext)
    const [anlysiOptions, setAnalysiOptions] = useState({
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center'
        },
        series: [
            {
                top:'10%',
                name: 'Access From',
                type: 'pie',
                radius: ['40%', '80%'],
                avoidLabelOverlap: false,
                padAngle: 5,
                itemStyle: {
                    borderRadius: 10
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 40,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: 1048, name: '200' },
                    { value: 735, name: '304' },
                    { value: 580, name: '404' },
                    { value: 484, name: '500' },
                    { value: 300, name: '401' }
                ]
            }
        ]
    })
    return <div className="response-pie-chart">

        <div className="card-title">{wordList['响应分析'][Context.lan]}</div>

        <EChartsReact key={Context.lan} style={{ height: 'calc(100% - 40px)', flex: 1 }} option={anlysiOptions} notMerge={true} ></EChartsReact>
    </div>
}
export default ResponsePieChart