.colorModal{
    .ant-modal-content{
        overflow: visible !important;
    }
    .ant-modal-header{
        border-top-left-radius: 30px;
        border-top-right-radius: 30px;
    }
}
.color-item{
    cursor: pointer;
    margin-top: 10px;
    border: 3px solid #ddd;
    box-shadow: none;
    padding: 8px;
    border-radius: 4px;
    background-image: repeating-linear-gradient(45deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 5px, transparent 5px, transparent 10px);
}
.html-page-item{
    width: calc(25% - 10px);
    min-width: 200px;
    box-sizing: border-box;
    cursor: pointer;
    user-select: none;
    border: 1px solid #ddd;
    transition: .2s;
    gap: 10px;
    border-radius: 5px;
    height: 50px;
    padding-left: 10px;
    padding-right: 10px;
    display: flex;
    align-items: center;
    background-color: white;
}
.html-page-item:hover{
    border: 1px solid #cc1212;
}
.html-page-item-code{
    color: #999999;
    font-size: 12px;
}