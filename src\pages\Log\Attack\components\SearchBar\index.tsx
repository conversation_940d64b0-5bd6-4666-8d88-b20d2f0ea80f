import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Divider, Input, InputNumber, Popover, Select, Space, Tree, TreeSelect } from "antd"
import "./index.less"
import { useContext, useEffect, useState } from "react"
import config from "../../../../../config"
import wordList from "../../../../../config/wordList"
import ProviderContext from "../../../../providerContext"
const { AttackTypeRefer } = config
let updateTimer: any = null
const SearchBar = (props: any) => {
    const Context = useContext(ProviderContext)
    const [openSelectType, setOpenSelectType] = useState(false)
    const [searchQuery, setSearchQuery] = useState({})
    const [value, setValue] = useState<any>([])
    const onChange = (newValue: string[]) => {
        setValue(newValue)
        updateQuqery({
            'attack_type': newValue[0] == 'all' ? Object.keys(AttackTypeRefer).join(",") : newValue.join(",")
        })
    }
    const updateQuqery = (newQuery) => {
        clearTimeout(updateTimer)
        updateTimer = setTimeout(() => {
            let searchData = {
                ...searchQuery,
                ...newQuery
            }
            for (let key in searchData) {
                if (!searchData[key] && searchData[key] !== 0) {
                    try {
                        delete searchData[key]
                    } catch { }
                }
            }
            props.searchQuery && props.searchQuery(searchData)
            setSearchQuery(searchData)
        }, 300)

    }
    const logTypeRefer: any = {
        "action": {
            name: wordList["动作"][Context.lan],
            node: <Space style={
                props.showAll?
                {
                width:'400px'
            }:{}}>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["动作"][Context.lan]}</div>
                <Select style={{
                    width: "100px",
                    borderRadius: '10px'
                }}
                    onChange={(e) => {
                        updateQuqery({ 'action': e == 'all' ? '' : e })
                    }}
                    options={[
                        {
                            label: wordList["全部"][Context.lan],
                            value: "all"
                        },
                        {
                            label: wordList["已拦截"][Context.lan],
                            value: 1
                        },
                        {
                            label: wordList["未拦截"][Context.lan],
                            value: 0
                        }
                    ]} defaultValue={"all"}></Select>
            </Space>
        },
        "ip": {
            name: "IP",
            node: <Space style={
                props.showAll?
                {
                width:'400px'
            }:{}}>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["攻击IP"][Context.lan]}</div>
                <Input onChange={(e: any) => {
                    updateQuqery({ 'src_ip': e.target.value })
                }} placeholder={wordList["搜索攻击IP"][Context.lan]}></Input>
            </Space>
        },
        "url": {
            name: 'URL',
            node: <Space style={
                props.showAll?
                {
                width:'400px'
            }:{}}>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>URL</div>
                <Input onChange={(e: any) => {
                    updateQuqery({ 'url': e.target.value })
                }} placeholder={wordList["搜索攻击URL"][Context.lan]}></Input>
            </Space>
        },
        "path": {
            name: 'path',
            node: <Space style={
                props.showAll?
                {
                width:'400px'
            }:{}}> 
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>PATH</div>
                <Input onChange={(e: any) => {
                    updateQuqery({ 'path': e.target.value })
                }} placeholder={wordList["搜索攻击PATH"][Context.lan]}></Input>
            </Space>
        },
        /*"domain": {
            name: wordList["域名"][Context.lan],
            node: <Space>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["域名"][Context.lan]}</div>
                <Input placeholder={wordList["搜索域名"][Context.lan]}></Input>
            </Space>
        },
        "port": {
            name: wordList["端口"][Context.lan],
            node: <Space>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["端口"][Context.lan]}</div>
                <InputNumber placeholder={wordList["搜索攻击端口"][Context.lan]} style={{ borderRadius: '5px' }}></InputNumber>
            </Space>
        },*/
        "startTime": {
            name: wordList["开始时间"][Context.lan],
            node: <Space style={
                props.showAll?
                {
                width:'400px'
            }:{}}>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["开始时间"][Context.lan]}</div>
                <DatePicker placeholder={wordList["搜索攻击开始时间"][Context.lan]} style={{ borderRadius: '5px' }}
                    showTime={{ format: 'HH:mm' }}
                    format="YYYY-MM-DD HH:mm" onChange={(e: any, dateString) => {
                        updateQuqery({ 'start': e._d.getTime() })
                    }} onOk={() => { }} />
            </Space>
        },
        "endTime": {
            name: wordList["结束时间"][Context.lan],
            node: <Space style={
                props.showAll?
                {
                width:'400px'
            }:{}}>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["结束时间"][Context.lan]}</div>
                <DatePicker placeholder={wordList["搜索攻击开始时间"][Context.lan]} style={{ borderRadius: '5px' }}
                    showTime={{ format: 'HH:mm' }}
                    format="YYYY-MM-DD HH:mm" onChange={(e: any, dateString) => {
                        updateQuqery({ 'end': e._d.getTime() })
                    }}
                    onOk={() => { }} />
            </Space>
        },
        "type": {
            name: wordList["类型"][Context.lan],
            
            node: <Space style={ props.showAll?
                {
                width:'400px'
            }:{ width: '220px', marginLeft: '10px' }}>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999", whiteSpace: 'nowrap' }}>{wordList["类型"][Context.lan]}</div>
                <TreeSelect
                    {...{
                        treeData: [
                            {
                                title: wordList["全部"][Context.lan],
                                key: 'all',
                                value: "all",
                                children: Object.keys(AttackTypeRefer).filter((item: any) => item != 5).map((item: any) => {

                                    return {
                                        title: wordList[AttackTypeRefer[item]][Context.lan],
                                        key: item,
                                        value: item
                                    }
                                })

                            }
                        ],
                        maxTagCount: 1,
                        treeDefaultExpandedKeys: ['all'],
                        multiple: false,
                        value,
                        onChange,
                        treeCheckable: true,
                        showCheckedStrategy: TreeSelect.SHOW_PARENT,
                        placeholder: wordList["搜索攻击类型"][Context.lan],
                        style: {
                            width: '190px',
                            borderRadius: '5px'
                        },
                    }} />
                {/*<Select style={{
                    width: "200px",
                    borderRadius: '10px'
                }}
                    onChange={(e) => {
                        updateQuqery({ 'attack_type': e == 'all' ? '' : e })
                    }}
                    options={
                        [
                            {
                                label: "所有",
                                value: 'all'
                            },
                            ...Object.keys(AttackTypeRefer).map(item => {
                                return {
                                    label: AttackTypeRefer[item],
                                    key: item,
                                    value: item
                                }
                            })
                        ]
                    } defaultValue={"all"}></Select>*/}
            </Space>
        },
        /*"path": {
            name: wordList["路径"][Context.lan],
            node: <Space>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["路径"][Context.lan]}</div>
                <Input placeholder={wordList["搜索攻击路径"][Context.lan]}></Input>
            </Space>
        },*/
        "TraceID": {
            name: "TraceID",
            node: <Space style={
                props.showAll?
                {
                width:'400px'
            }:{}}>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>TraceID</div>
                <Input placeholder={wordList["搜索日志ID"][Context.lan]} onPressEnter={(e: any) => {
                    //props.searchTrace && props.searchTrace(e.target.value)
                    updateQuqery({ 'trace_id': e.target.value })
                }}></Input>
            </Space>
        },
        "res_status": {
            name: "状态码",
            node: <Space style={
                props.showAll?
                {
                width:'400px'
            }:{}}>
                <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>状态码</div>
                <Input onChange={(e: any) => {
                    updateQuqery({ 'res_status': e.target.value })
                }} placeholder={'搜索响应状态码'}></Input>
            </Space>

        }
    }
    const [showSearchType, setSearchType] = useState<string[]>([
        "action",
        "ip",
        //"domain",
        "type"
    ])
    const [logType, setLogType] = useState({

    })
    useEffect(() => {
        if (props.showAll) {
            setSearchType(Object.keys(logTypeRefer))
        }
    },[])
    return props.type == 'event'
        ? <Space>
            <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>IP</div>
            <Input style={{ width: '150px' }} placeholder={wordList["搜索攻击IP"][Context.lan]}></Input>
            <Divider type="vertical" style={{ marginLeft: 0, marginRight: 0 }}></Divider>
            <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["域名"][Context.lan]}</div>
            <Input style={{ width: '150px' }} placeholder={wordList["搜索域名"][Context.lan]}></Input>
            <Divider type="vertical" style={{ marginLeft: 0, marginRight: 0 }}></Divider>
            <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }} >{wordList["端口"][Context.lan]}</div>
            <InputNumber style={{ width: '150px', borderRadius: '5px' }} placeholder={wordList["搜索攻击端口"][Context.lan]}></InputNumber>
            <Divider type="vertical" style={{ marginLeft: 0, marginRight: 0 }}></Divider>
            <div style={{width:props.showAll?"60px":"auto", "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["攻击时间"][Context.lan]}</div>
            <DatePicker.RangePicker showTime style={{ borderRadius: '5px' }}></DatePicker.RangePicker>
        </Space>
        : <Space style={
            props.showAll
            ?{
                flexDirection:"column",
                gap:'20px'
            }
            :
            {
            flexWrap: 'wrap'
        }}>
            {
                showSearchType.map((item, index: number) => {
                    return <>
                        {logTypeRefer[item].node}
                        {
                            index < (showSearchType.length - 1) && !props.showAll
                                ? <Divider type="vertical"></Divider>
                                : null
                        }
                    </>
                })
            }
            {
                !props?.showAll
                    ? <Popover
                        title={wordList["筛选内容"][Context.lan]}
                        trigger={"click"}
                        open={openSelectType}
                        onOpenChange={(e) => {
                            setOpenSelectType(e)
                        }}
                        content={
                            <Tree
                                checkable
                                onCheck={(e: any) => {
                                    setSearchType(e)
                                }}

                                checkedKeys={showSearchType}
                                treeData={Object.keys(logTypeRefer).map(item => {
                                    return {
                                        title: logTypeRefer[item].name,
                                        key: item,
                                        value: item
                                    }
                                })}
                            />
                        }
                    >

                        <Button type="primary" style={{ marginLeft: '10px', borderRadius: '5px', background: "#cc1212" }}>{wordList["更多"][Context.lan]}</Button>
                    </Popover>
                    : null
            }

        </Space>
}
export default SearchBar