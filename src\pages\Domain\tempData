[[
        {
            type: "static",
            desc: "演示1",
            domainRule: ['*'],
            port: 8080,
            SSL: false,
            backend:'***********',
            backendPort:"8083",
            statistic: {
                requestion: 0,
                attack: 0
            },
            advancedProtection: {
                "CC": {
                    open: false,
                    waitRoom: {
                        open: false,
                        personNums: 1,
                        timeout: 1
                    },
                    frequency: {
                        type: 'custom',
                        times: false,
                        attack: false,
                        error: false
                    }
                },
                "BOT": {
                    open: true,
                    type: 1,
                    timeout: 1,
                    activeProduction: {
                        HTML: false,
                        JS: false,
                        WaterMark: false
                    },
                    reply: true
                },
                "AUTH": {
                    open: false,
                    source: ""
                }
            },
            serverType: 0
        },
        {
            type: "static",
            desc: "演示2",
            domainRule: ['*'],
            port: 8080,
            SSL: false,
            serverType: 1,
            statistic: {
                requestion: 0,
                attack: 0
            },
            advancedProtection: {
                "CC": {
                    open: true,
                    waitRoom: {
                        open: false,
                        personNums: 1,
                        timeout: 1
                    },
                    frequency: {
                        type: 'custom',
                        times: false,
                        attack: false,
                        error: false
                    }
                },
                "BOT": {
                    open: false,
                    type: 1,
                    timeout: 1,
                    activeProduction: {
                        HTML: false,
                        JS: false,
                        WaterMark: false
                    },
                    reply: true
                },
                "AUTH": {
                    open: false,
                    source: ""
                }
            },
        },
        {
            type: "static",
            desc: "演示3",
            backend:'***********',
            backendPort:"8083",
            domainRule: ['*'],
            port: 8080,
            SSL: true,
            advancedProtection: {
                "CC": {
                    open: false,
                    waitRoom: {
                        open: false,
                        personNums: 1,
                        timeout: 1
                    },
                    frequency: {
                        type: 'custom',
                        times: false,
                        attack: false,
                        error: false
                    }
                },
                "BOT": {
                    open: false,
                    type: 1,
                    timeout: 1,
                    activeProduction: {
                        HTML: false,
                        JS: false,
                        WaterMark: false
                    },
                    reply: true
                },
                "AUTH": {
                    open: true,
                    source: ""
                }
            },
            serverType: 2,
            statistic: {
                requestion: 0,
                attack: 0
            }
        }
],



    //siteDetail
    {
        name: "BugsGuard管理后台",
        createTime: Date.now(),
        listeningPort: 8000,
        ssl: true,
        serverType: 1,
        backendPort: 8080,
        domainRules: ['*', '*-test.com'],
        requestTimes: 16536,
        requestTimesBefore: 18536,
        interceptTimes: 165,
        interceptTimesBefore: 115,
        ccProtection: {
            open: false,
        },
        botProtection: {

            open: true,
        },
        authProtection: {

            open: true,
        },
        upStreamNodes: [{
            ip: "***********",
            port: "8083",
            status: 1
        }, {
            ip: "***********",
            port: "8083",
            status: 1
        }, {
            ip: "***********",
            port: "8083",
            status: 0
        }, {
            ip: "***********",
            port: "8083",
            status: 1
        }, {
            ip: "***********",
            port: "8083",
            status: 0
        }, {
            ip: "***********",
            port: "8083",
            status: 1
        }, {
            ip: "***********",
            port: "8083",
            status: 1
        }, {
            ip: "***********",
            port: "8083",
            status: 1
        }, {
            ip: "***********",
            port: "8083",
            status: 1
        }, {
            ip: "***********",
            port: "8083",
            status: 1
        }, {
            ip: "***********",
            port: "8083",
            status: 1
        }]
    },
    {
        'host': 'demo.waf-ce.chaitin.cn:10084',
        'sec-ch-ua': `"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"`,
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'upgrade-insecure-requests': 1,
        'user-agent': `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36`,
        'accept': `text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7`,
        'sec-fetch-site': 'same-site',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'referer': 'https://demo.waf-ce.chaitin.cn/',
        'accept-encoding': 'gzip, deflate, br, zstd',
        'accept-language': 'zh-CN,zh;q=0.9',
        'priority': 'u=0, i',
        'cookie': `_ga=GA1.1.587469816.1735467268; _ga_QQNNL4RHQF=GS1.1.1735525656.2.0.1735525656.0.0.0; _ga_9GZCPX5F2S=GS1.1.1735525686.1.1.1735526105.0.0.0; _ga_0XR7R2VXZF=GS1.1.1735525657.1.1.1735526113.0.0.0; sl-session=0uFjYphYc2eqoaaiJUpuiQ==`,
    
    }
]