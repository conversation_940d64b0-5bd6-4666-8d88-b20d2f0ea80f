body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

::-webkit-scrollbar-track {
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
  border-radius: 10px;
  background-color: transparent;
  /*rgba(255, 255, 255, 0.5);*/
}

::-webkit-scrollbar {
  /*border-radius: 50%;*/
  border-radius: 10px;
  width: 7px;
  height: 7px;
  background-color: transparent;
  /* #ddd;*/
}

::-webkit-scrollbar-thumb {
  border-radius: 20px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  /*background-color: rgba(255,255,255,0);*/
  background-color: transparent;
  /*#ddd;*/
  border-left: 1px solid rgba(255, 255, 255, 0);
  border-right: 1px solid rgba(255, 255, 255, 0);
}

.ant-select-selector {
  border-radius: 10px !important;
  border: 1px solid #eaeaea !important;
}

.ant-modal-footer {
  .ant-btn-primary {
    border-radius: 10px;
    background: #288df5;
    border: none
  }

  .ant-btn-default {
    border: none;
    color: #288df5;
  }
}

.ant-table {


  ::-webkit-scrollbar-track {
    /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.5);
  }

  ::-webkit-scrollbar {
    /*border-radius: 50%;*/
    border-radius: 10px;
    width: 7px;
    height: 7px;
    background-color: #ddd;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 20px;
    /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
    /*background-color: rgba(255,255,255,0);*/
    background-color: #ddd;
    border-left: 1px solid rgba(255, 255, 255, 0);
    border-right: 1px solid rgba(255, 255, 255, 0);
  }

  /*去除表格表头竖线*/
  .ant-table-thead {
    .ant-table-cell::before {
      content: '';
      display: none;
    }
  }

}

.ant-checkbox-checked {
  .ant-checkbox-inner {
    background-color: #cc1212 !important;
    border-color: #cc1212 !important;
  }
}

.ant-btn-primary {
  border-color: #cc1212 !important;
}

.ant-tree-checkbox-checked {
  .ant-tree-checkbox-inner {

    background-color: #cc1212 !important;
    border-color: #cc1212 !important;
  }
}

.ant-table-tbody {
  .ant-table-row:last-child {
    td {

      border-bottom: none !important;
    }
  }
}