import { useContext, useState } from "react"
import wordList from "../../../../../config/wordList"
import ProviderContext from "../../../../providerContext"
import EChartsReact from 'echarts-for-react'
import * as echarts from "echarts"
import "./index.less"
/**时段访问拦截分析 */
const VisitedLineChart = (props) => {
    const Context = useContext(ProviderContext)
    const [anlysiOptions, setAnalysiOptions] = useState({
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: [wordList['访问情况'][Context.lan], wordList['拦截情况'][Context.lan],],
            top: "87%",
        },
        grid: {
            top: '8%',
            left: '3%',
            right: '4%',
            bottom: '2%',
            containLabel: true
        },
        toolbox: {
            show: false,
            feature: {
                saveAsImage: {}
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            show: false,
            data:[]
        },
        yAxis: {
            type: 'value'
        },
        color: ['#f4424d', '#cc1212'],
        series: [
            {
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgb(244, 66, 77,1)'
                        },
                        {
                            offset: 1,
                            color: 'rgb(244, 66, 77,0.2)'
                        }
                    ])
                },
                name: wordList['访问情况'][Context.lan],
                type: 'line',
                smooth: true,
                stack: 'Total',
                data: [120, 132, 101, 134, 90, 230, 210]
            },
            {
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgb(50, 148, 242,1)'
                        },
                        {
                            offset: 1,
                            color: 'rgb(50, 148, 242,0.2)'
                        }
                    ])
                },
                name: wordList['拦截情况'][Context.lan],
                smooth: true,
                type: 'line',
                stack: 'Total',
                data: [220, 182, 191, 234, 290, 330, 310]
            },

        ]
    })
    return <div className="analysi-ip-chart">

        <div className="card-title">{wordList['拦截&访问分析'][Context.lan]}</div>

        <EChartsReact key={Context.lan} style={{ height: 'calc(100% - 40px)', flex: 1 }} option={anlysiOptions} notMerge={true} ></EChartsReact>
    </div>
}
export default VisitedLineChart