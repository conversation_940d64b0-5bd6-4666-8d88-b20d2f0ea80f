import { Tag, <PERSON><PERSON>, Card, <PERSON>Picker, Divider, Input, InputNumber, Popover, Radio, Space, Table, TimePicker, Spin, Modal, Collapse, Pagination } from "antd"
import "./index.less"
import { useContext, useEffect, useState } from "react"
import moment from "moment";
import config from "../../../config";
import wordList from "../../../config/wordList";
import ProviderContext from "../../providerContext";
import api from "../../../api";
import CustomTable from "../../../components/CustomTable";
import { CaretRightFilled } from "@ant-design/icons";
import type { ColumnsType } from 'antd/es/table';
import CollapsePanel from "antd/lib/collapse/CollapsePanel";
import LogDetail from "../Attack/components/LogDetail";
import CustomCard from "../../../components/CustomCard";
import BugsLinkButton from "../../../components/BugsLinkButton";
const LogAttacker = (props: any) => {
    const Context = useContext(ProviderContext)

    const [logData, setLogData] = useState([
      
    ])
    const [attackRecord, setAttackRecord] = useState<any>(
        {
            show: false,
            nowSite: "",
            data: [

            ]
        })
    const [loadingLog, setLoadingLog] = useState(false)
    const [detailLoading, setDetailLoading] = useState(false)
    const [detailPageInfo, setDetailPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const getDetail = (page = 1, pageSize = 10, options: any = {}) => {
        setDetailLoading(true)
        api.record.getRecordList({
            page,
            page_size: pageSize,
            sort: "-time",
            ...options
        })
            .then(res => {
                setDetailLoading(false)
                if (res?.data?.data) {
                    setAttackRecord({
                        show: true,
                        nowSite: options.site,
                        data: res.data.data.rows
                    })
                    setDetailPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    const logColumns:ColumnsType & any = [
        {
            title: wordList["源IP"][Context.lan],
            key: 'src_ip',
            dataIndex: 'src_ip',
            width: 150,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '30px',
                    display: 'flex',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <div>
                        {
                            record?.src_ip || '-'
                        }
                    </div>

                </div>
            }
        },
        {
            title: wordList["来源国家"][Context.lan],
            key: 'src_city',
            dataIndex: 'src_city',
            width: 150,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '30px',
                    display: 'flex',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    {<div

                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {record?.src_country || '内网IP'}



                        {record?.src_subdivision ? ('-' + (record?.src_subdivision || '内网IP')) : ""}
                    </div>}
                </div>
            }
        },
        {
            title:wordList["攻击次数"][Context.lan],
            dataIndex: 'count',
            key: 'count',
            width: 100,
            align:'center',
            render: (count, record) => {
                return <BugsLinkButton  onClick={() => {
                    getDetail(1, 10, {
                        src_ip: record.src_ip,
                        attack_type:5
                    })
                }}>{count}</BugsLinkButton>
            }
        },
        {
            title: wordList["最近攻击时间"][Context.lan],
            key: 'latest_time',
            dataIndex: 'latest_time',
            width: 100,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    gap: '5px',

                }}>
                    {
                        moment(record.latest_time || '').format('YYYY/MM/DD HH:mm:ss')
                    }
                </div>
            }
        },
    ]
    const [logpPageInfo, setLogPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const getLogRecord = (page = 1, pageSize = 10, options = {}) => {
        setLoadingLog(true)
        api.record.getLimitList({
            page,
            page_size: pageSize,
            sort: "-latest_time",
            ...options,
        })
            .then(res => {
                setLoadingLog(false)
                if (res?.data?.data) {
                    setLogData(res.data.data.rows)
                    setLogPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    useEffect(() => {
        getLogRecord()
    }, [])
    return <>
        <Modal
            title={ wordList["攻击记录"][Context.lan]}
            destroyOnClose
            open={attackRecord.show}
            width={1200}
            style={{
                maxWidth: "calc(100vw - 160px)"
            }}
            onCancel={() => {
                setAttackRecord({
                    ...attackRecord,
                    show: false
                })
            }}
            footer={
                <Pagination
                    size="small"
                    onChange={(page, pageSize) => {
                        getDetail(page, pageSize, {
                            site: attackRecord.nowSite
                        })
                    }}
                    showTotal={total => `共 ${total} 条数据`}
                    pageSize={
                        detailPageInfo.page_size
                    }
                    current={
                        detailPageInfo.page
                    }
                    total={
                        detailPageInfo.total
                    }
                ></Pagination>
            }
        >
            <div style={{
                maxHeight: "calc(100vh - 300px)",
                overflowY: "auto"
            }}>
                <Spin spinning={detailLoading}>
                    <Collapse
                        bordered={false}
                        defaultActiveKey={[(attackRecord?.data || [])[0]?.trace_id || '-']}
                        expandIcon={({ isActive }) => <CaretRightFilled rotate={isActive ? 90 : 0} />}
                        style={{ background: "#f5f5f5", padding: '10px', paddingTop: '20px' }}

                    >
                        {
                            attackRecord.data.map((item: any, index) => {
                                return <CollapsePanel header={moment(item.time).format("YYYY-MM-DD HH:mm:ss")} key={item.trace_id} className="site-collapse-custom-panel" style={{
                                    background: "white",
                                    marginBottom: '20px',
                                    boxShadow: "0 0 10px rgba(0, 0, 0, 0.05)",
                                    borderRadius: "10px",
                                    width: "calc(100% - 20px)",
                                    marginLeft: "10px"
                                }}>
                                    <LogDetail useContent={true} provideData={item}></LogDetail>
                                </CollapsePanel>
                            })

                        }
                    </Collapse>
                </Spin>
            </div>
        </Modal>
        <CustomCard title={
           wordList["监察者日志"][Context.lan]
        }

            extra={
                null
            }
            style={{
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: '100%',
            }}>
            <div
                className="search-bar"
                style={{
                    alignItems: 'center',
                    display: 'flex',
                    flexWrap: 'wrap',
                    width: '100%',
                    height: 'auto',
                    gap: '10px'
                }}>
                {/*<Radio.Group
                    style={{
                        flexShrink: 0,
                    }}
                    options={[
                        { label: wordList["攻击事件"][Context.lan], value: 'event' },
                        { label: wordList["攻击日志"][Context.lan], value: 'log' },
                    ]}
                    onChange={(e) => {
                        setFindTarget(e.target.value)
                    }}
                    value={findTarget}
                    optionType="button"
                    buttonStyle="solid"
                />
                <Divider type="vertical"></Divider>*/}
            </div>
            <Spin spinning={loadingLog}>
                <div
                    className="log-table-container">
                    <CustomTable columns={logColumns}
                        dataSource={logData}
                        useBorder={true}
                        pagination={{
                            pageSize: logpPageInfo.page_size,
                            current: logpPageInfo.page,
                            total: logpPageInfo.total,
                            showSizeChanger:true
                        }}

                        loading={false}
                        onChange={(e) => {

                            getLogRecord(e.current, e.pageSize, {})
                        }}
                        scroll={{ x: 'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
                        size={'small'}
                    ></CustomTable>

                </div>
            </Spin>

        </CustomCard>
    </>
}

export default LogAttacker