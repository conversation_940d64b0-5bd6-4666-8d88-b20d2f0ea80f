import { Button } from "antd"

import type { ButtonProps } from "antd/es/button"
const BugsLinkButton=(props:ButtonProps)=>{
    let propsStyle=props.style || {}
    let newProps={
        ...props
    }
    try{
        delete newProps.style
    }catch{}
    return <Button type="link" style={{
        borderRadius: '10px', color: "#cc1212", border: 'none',
        ...propsStyle
    }} {...newProps}></Button>
}
export default BugsLinkButton