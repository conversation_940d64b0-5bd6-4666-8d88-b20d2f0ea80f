import { Button, Card, Form, Input, Modal, Radio, Space, Table } from "antd"
import "./index.less"
import { useContext, useEffect, useState } from "react"
import ProviderContext from "../../../../providerContext"
import wordList from "../../../../../config/wordList"
import CustomTable from "../../../../../components/CustomTable"
import BugsLinkButton from "../../../../../components/BugsLinkButton"
const EditIpGroup = (props: any) => {
    const Context=useContext(ProviderContext)
    const [open, setOpen] = useState(true)
    const [form] = Form.useForm()
    const [selectType, setSelectType] = useState('text')
    const [data, setData] = useState(props.data)
    useEffect(() => {
        setSelectType(props?.data?.type)
    }, [])
    return <Modal
        footer={
            <Space>
                <BugsLinkButton>{wordList["取消"][Context.lan]}</BugsLinkButton>
                <Button type="primary" style={{ borderRadius: '10px', background: "#cc1212" }}>{wordList["保存"][Context.lan]}</Button>
            </Space>
        }
        open={open}
        title={props.type == 'add' ? wordList["添加IP组"][Context.lan] : wordList["编辑IP组"][Context.lan]}
        width={800}
        onCancel={() => {
            setOpen(false)
        }}
        afterClose={() => {
            props.close && props.close()
        }}
    >
        <Form form={form} layout="vertical">
            <Form.Item
                label={wordList["名称"][Context.lan]}
                name={"name"}

                initialValue={data?.name}
                rules={[{
                    required: true,
                    message: wordList["请输入IP组名称"][Context.lan]
                }]}
            >
                <Input style={{
                    width: "100%"
                }}></Input>
            </Form.Item>
            <Form.Item
                name={"type"}
                label={wordList["数据来源"][Context.lan]}
                initialValue={data?.type}
            >
                <Radio.Group
                    style={{
                        flexShrink: 0,
                    }}
                    options={[
                        { label: wordList["文本填写"][Context.lan], value: 'text' },
                        { label: wordList["在线订阅"][Context.lan], value: 'describe' },
                    ]}
                    onChange={(e) => {
                        setSelectType(e.target.value)
                    }}
                    optionType="button"
                    buttonStyle="solid"
                />
            </Form.Item>
            {
                selectType == 'describe'
                    ? <Form.Item
                        label={wordList["订阅地址"][Context.lan]}
                        name={'describe'}
                        initialValue={data?.describe}
                    >
                        <Input placeholder={wordList["请输入在线订阅地址"][Context.lan]} style={{ width: "100%" }}></Input>
                    </Form.Item>
                    : <Form.Item
                        label={wordList["内容"][Context.lan]}
                        name={'content'}
                        initialValue={data?.content}>
                        <Input.TextArea autoSize={
                            {
                                minRows: 5,
                                maxRows: 10
                            }
                        } placeholder={wordList["请输入IP内容，每行一个IP，'//'用于注释"][Context.lan]} style={{ width: "100%" }}></Input.TextArea>
                    </Form.Item>
            }



        </Form>
    </Modal>
}
const IPGroup = (props: any) => {
    const Context=useContext(ProviderContext)
    const [data, setData] = useState([
       
    ])
    const [editGroup, setEditGroup] = useState({
        open: false,
        data: {},
        type: 'edit'
    })
    const columns = [
        {
            title: wordList["名称"][Context.lan],
            dataIndex: "name",
            key: "name",
            width: 500
        },
        {
            title: wordList["内容"][Context.lan],
            dataIndex: "content",
            key: "content",
            width: 500,
            render: (content) => {
                let split = content.split("\n").map(ipRow => ipRow.replace(/\s/g, "")).filter(item => !item.startsWith("//"))
                split = split.filter(item => item.length > 0)
                let allNums = split.length
                if (allNums > 1) {
                    return <div>{split[0]}等共{allNums}个IP</div>
                }
                return split[0] || '-'
            }
        },
        {
            title: "",
            width: 30,
            key: "operation",
            dataIndex: 'operation',
            render: (_: any, record: any, index: number) => {

                return <Space>
                    <BugsLinkButton  onClick={() => {

                        let ipRecord = record
                        if (record.type == 'text') {
                            ipRecord.content = ipRecord.content.split("\n").map(ipRow => ipRow.replace(/\s/g, "")).join("\n")
                        }
                        setEditGroup({
                            type: 'edit',
                            data: ipRecord,
                            open: true
                        })
                    }}>{wordList["编辑"][Context.lan]}</BugsLinkButton>
                    <BugsLinkButton style={{ color: !record.describe ? "#999999" : "#cc1212" }} disabled={!record.describe}>{wordList["更新"][Context.lan]}</BugsLinkButton>
                    <BugsLinkButton  style={{ color: "red" }}>{wordList["删除"][Context.lan]}</BugsLinkButton>
                </Space>
            }
        }
    ]
    return <>
        {
            editGroup.open
                ? <EditIpGroup
                    type={
                        editGroup.type
                    }
                    data={
                        editGroup.data
                    }
                    close={() => {
                        setEditGroup({
                            ...editGroup,
                            open: false
                        })
                    }}></EditIpGroup>
                : null
        }
        <Card 
        className="common-ipgroup"
        title={
            <div style={{
                fontWeight: 'bold'
            }}>{wordList["IP组"][Context.lan]}
            </div>
        }
            extra={
                <Space>
                    <Button type="primary"
                        onClick={() => {
                            setEditGroup({
                                open: true,
                                data: {
                                    type: "text",
                                    name: "",
                                    content: ``,
                                    describe: ""
                                },
                                type: 'add'
                            })
                        }}
                        style={{
                            borderRadius: '10px',
                            background: '#cc1212',

                        }}>
                        {wordList["添加IP组"][Context.lan]}
                    </Button>
                </Space>
            }
            bordered={false}
            style={{
                border:'1px solid #ddd',
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: 'auto',
            }}
        >
            <CustomTable
                columns={columns}
                dataSource={data}
            ></CustomTable>
        </Card>
    </>
}
export default IPGroup