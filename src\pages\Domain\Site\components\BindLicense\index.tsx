
import { render } from "@testing-library/react"
import { <PERSON><PERSON>, Card, Divider, message, Modal, Popover, Select, Space, Table } from "antd"
import { useContext, useEffect, useState } from "react"
import "./index.less"
import type { ColumnsType } from 'antd/es/table';
import ProviderContext from "../../../../providerContext";
import wordList from "../../../../../config/wordList"
import CustomTable from "../../../../../components/CustomTable"
import BugsLinkButton from "../../../../../components/BugsLinkButton"
import api from "../../../../../api"
import { title } from "process"
import moment from "moment";
import BugsButton from "../../../../../components/BugsButton";
const BindLicense = (props:any) => {
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
        setSelectedRowKeys(newSelectedRowKeys);
    };
    const rowSelection: any = {
        type: 'radio',
        selectedRowKeys,
        onChange: onSelectChange,
    };
    const [open, setOpen] = useState(true)
    const Context = useContext(ProviderContext)
    const [pageInfo, setPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const columns: ColumnsType<any> = [
        {
            title: wordList["类型"][Context.lan],
            dataIndex: 'type',
            key: 'type',
            render: (type) => {
                return <div style={{
                    height: '30px',
                    lineHeight: '30px'
                }}>
                    {
                        {
                            1: wordList["申请免费证书"][Context.lan],
                            0: wordList["上传已有证书"][Context.lan]
                        }[type]
                    }
                </div>
            }
        },
        {
            title: wordList["域名"][Context.lan],
            key: 'domains',
            dataIndex: 'domains',
            width: 300,
            render: (_: any, record: any, index: any) => {

                return <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                }}>
                    <div
                        className="domain-column"
                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {
                            (record?.domains || []).length == 0 ?
                                "未配置匹配方式"
                                : (record.domains.length == 0 && record.domains[0] == '*'
                                    ? "通配所有域名" : (<Space>
                                        {
                                            record?.domains.map((item, index) => {
                                                return <>
                                                    {
                                                        index > 0
                                                            ? <Divider type="vertical"></Divider>
                                                            : null
                                                    }
                                                    {item}

                                                </>
                                            })
                                        }
                                    </Space>))
                        }
                    </div>
                </div>
            }
        },
        {
            title: "颁发机构",
            dataIndex: "issuer",
            key: "issuer",
            align: 'center'
        },
        {
            title: wordList["有效期至"][Context.lan],
            dataIndex: 'detail',
            key: 'detail',
            width: 200,
            align: 'center',
            render: (_: any, record: any) => {
                return moment(record?.not_after || '-').format("YYYY-MM-DD HH:mm:ss")
            }
        },
        {
            title: "关联站点",
            dataIndex: "related_sites",
            key: "related_sites",
            align: 'center',
            render: (sites) => {
                return (sites ?? []).join(" | ") || '-'
            }
        },
    ]
    const [openEdit, setOpenEdit] = useState({
        type: 'add',
        open: false
    })
    const [data, setData] = useState<any>([
        /*{
            type: "free",
            domainRule: ['*', 'www.test.moule.cn'],
            detail: {
                organization: '谋乐网络',
                timend: '2024-12-31'
            }
        },
        {
            type: "upload",
            domainRule: ['*', 'www.test.moule.cn'],
            detail: {
                organization: '谋乐网络',
                timend: '2024-12-31'
            }
        }*/
    ])
    const [getting, setGetting] = useState(false)
    const getList = (page = 1, pageSize = 10) => {
        setGetting(true)
        api.keyLicense.getLicense({
            pageSize,
            page
        })
            .then(res => {
                setGetting(false)
                if (res?.data?.data?.rows) {
                    if (res.data.data.rows.length == 0 && page > 1) {
                        getList(page - 1, pageSize)
                        return
                    }
                    setData(res.data.data.rows.map(item=>{
                        return {
                            key:item.id,
                            ...item
                        }
                    }))
                    setPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    useEffect(() => {
        getList()
    }, [])
    return <Modal
    onCancel={()=>{
        setOpen(false)
    }}
    width={1500}
    style={{
        maxWidth:"calc(100vw - 200px)"
    }}
    afterClose={()=>{
        props?.close && props.close()
    }}
        open={open}
        footer={
            <div style={{
                width: '100%',
                display: 'flex',
                justifyContent: "space-between",

            }}>
                <div></div>
                <Space>
                    <BugsLinkButton onClick={() => {
                        setOpen(false)
                    }}>取消</BugsLinkButton>
                    <BugsButton onClick={() => {
                        if(selectedRowKeys.length==0){
                            message.error("未选择证书")
                            return
                        }
                        Modal.confirm({
                            title:"操作确认",
                            content:"确认绑定此证书",
                            onOk:()=>{
                                props?.onBind && props.onBind(selectedRowKeys[0],()=>{
                                    message.success("绑定成功")
                                    setOpen(false)
                                })
                            }
                        })
                        
                    }}>保存</BugsButton>
                </Space>
            </div>
        }
        title="绑定证书"
    >

        <CustomTable
            columns={columns}
            dataSource={data}
            useBorder={true}
            loading={getting}
            rowSelection={rowSelection}
            onChange={(e) => {

                getList(e.current, e.pageSize)
            }}
            pagination={{
                pageSize: pageInfo.page_size,
                current: pageInfo.page,
                total: pageInfo.total,
                showSizeChanger: true
            }}
            scroll={{ x: data.length == 0 ? 0 : 'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
            size={'middle'}
        ></CustomTable>
    </Modal>
}
export default BindLicense