/**主页 */
import { Divider, Empty, message, Progress, Table, Tag } from "antd"
import ProviderContext from "../providerContext"
import wordList from "../../config/wordList"
import "./index.less"
import React, { useContext, useEffect, useMemo, useRef, useState } from "react"
import type { ColumnsType } from 'antd/es/table';
import LineChart from "./components/LineChart"
import ViewPageList from "../../modules/ViewPageList"
import TimeRangeSelector from "../../components/TimeRangeSelector"
import CustomTable from "../../components/CustomTable"
import EarthChart from "../../components/Earth3D"
import api from "../../api"
import AttackFrom from "./components/AttackFrom"

const { CheckableTag } = Tag;


const guestIcon = <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20867" width="64" height="64"><path d="M522.666667 53.333333a266.666667 266.666667 0 1 1-8.106667 533.205334l-2.56 0.128A309.333333 309.333333 0 0 0 202.666667 896a32 32 0 1 1-64 0 373.461333 373.461333 0 0 1 242.688-349.866667A266.666667 266.666667 0 0 1 522.666667 53.333333z m0 64a202.666667 202.666667 0 1 0 0 405.333334 202.666667 202.666667 0 0 0 0-405.333334zM896 864a32 32 0 0 1 4.352 63.701333L896 928h-213.333333a32 32 0 0 1-4.352-63.701333L682.666667 864h213.333333z" fill="#ffffff" p-id="20868"></path><path d="M682.666667 650.666667a32 32 0 0 1 31.701333 27.648l0.298667 4.352v213.333333a32 32 0 0 1-63.701334 4.352L650.666667 896v-213.333333a32 32 0 0 1 32-32z" fill="#ffffff" p-id="20869"></path></svg>
const viewIcon = <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="24637" width="256" height="256"><path d="M455.68 404.032l91.264 529.152s67.456-69.44 123.136-117.888L792.512 979.2c4.928 6.656 15.68 7.104 23.872 1.088l52.288-38.208c8.256-6.016 10.944-16.32 5.952-22.976L755.52 759.68c62.208-25.088 164.672-53.632 164.672-53.632L455.68 404.032zM308.352 648.384L172.48 747.712c-20.544 15.04-24.256 43.968-8 65.408 16.256 21.376 46.272 27.008 66.752 12.032l135.872-99.328c20.992-15.36 24.512-45.504 8.256-66.88-16.192-21.44-46.016-25.92-67.008-10.56z m641.344-409.408C933.44 217.6 904.064 212.8 882.624 228.48l-134.912 98.688c-21.44 15.68-25.152 44.672-8.896 66.048 16.256 21.376 46.272 27.008 67.712 11.328l134.912-98.688c21.44-15.68 24.512-45.504 8.256-66.88z m-630.4-102.144c-15.936-20.928-45.248-25.728-66.752-10.048-20.096 14.72-24.256 43.968-8.32 64.896L349.76 330.496c15.936 20.992 45.696 25.408 65.792 10.688 21.44-15.68 25.216-44.608 9.28-65.6L319.296 136.832zM585.792 301.76c26.176 4.224 50.24-13.376 53.632-39.232L660.608 94.72c3.392-25.792-14.976-49.984-41.536-54.656-26.176-4.224-50.24 13.376-53.632 39.168l-21.248 167.872c-3.264 25.856 15.104 49.984 41.6 54.656zM329.728 489.024c2.56-25.92-15.808-50.048-41.536-54.656L118.144 406.4c-27.072-3.584-50.688 13.696-53.632 39.232-3.904 26.944 14.464 51.072 41.536 54.656l170.048 27.968c25.728 4.48 49.408-12.8 53.632-39.232z" fill="#ffffff" p-id="24638"></path></svg>
const interceptIcon = <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="25761" width="256" height="256"><path d="M116.288 481.536V228.416c0-11.968 7.296-22.784 18.496-27.2L498.56 56.96a29.248 29.248 0 0 1 21.568 0l363.52 144.256c11.072 4.48 18.432 15.232 18.432 27.2v253.248q0 177.152-107.136 305.92Q695.04 907.648 518.4 966.4a29.312 29.312 0 0 1-18.496 0q-176.576-58.88-276.48-178.88-107.2-128.832-107.2-306.048z m58.496 0q0 156.096 93.632 268.608 86.72 104.128 240.832 157.696 154.048-53.568 240.704-157.696 93.568-112.512 93.568-268.48V248.256L509.44 115.584 174.72 248.32v233.216z" p-id="25762" fill="#ffffff"></path><path d="M363.328 531.968a9.408 9.408 0 0 0 7.68 14.848h89.6c5.248 0 9.408 4.224 9.408 9.408v156.864c0 9.152 11.776 12.864 17.088 5.44l167.936-235.072a9.408 9.408 0 0 0-7.68-14.848H557.696a9.408 9.408 0 0 1-9.408-9.408V302.336a9.408 9.408 0 0 0-17.024-5.44L363.328 531.968z" p-id="25763" fill="#ffffff"></path></svg>
interface DataType {
    rank: number;
    ip: string;
    times: number;
    placement: string;
}

function getTimeAndWeekday() {
    const date = new Date();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const weekday = days[date.getDay()];
    let month: any = date.getMonth() + 1
    if (month < 10) month = '0' + month
    let day: any = date.getDate()
    if (day < 10) {
        day = '0' + day
    }
    return {
        date: `${date.getFullYear()}-${month}-${day}`,
        time: `${hours}:${minutes}:${seconds}`,
        weekday
    };
}
let updateTimer: any = null
function getTimeRanges(start = "") {
    const now = new Date();
    // 近 24 小时的时间起点
    const last24HoursStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    // 近 7 天的时间起点
    const last7DaysStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    // 近 30 天的时间起点
    const last30DaysStart = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    if (!start) return ""
    return {
        _today: last24HoursStart,
        _7days: last7DaysStart,
        _30days: last30DaysStart
    }[start];
}
let resizeTimer:any=null
let reRenderTimer: any = null
let nowStart = "_today"
const HomePage = () => {
    const size={
        width:1920,
        height:911
    }
    const [scale,setScale]=useState(1)
    const Context = useContext(ProviderContext)
    const [nowFilter, setNowFilter] = useState({
        value: "_today",
        text: "今日"
    })
    const [attack, setAttack] = useState([])
    const [overView, stOverView] = useState({

        "attack": 0,
        "ban": 0,
        "request": 0

    })
    const getOverViewCount = (target = "") => {

        let options: any
        if (nowFilter.value == "_all") {
            options = {}
        }
        else {
            options = {
                start: getTimeRanges(target)
            }
        }
        api.overview.getOverviewCount(options)
            .then(res => {
                if (res?.data?.data) {
                    stOverView(res.data.data)
                }
            })

    }
    const getAttackCount = (target = "") => {
        let options: any
        if (nowFilter.value == "_all") {
            options = {}
        }
        else {
            options = {
                start: getTimeRanges(target)
            }
        }
        api.overview.getAttackCount(options)
            .then(res => {
                if (res?.data?.data?.rows) {
                    setAttack(res.data.data.rows)
                } else {
                    setAttack([])
                }
            })
    }
    useMemo(() => {
        nowStart = nowFilter.value
        getOverViewCount(nowStart)
        getAttackCount(nowStart)
    }, [nowFilter])
    useEffect(() => {

        nowStart = '_today'
        //getOverViewCount('_today')
        //getAttackCount('_today')
        reRenderTimer = setInterval(() => {
            getOverViewCount(nowStart)
            getAttackCount(nowStart)
        }, 5000)
        updateTimer = setInterval(() => {
            let node: any = document.getElementById("nowTime")
            if (node) {
                node.innerText = getTimeAndWeekday().time
            }
        }, 1000)
        const resizeHandle=()=>{
            clearTimeout(resizeTimer)
            resizeTimer=setTimeout(()=>{
                    
                let min=Math.min(document.body.offsetHeight/900,(document.body.offsetWidth-200)/1720)
                console.log(min,window.outerWidth / window.innerWidth)
                setScale(Math.floor(min * 1000) / 1000-0.03)
            },200)
        }
        resizeHandle()
        window.addEventListener("resize",resizeHandle)
        return () => {
            window.removeEventListener("resize",resizeHandle)
            clearInterval(updateTimer)
            clearInterval(reRenderTimer)
        }
    }, [])
    return <div className="home-page-container" style={{
       display:'flex',
       justifyContent:'center',
       alignItems:'center'
    }}>
        <div 
        id="scaleNode"
        style={{
            width: '1720px',
            height: "900px",
            transform:`scale(${scale}) `,
            transformOrigin:"center",
            position: 'relative',
            flexShrink:0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            background: "#eef1fa"
        }}>
            <div style={{
                width: '400px',
                padding: "20px",
                marginLeft: '2%',
                boxSizing: "border-box",
                height: '100%',
                display: 'flex',
                flexDirection: "column",
                position: "relative",
                zIndex: 3
            }}>

                <TimeRangeSelector
                    onChange={(data) => {
                        setNowFilter({
                            ...data
                        })
                    }}
                    extend={[{
                        key: "_all",
                        label: '全部'
                    }]}></TimeRangeSelector>
                <div className="left-bar-item-container">
                    {/**图标 */}
                    <div className="left-bar-item-icon-contianer" style={{ background: "#8675ff" }}>
                        <div className="left-bar-item-icon-inner">
                            {guestIcon}
                        </div>
                    </div>
                    {/**文本 */}
                    <div className="left-bar-item-text-container">
                        <div style={{ lineHeight: '20px', height: '20px', letterSpacing: '2px', whiteSpace: "nowrap" }}>{nowFilter.text ? (wordList[nowFilter.text] ? wordList[nowFilter.text][Context.lan] : '-') : '-'} {wordList['拦截监察数'][Context.lan]}</div>
                        <div style={{ lineHeight: '30px', fontWeight: 700, fontSize: "32px", height: '25px' }}>{overView.ban ?? '0'}</div>
                    </div>
                </div>



                <div className="left-bar-item-container">
                    {/**图标 */}
                    <div className="left-bar-item-icon-contianer" style={{ background: "#ff8f69" }}>
                        <div className="left-bar-item-icon-inner">
                            {interceptIcon}
                        </div>
                    </div>
                    {/**文本 */}
                    <div className="left-bar-item-text-container">

                        <div style={{ lineHeight: '20px', height: '20px', letterSpacing: '2px', whiteSpace: "nowrap" }}>{nowFilter.text ? (wordList[nowFilter.text] ? wordList[nowFilter.text][Context.lan] : '-') : '-'} {wordList['拦截攻击数'][Context.lan]}</div>
                        <div style={{ lineHeight: '30px', fontWeight: 700, fontSize: "32px", height: '25px' }}>{overView.attack ?? '0'}</div>




                    </div>
                </div>

                <div className="left-bar-item-container">
                    {/**图标 */}
                    <div className="left-bar-item-icon-contianer" style={{ background: "#66c8ff" }}>
                        <div className="left-bar-item-icon-inner">
                            {viewIcon}
                        </div>
                    </div>
                    {/**文本 */}
                    <div className="left-bar-item-text-container">
                        <div style={{ lineHeight: '20px', height: '20px', letterSpacing: '2px', whiteSpace: "nowrap" }}>{nowFilter.text ? (wordList[nowFilter.text] ? wordList[nowFilter.text][Context.lan] : '-') : '-'} {wordList['访问次数'][Context.lan]}</div>
                        <div style={{ lineHeight: '30px', fontWeight: 700, fontSize: "32px", height: '25px' }}>{overView.request ?? '0'}</div>
                    </div>
                </div>

                <div className="left-bar-attack-ip-container">
                    <div style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between',
                        height: '30px',
                        fontSize: '20px'
                    }}>
                        <div>

                            {wordList['监察攻击情况'][Context.lan]}
                        </div>
                        <div style={{
                            color: "#999",
                            fontSize: '16px',
                            lineHeight: '30px'
                        }}>
                            {nowFilter.text ? wordList[nowFilter.text][Context.lan] : '-'}
                        </div>
                    </div>
                    <div style={{
                        width: '100%',
                        height: 'calc(100% - 40px)',
                        overflowY: "auto"
                    }}>
                        {
                            attack.length == 0
                                ? <div style={{ height: '100%', width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={'暂无数据'} />
                                </div>
                                : null
                        }
                        {
                            attack.map((item: any, index) => {
                                return <div style={{
                                    width: '100%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    marginTop: '20px',
                                    gap: '10px',
                                    justifyContent: 'space-between'
                                }}>
                                    <div style={{
                                        width: "100%",
                                        height: '100%',
                                        display: 'flex',

                                        gap: '10px',
                                        alignItems: "center",
                                    }}>
                                        <div className={`left-bar-attack-ip-rank ${index < 4 ? ('left-bar-attack-ip-rank-' + (index + 1)) : 'left-bar-attack-ip-rank-other'}`}>
                                            <p className="left-bar-attack-ip-rank-number">{index + 1}</p>
                                        </div>
                                        <div className="left-bar-attack-ip">{item?.src_ip || '-'}</div>
                                    </div>
                                    <div style={{
                                        fontFamily: 'AlimamaShuHeiTi',
                                        fontSize: '14px',
                                        color: "#ff2156"
                                    }}>{item?.count || '-'}</div>
                                </div>
                            })
                        }
                    </div>
                </div>

            </div>
            <div style={{
                width: '350px',
                padding: "20px",
                marginRight: '2%',
                boxSizing: "border-box",
                height: 'calc(100% - 30px)',
                display: 'flex',
                flexDirection: "column",
                position: "relative",
                zIndex: 3
            }}>
                <div className="home-page-top-time-date">
                    <div id="nowDate" style={{ width: '100%', textAlign: 'right' }}>{getTimeAndWeekday().date}</div>
                    <div id="nowTime" style={{ width: '100%', textAlign: 'right', fontSize: "24px" }}>{getTimeAndWeekday().time}</div>
                </div>
                <AttackFrom></AttackFrom>
            </div>
            <div style={{
                position: 'absolute',
                left: "50%",
                transform: 'translateX(-60%)',
                top: '0',
                zIndex: 1,
                width: 'calc(100% - 600px)',
                height: "100%"
            }}>

                <EarthChart></EarthChart>
            </div>
        </div>
    </div>
}
export default HomePage