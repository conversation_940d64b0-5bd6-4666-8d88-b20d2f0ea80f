import { <PERSON><PERSON>, Card, Col, Form, Input, message, Modal, Row, Space, Table, Tooltip } from "antd"
import "./index.less"
import ProviderContext from "../../providerContext"
import wordList from "../../../config/wordList"
import { useContext, useEffect, useState } from "react"
import BugsButton from "../../../components/BugsButton"
import moment from "moment"
import config from "../../../config"
import { title } from "process"
import api from "../../../api"
import { DeleteOutlined, EditOutlined } from "@ant-design/icons"
import CustomTable from "../../../components/CustomTable"
import CustomCard from "../../../components/CustomCard"
import BugsLinkButton from "../../../components/BugsLinkButton"
import md5 from "js-md5"
let { account } = config
let { roleRefer } = account
const Account = () => {
    const Context = useContext(ProviderContext)
    const [nowEditRecord, setNowEditRecord] = useState({
        show: false,
        index: -1
    })
    const [resetPwd, setResetPwd] = useState({
        show: false,
        userid: ""
    })
    const [pageInfo, setPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })

    const accountColumns = [
        {
            title: wordList['用户ID'][Context.lan],
            width: 100,
            key: "id",
            dataIndex: "id",
        }, {
            title: wordList['用户名'][Context.lan],
            width: 200,
            key: "username",
            dataIndex: "username",
        }, {
            title: wordList['角色'][Context.lan],
            width: 100,
            key: "role",
            dataIndex: "role",
            render: (role) => {
                return roleRefer[role]
            }
        }, {
            width: 150,
            title: wordList['创建时间'][Context.lan],
            key: "created_at",
            dataIndex: "created_at",
            render: (created_at) => {
                return moment(created_at).format('YYYY/MM/DD HH:mm:ss')
            }
        }, {
            title: wordList['备注'][Context.lan],
            key: "remark",
            width: 150,
            dataIndex: "remark",
            render: (remark, record, index) => {
                return <div>
                    {remark || '-'}
                    <span style={{ cursor: 'pointer' }} onClick={() => {
                        editForm.resetFields()
                        editForm.setFieldsValue({
                            'remark': remark
                        })
                        setNowEditRecord({
                            show: true,
                            index: index
                        })

                    }}><EditOutlined style={{ color: "#cc1212", marginLeft: '10px' }}></EditOutlined></span>
                </div>
            }
        },
        {
            title: "",
            dataIndex: "operation",
            width: 50,
            key: "operation",
            render: (_: any, record: any, index: number) => {
                return <Space>
                    <BugsLinkButton onClick={() => {
                        setResetPwd({
                            show: true,
                            userid: record.id
                        })
                    }}>{wordList["重置密码"][Context.lan]}</BugsLinkButton>
                    <Tooltip title={wordList["删除用户"][Context.lan]}>
                        <DeleteOutlined style={{ color: "#f4424d", cursor: "pointer" }} onClick={() => {
                            Modal.confirm({
                                title: wordList["操作确认"][Context.lan],
                                content: wordList["确认删除用户"][Context.lan] + record.username,
                                onOk: () => {
                                    return new Promise<boolean>((resolve, reject) => {
                                        api.account.deleteUser(record.id)
                                            .then(res => {
                                                if (res?.data?.data) {
                                                    message.success(wordList["删除成功"][Context.lan])
                                                    getAccount(pageInfo.page, pageInfo.page_size)
                                                    resolve(true)
                                                    return
                                                }
                                                resolve(false)
                                            })
                                    })
                                }
                            })
                        }}></DeleteOutlined>
                    </Tooltip>
                </Space>
            }
        }
    ]
    const getAccount = (page = 1, pageSize = 10) => {
        api.account.getAccountList({
            page: page,
            page_size: pageSize,
            sort: 'id'
        })
            .then(res => {
                if (res?.data?.data) {
                    setData(res.data.data.rows)
                    setPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    const [data, setData] = useState<any>([])
    useEffect(() => {
        getAccount()
    }, [])
    const [editForm] = Form.useForm()
    const [resetPwdForm] = Form.useForm()
    const saveEditUser = () => {
        return new Promise<boolean>((resolve, reject) => {
            api.account.editUser(data[nowEditRecord.index].id, editForm.getFieldsValue())
                .then(res => {
                    if (res?.data?.data) {
                        message.success(wordList["成功"][Context.lan])
                        setData([
                            ...data.slice(0, nowEditRecord.index),
                            {
                                ...data[nowEditRecord.index],
                                ...editForm.getFieldsValue()
                            },
                            ...data.slice(nowEditRecord.index + 1),

                        ])
                        setNowEditRecord({
                            show: false,
                            index: -1
                        })
                        resolve(true)
                    } else {
                        resolve(false)
                    }
                })
        })
    }
    const [showAddAccount, setShowAddAcount] = useState(false)
    const [registerForm] = Form.useForm()
    const [registing, setRegisting] = useState(false)
    const confirmRegister = async () => {
        try {
            let res = await registerForm.validateFields()
            for (let key in res) {
                if (!res[key]) {
                    message.error(wordList["请填写信息"][Context.lan])
                    return
                }
            }
        } catch (e) {
            return
        }
        let { username, password, confirmPassword } = registerForm.getFieldsValue()
        if (password != confirmPassword) {
            message.error(wordList["两次输入的密码不一致"][Context.lan])
            return
        }
        setRegisting(true)
        api.account.register({
            username: username,
            password: md5(password),
            role: 101
        })
            .then(res => {
                setRegisting(false)
                if (res?.data?.data?.id) {
                    message.success(wordList["添加成功"][Context.lan])
                    setShowAddAcount(false)
                    getAccount()
                }
            })
    }
    return <CustomCard
        title={wordList["账号管理"][Context.lan]}
        extra={
            data.length<3
            ?
            <BugsButton
                style={{ background: "#cc1212" }}
                onClick={() => {
                    setShowAddAcount(true)
                }}>
                {wordList["添加账号"][Context.lan]}
            </BugsButton>
            :null
        }
        style={{
            borderRadius: '10px',
            width: '100%',
            paddingLeft: 0,
            paddingRight: 0,
            height: 'calc(100% - 20px)',
        }}
    >
        <Modal
            className="add-account-modal"
            open={showAddAccount}
            title={wordList["添加账号"][Context.lan]}
            cancelButtonProps={{
                style: {
                    color: "#cc1212"
                }
            }}
            okText={wordList["提交"][Context.lan]}
            okButtonProps={{
                style: {
                    background: "#cc1212"
                }
            }}
            onOk={() => { confirmRegister() }}
            confirmLoading={registing}
            onCancel={() => {
                setShowAddAcount(false)
            }}
            width={600}

        >
            <div style={{
                display: "flex",
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                <Form
                    form={registerForm}
                    style={{
                        marginTop: '20px',
                        width: '100%'
                    }}
                >
                    <Form.Item
                        label={wordList["用户名"][Context.lan]}
                        name="username"
                        required
                    >
                        <Input style={{
                            width: '100%'
                        }}></Input>
                    </Form.Item>

                    <Form.Item
                        label={wordList["密码"][Context.lan]}
                        name="password"
                        required
                    >
                        <Input.Password style={{
                            width: '100%',
                            borderRadius: '5px'
                        }}></Input.Password>
                    </Form.Item>
                    <Form.Item
                        label={wordList["确认密码"][Context.lan]}
                        name="confirmPassword"
                        required
                    >
                        <Input.Password style={{
                            width: '100%',
                            borderRadius: '5px'
                        }}></Input.Password>
                    </Form.Item>
                </Form>

            </div>
        </Modal>
        <Modal
            open={
                nowEditRecord.show
            }
            title={wordList["编辑备注"][Context.lan]}
            onCancel={() => {
                setNowEditRecord({
                    show: false,
                    index: -1
                })
            }}
            cancelButtonProps={{
                style: {
                    color: "#cc1212"
                }
            }}
            okButtonProps={{
                style: {
                    background: "#cc1212"
                }
            }}
            onOk={saveEditUser}
        >
            <Form form={editForm}>
                <Form.Item
                    label={wordList["用户备注"][Context.lan]}
                    name={"remark"}
                >
                    <Input style={{
                        width: '100%'
                    }} placeholder={wordList["请输入用户备注"][Context.lan]}></Input>
                </Form.Item>
            </Form>
        </Modal>

        <Modal
            open={
                resetPwd.show
            }
            title={wordList["重置密码"][Context.lan]}
            onCancel={() => {
                setResetPwd({
                    show: false,
                    userid: ''
                })
            }}
            cancelButtonProps={{
                style:{
                    border:'none',
                    color:"#cc1212"
                }
            }}
            okButtonProps={{
                style:{
                    background:"#cc1212",
                    border:'none'
                }
            }}
            onOk={() => {
                return new Promise<boolean>((resolve, reject) => {
                    let { password } = resetPwdForm.getFieldsValue()
                    if (password.length < 8) {
                        message.error(wordList["密码不能少于8位"][Context.lan])
                        return
                    }
                    api.account.resetPassword({
                        id: resetPwd.userid,
                        password: md5(password)
                    })
                        .then(res => {
                            if (res?.data?.data) {
                                message.success(wordList["重置成功"][Context.lan])
                                setResetPwd({
                                    ...resetPwd,
                                    show: false
                                })
                                resolve(true)
                            } else {
                                resolve(false)
                            }
                        })
                })

            }}
        >
            <Form form={resetPwdForm}>
                <Form.Item
                    label={wordList["重置密码"][Context.lan]}
                    name={"password"}
                    required
                >
                    <Input style={{
                        width: '100%'
                    }} placeholder={wordList["请输入重置后的密码"][Context.lan]}></Input>
                </Form.Item>
            </Form>
        </Modal>

        {/*<div style={{
            width: '100%',
            display: 'flex',
            height: 'auto'
        }}>
            <Input style={{ width: '300px' }} placeholder="输入搜索内容"></Input>
            <BugsButton style={{ marginLeft: '10px',background:"#cc1212" }}>搜索</BugsButton>
        </div>*/}
        <div
            className="account-list-container"
            style={{
                height: "calc(100vh - 255px)",
                width: '100%',
                overflowY: "auto",
                marginTop: '20px',
                position: 'relative'
            }}>
            <CustomTable

                key={Context.lan}
                useBorder={true}
                scroll={{ x: 'max-content' }}
                dataSource={data}
                size="small"
                columns={accountColumns}></CustomTable>
        </div>


    </CustomCard>
}
export default Account