import { Card, Checkbox } from "antd"

import ProviderContext from "../../../../providerContext"
import wordList from "../../../../../config/wordList"
import { useContext } from "react"
const SharePlan = (props: any) => {
    const Context = useContext(ProviderContext)
    return <Card title={
        <div style={{
            fontWeight: 'bold',
        }}>
            {wordList["威胁情报共享计划"][Context.lan]}
        </div>
    }

        bordered={false}
        style={{
            marginTop: '20px',
            border: '1px solid #ddd',
            borderRadius: '10px',
            width: '100%',
            paddingLeft: 0,
            paddingRight: 0,
            height: 'auto',
        }}
    >
        <Checkbox>
            {wordList["共享恶意IP信息"][Context.lan]}</Checkbox>
        <div style={{
            marginTop: '20px',
            marginBottom: '20px',
            width: "100%",
            padding: '10px',
            borderRadius: '10px',
            background: "#fafafa",
            minHeight: '50px',
            height:'auto',
            lineHeight: '30px'
        }}>
            {wordList["加入后将共享攻击 IP 信息到社区，并可使用 IP 组 “谋乐恶意IP情报”，内容为社区共享的最具威胁的攻击 IP，每日自动更新。共享内容为设备每日检测到的公网攻击 IP、攻击次数、攻击类型。"][Context.lan]}

        </div>
        <Checkbox>{wordList["共享浏览器指纹信息"][Context.lan]}</Checkbox>
        <div style={{
            marginTop: '20px',
            marginBottom: '20px',
            width: "100%",
            padding: '10px',
            borderRadius: '10px',
            background: "#fafafa",
            minHeight: '50px',
            height:'auto',
            lineHeight: '30px'
        }}>
            {wordList["开启此选项后，谋乐WAF将在攻击拦截页面中通过 js 获取攻击者的浏览器指纹信息。此信息将用于计算攻击者画像，提升威胁情报的数据准确性。"][Context.lan]}

        </div>
    </Card>
}
export default SharePlan