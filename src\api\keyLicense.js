import ajax from "../tools/ajax";
const getLicense=(data)=>ajax(`/api/v1/cert`,data,"GET") //获取分页证书
const createLicense=(data)=>ajax(`/api/v1/cert`,data,'POST') //创建证书
const deleteLicense=(id)=>ajax(`/api/v1/cert/${id}`,{},'DELETE') //删除证书

const getLicenseById=(id)=>ajax(`/api/v1/cert/${id}`,{},'GET') //id获取证书

const editLicense=(id,data)=>ajax(`/api/v1/cert/${id}`,data,'PATCH') //修改证书
export default {
    getLicense,
    createLicense,
    deleteLicense,
    getLicenseById,
    editLicense
}