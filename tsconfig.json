{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "suppressImplicitAnyIndexErrors": true, "noImplicitAny": false, "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "ignoreDeprecations": "5.0"}, "include": ["src"]}