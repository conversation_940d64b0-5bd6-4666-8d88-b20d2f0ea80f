/*.line-point-container::before{
    content: '';
    width: 10px;
    position: absolute;
    left: 3px;
    top: 0;
    height: 10px;
    filter: blur(10px);
    background-color: black;
}*/
.line-point-container {
    overflow: hidden;
    border-radius: 50%;
    box-sizing: border-box;
    transform: scale(0.5) translateX(-50%) translateY(-50%);
    transform-origin: -5px -5px;
    background-color: rgba(255, 0, 0, 0);
    opacity: 0.5;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;

}

/* 定义波纹通用样式 */
.ripple {
    position: absolute;
    border-radius: 50%;
    border: 5px solid rgba(255,0,0,0.5);
    opacity: 0;
    animation: ripple-animation 3s infinite;
}
/* 定义最大波纹样式 */
.ripple.large {
    width: 80px;
    height: 80px;
}
/* 定义中等波纹样式 */
.ripple.medium {
    width: 60px;
    height: 60px;
    animation-delay: 1s;
}
/* 定义最小波纹样式 */
.ripple.small {
    width: 40px;
    height: 40px;
    animation-delay: 1.5s;
}
/* 定义波纹动画 */
@keyframes ripple-animation {
    0% {
        transform: scale(0.3);
        opacity: 1;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}