import { Tag, <PERSON><PERSON>, Card, DatePicker, Divider, Input, InputNumber, Popover, Radio, Space, Table, TimePicker, Switch, Select } from "antd"
import "./index.less"
import { useContext, useState } from "react"
import moment from "moment";
import { render } from "@testing-library/react";
import wordList from "../../../config/wordList";
import ProviderContext from "../../providerContext";
import CustomTable from "../../../components/CustomTable";
import CustomCard from "../../../components/CustomCard";
const ProtectionFrequency = (props: any) => {
    const Context = useContext(ProviderContext)
    const [data, setData] = useState({
        frequencyType: "custom",
        times: {
            use: false,
            time: -1,
            action: 0
        },
        attack: {
            use: false,
            time: -1,
            action: 0
        },
        error: {
            use: false,
            time: -1,
            action: 0
        },
    })


   
    return <>
        <div style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            gap: '10px'
        }}>

            <CustomCard
                title={wordList["监察阻断设置"][Context.lan]}
                extra={
                    null
                }
                style={{
                    borderRadius: '10px',
                    width: '100%',
                    paddingLeft: 0,
                    paddingRight: 0,
                    height: '100%'
                }}>
                <div style={{
                    width: '100%',
                    height: "100%",
                    boxSizing: 'border-box'
                }}>
                    <div className="fre-protection-custom">
                        <div className="fre-protection-custom-item">
                            <div className="fre-protection-item-title">{wordList["高频访问限制"][Context.lan]}</div>
                            <Switch onChange={(e) => {
                               
                                setData({
                                    ...data,
                                    times: {
                                        ...data.times,
                                        use: e
                                    }
                                })
                            }}></Switch>
                        </div>
                        {

                            data.times.use ?
                                <>
                                    <Space style={{
                                        gap: '15px'
                                    }}>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">
                                                {wordList["经过时间"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                        </div>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">

                                                {wordList["请求次数达到"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                        </div>

                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">

                                                {wordList["处理方式"][Context.lan]}
                                            </div>
                                            <Select
                                                defaultValue="bot"
                                                style={{ width: '100%' }}
                                                onChange={() => { }}
                                                options={[
                                                    {
                                                        value: 'bot',
                                                        label: wordList["人机验证"][Context.lan],
                                                    },
                                                    {
                                                        value: 'ban',
                                                        label: wordList["封禁"][Context.lan],
                                                    },
                                                ]}
                                            />
                                        </div>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">
                                                {data.times.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                        </div>
                                    </Space>
                                    <Divider orientation="left" style={{ marginTop: '5px', marginBottom: '5px', fontSize: '12px', color: "#999999" }}>{wordList["排除 Content-Type"][Context.lan]}</Divider>
                                    <Select
                                        size="large"
                                        mode="tags"
                                        style={{ width: '100%', borderRadius: '10px' }}
                                        placeholder="Tags Mode"
                                        onChange={(e) => {
                                           
                                        }}
                                        options={[]}
                                    />
                                </>

                                : null
                        }
                    </div>
                    <div className="fre-protection-custom">
                        <div className="fre-protection-custom-item">
                            <div className="fre-protection-item-title">{wordList["高频攻击限制"][Context.lan]}</div>
                            <Switch onChange={(e) => {
                               
                                setData({
                                    ...data,
                                    attack: {
                                        ...data.attack,
                                        use: e
                                    }
                                })
                            }}></Switch>
                        </div>
                        {

                            data.attack.use ?
                                <>
                                    <Space style={{
                                        gap: '15px'
                                    }}>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">
                                                {wordList["经过时间"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                        </div>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">

                                                {wordList["攻击阻断次数达到"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                        </div>

                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">

                                                {wordList["处理方式"][Context.lan]}
                                            </div>
                                            <Select
                                                defaultValue="bot"
                                                style={{ width: '100%' }}
                                                onChange={() => { }}
                                                options={[
                                                    {
                                                        value: 'bot',
                                                        label: wordList["人机验证"][Context.lan],
                                                    },
                                                    {
                                                        value: 'ban',
                                                        label: wordList["封禁"][Context.lan],
                                                    },
                                                ]}
                                            />
                                        </div>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">
                                                {data.times.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                        </div>
                                    </Space>
                                </>

                                : null
                        }
                    </div>
                    <div className="fre-protection-custom">
                        <div className="fre-protection-custom-item">
                            <div className="fre-protection-item-title">{wordList["高频错误限制"][Context.lan]}</div>
                            <Switch onChange={(e) => {
                               
                                setData({
                                    ...data,
                                    error: {
                                        ...data.error,
                                        use: e
                                    }
                                })
                            }}></Switch>
                        </div>
                        {

                            data.error.use ?
                                <>
                                    <Divider orientation="left"
                                        style={{ marginTop: '5px', marginBottom: '5px', fontSize: '12px', color: "#999999" }}>
                                        {wordList["状态码"][Context.lan]}
                                    </Divider>
                                    <Select
                                        size="large"
                                        mode="tags"
                                        style={{ width: '100%', borderRadius: '10px' }}
                                        placeholder="Tags Mode"
                                        onChange={(e) => {
                                           
                                        }}
                                        options={[]}
                                    />
                                    <Space style={{
                                        gap: '15px'
                                    }}>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">
                                                {wordList["经过时间"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                        </div>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">

                                                {wordList["错误次数达到"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                        </div>

                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">

                                                {wordList["处理方式"][Context.lan]}
                                            </div>
                                            <Select
                                                defaultValue="bot"
                                                style={{ width: '100%' }}
                                                onChange={() => { }}
                                                options={[
                                                    {
                                                        value: 'bot',
                                                        label: wordList["人机验证"][Context.lan],
                                                    },
                                                    {
                                                        value: 'ban',
                                                        label: wordList["封禁"][Context.lan],
                                                    },
                                                ]}
                                            />
                                        </div>
                                        <div className="fre-action-card">
                                            <div className="fre-action-card-title">
                                                {data.times.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                            </div>
                                            <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                        </div>
                                    </Space>
                                </>

                                : null
                        }
                    </div>
                </div>


            </CustomCard>
        </div>
    </>
}
export default ProtectionFrequency
