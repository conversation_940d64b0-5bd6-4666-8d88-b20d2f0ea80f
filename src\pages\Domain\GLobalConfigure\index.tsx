import { Button, Card, Checkbox, Divider, Input, InputNumber, Select, Space } from "antd"
import { useContext, useState } from "react"
import "./index.less"
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons"
import wordList from "../../../config/wordList"
import ProviderContext from "../../providerContext"
import BugsLinkButton from "../../../components/BugsLinkButton"
const GlobalConfigure = (props: any) => {
    const Context=useContext(ProviderContext)
    const [nowIpSource, setIpSource] = useState("fromSource")
    const IpSource = [
        {
            value: "fromSource",
            label: wordList["从网络连接中获取"][Context.lan]
        },
        {
            value: "proxy-level-1",
            label: wordList["取 X-Forwarded-For 中上一级代理的地址"][Context.lan]
        },
        {
            value: "proxy-level-2",
            label: wordList["取 X-Forwarded-For 中上上一级代理的地址"][Context.lan]
        },
        {
            value: "proxy-level-3",
            label: wordList["取 X-Forwarded-For 中上上上一级代理的地址"][Context.lan]
        },
        {
            value: "httpHeader",
            label: wordList["从 HTTP Header 中获取"][Context.lan]
        },
    ]
    return <Card
        title={wordList["全局配置"][Context.lan]}
        bordered
        className="global-configure"
        style={{
            borderRadius: '10px',
            width: '100%',
            paddingLeft: 0,
            paddingRight: 0,
            height: '100%',
            overflowY: 'auto'
        }}>
        <Card title={wordList["源 IP 获取方式"][Context.lan]}
            style={{
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: 'auto',
                minHeight: '100px',
                overflowY: 'auto'
            }}>
            <Select
                defaultValue={'fromSource'}
                style={{ width: '500px' }} options={IpSource} onChange={(e) => {
                   

                    setIpSource(e)
                }}></Select>
            <br />
            {
                nowIpSource == 'httpHeader'
                    ? <Space style={{
                        marginTop: '30px'
                    }}>
                        <div style={{ width: '100px' }}>Header:</div>
                        <Input
                            defaultValue={"X-Real-IP"}
                            style={{
                                width: '400px'
                            }}></Input>
                    </Space>
                    : null
            }
        </Card>
        <Card title={wordList["其他高级配置"][Context.lan]}
            style={{
                borderRadius: '10px',
                width: '100%',
                marginTop: '20px',
                paddingLeft: 0,
                paddingRight: 0,
                height: 'auto',
                minHeight: '100px',
                overflowY: 'auto'
            }}>
            <Checkbox> {wordList["监听 IPv6 地址"][Context.lan]}</Checkbox>
            <Divider></Divider>
            <Checkbox> {wordList["启用 HTTP/1.0"][Context.lan]}</Checkbox>
            <Divider></Divider>
            <Checkbox>  {wordList["启用 HTTP/2"][Context.lan]}</Checkbox>
            <Divider></Divider>
            <Checkbox>{wordList["HTTP 自动跳转到 HTTPS"][Context.lan]} </Checkbox>
            <Divider></Divider>
            <Checkbox> {wordList["启用 HSTS"][Context.lan]}</Checkbox>
            <Divider></Divider>
            <Checkbox> {wordList["代理时修改请求中的 Host 头"][Context.lan]}</Checkbox>
            <Divider></Divider>
            <Checkbox> {wordList["为上游服务器传递 X-Forwarded-Host、 X-Forwarded-Proto"][Context.lan]}</Checkbox>
            <Divider></Divider>
            <Checkbox> {wordList["清空并重写 X-Forwarded-For"][Context.lan]} </Checkbox>
            <Divider></Divider>
            <Checkbox> {wordList["支持 Gzip 压缩"][Context.lan]}</Checkbox>
            <Divider></Divider>
            <Checkbox> {wordList["支持 Brotli 压缩"][Context.lan]}</Checkbox>
            <Divider></Divider>
            <Checkbox> {wordList["站点不存在时返回内置证书"][Context.lan]}</Checkbox>
        </Card>
        <Card title={wordList["SSL合规配置"][Context.lan]}
            style={{
                borderRadius: '10px',
                width: '100%',
                marginTop: '20px',
                paddingLeft: 0,
                paddingRight: 0,
                height: 'auto',
                minHeight: '100px',
                overflowY: 'auto'
            }}>
            <Space>
                <div style={{
                    width: '100px'
                }}>{wordList["SSL协议版本"][Context.lan]}</div>
                <Select
                    size="large"
                    mode="tags"
                    style={{ width: '400px', borderRadius: '10px' }}
                    placeholder="Tags Mode"
                    onChange={(e) => {
                       
                    }}
                    options={[]}
                />
            </Space>
            <br />
            <Space style={{
                marginTop: '20px'
            }}>
                <div style={{
                    width: '100px'
                }}>SSL Chipers</div>
                <Input style={{
                    width: '400px'
                }}></Input>
            </Space>
        </Card>
        <Card title={wordList["HTTP Header 操作"][Context.lan]}
            style={{
                borderRadius: '10px',
                width: '100%',
                marginTop: '20px',
                paddingLeft: 0,
                paddingRight: 0,
                height: 'auto',
                minHeight: '100px',
                overflowY: 'auto'
            }}>
            <Space style={{
                gap: '30px'
            }}>
                <div className="configure-action-card">
                    <div className="configure-action-card-title">
                        {wordList["操作对象"][Context.lan]}
                    </div>
                    <Select
                        defaultValue="request"
                        style={{ width: '100%' }}
                        onChange={() => { }}
                        options={[
                            {
                                value: 'request',
                                label: wordList["请求头"][Context.lan],
                            },
                            {
                                value: 'response',
                                label: wordList["响应头"][Context.lan],
                            },
                        ]}
                    />
                </div>
                <div className="configure-action-card">
                    <div className="configure-action-card-title">
                        {wordList["操作"][Context.lan]}
                    </div>
                    <Select
                        defaultValue="set"
                        style={{ width: '100%' }}
                        onChange={() => { }}
                        options={[
                            {
                                value: 'set',
                                label: wordList["设置"][Context.lan] ,
                               
                            },
                            {
                                value: 'remove',
                                label: wordList["删除"][Context.lan],
                            },
                        ]}
                    />
                </div>

                <div className="configure-action-card">
                    <div className="configure-action-card-title">
                        KEY
                    </div>
                    <Input style={{ width: '100%' }}></Input>
                </div>
                <div className="configure-action-card">
                    <div className="configure-action-card-title">
                        Value
                    </div>
                    <Input style={{ width: '100%' }}></Input>
                </div>
                <BugsLinkButton  icon={<DeleteOutlined></DeleteOutlined>}></BugsLinkButton>
            </Space>
            <br />
            <BugsLinkButton 
                style={{ marginTop: '20px' }} icon={<PlusOutlined></PlusOutlined>}>{wordList["添加一项Header操作"][Context.lan]}</BugsLinkButton>
        </Card>
    </Card>
}
export default GlobalConfigure