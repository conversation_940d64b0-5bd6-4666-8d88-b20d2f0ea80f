import "./index.less"
import { <PERSON>, <PERSON><PERSON>, Card, DatePicker, Divider, Input, InputNumber, Popover, Radio, Space, Table, TimePicker, Select, Modal, Switch, Popconfirm, message, Spin } from "antd"
import React, { useContext, useEffect, useState } from "react"
import moment from "moment";
import { render } from "@testing-library/react";
import CustomeRulesModules from "../../../modules/CustomRules";
import type { ColumnsType } from 'antd/es/table';
import ProviderContext from "../../providerContext";
import wordList from "../../../config/wordList";
import config from "../../../config";
import api from "../../../api";
import CustomTable from "../../../components/CustomTable";
import CustomCard from "../../../components/CustomCard";
import BugsLinkButton from "../../../components/BugsLinkButton";
let { ruleTypes, ruleTypeRefer } = config
const CustomeRules = (props: any) => {
    const Context = useContext(ProviderContext)
    const [openEdit, setOpenEdit] = useState({
        show: false,
        data: {},
        type: 'edit'
    })
    const [ruleData, setRuleData] = useState<any>([
        /*{
            status: 0,
            type: "white",
            name: "搜索引擎爬虫白名单",
            isOnline: 0,
            touchToday: 3,
            updateTime: Date.now()
        },
        {
            status: 1,
            type: "black",
            name: "搜索引擎爬虫白名单",
            isOnline: 1,
            touchToday: 1,
            updateTime: Date.now()
        }*/
    ])
    const [pageInfo, setPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const [getting, setGetting] = useState(false)
    const getRules = (page = 1, pageSize = 10) => {
        setGetting(true)
        api.rules.getRules({
            page: page,
            pageSize: pageSize,
            sort: 'id'
        })
            .then(res => {
                setGetting(false)
                if (res?.data?.data) {
                    setRuleData(res.data.data.rows)
                    setPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    useEffect(() => {
        getRules()
    }, [])
    const columns: ColumnsType<any> = [
        {
            title: "ID",
            width: 30,
            dataIndex: "id",
            key: 'id'
        },
        {
            title: wordList["名称"][Context.lan],
            key: 'name',
            dataIndex: "name",
            width: 300,
            render: (_: any, record: any) => {
                return <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                    <div>{record.name}</div>
                    {
                        record.isOnline ?
                            <Tag color="geekblue" style={{ borderRadius: '10px', height: '25px', lineHeight: '23px' }}>{wordList["在线规则"][Context.lan]}</Tag>
                            : null
                    }
                </div>
            }
        },
        {
            title: wordList["状态"][Context.lan],
            key: 'status',
            dataIndex: 'status',
            width: 100,

            align: 'center',
            render: (_: any, record: any, index: any) => {
                let style: React.CSSProperties = {
                    width: '50px',
                    height: '25px',
                    lineHeight: '25px',
                    borderRadius: '5px',
                    textAlign: 'center'
                }


                return <Popconfirm title={wordList[`确认${record.enabled ? '关闭' : '开启'}此规则`][Context.lan]}

                    onConfirm={() => {
                        setGetting(true)
                        api.rules.editRules(record.id, {
                            ...record,
                            enabled: !record.enabled
                        })
                            .then(res => {
                                setGetting(false)
                                if (res?.data?.data) {
                                    message.success(wordList["修改成功"][Context.lan])
                                    setRuleData([
                                        ...ruleData.slice(0, index),
                                        {
                                            ...ruleData[index],
                                            enabled: !record.enabled
                                        },
                                        ...ruleData.slice(index + 1)
                                    ])
                                }
                            })
                    }}
                >
                    <Switch checked={record.enabled} checkedChildren={wordList["已启用"][Context.lan]} unCheckedChildren={wordList["未启用"][Context.lan]}></Switch>
                </Popconfirm>
                /*!record.enabled
                ? <Tag style={style} color="#f4424d" >{ wordList["禁用"][Context.lan]}</Tag> 
                : <Tag style={style} color="#cc1212">{ wordList["启用"][Context.lan]}</Tag>*/
            }
        }, {
            title: wordList["类型"][Context.lan],
            key: 'type',
            dataIndex: "type",
            width: 80,
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    display: "flex",
                    alignItems: 'center',
                    gap: '5px'
                }}>
                    {ruleTypeRefer[record.action].icon}
                    {wordList[ruleTypeRefer[record.action].name][Context.lan]}
                </div>
            }
        },
        {
            title: wordList["日志记录"][Context.lan],
            key: "log",
            dataIndex: "log",
            width: 100,
            align: 'center',
            render: (touchToday, record, index) => {
                return <Popconfirm title={wordList[`确认${record.log ? '关闭' : '开启'}日志记录`][Context.lan]}

                    onConfirm={() => {
                        setGetting(true)
                        api.rules.editRules(record.id, {
                            ...record,
                            log: !record.log
                        })
                            .then(res => {
                                setGetting(false)
                                if (res?.data?.data) {
                                    message.success(wordList["修改成功"][Context.lan])
                                    setRuleData([
                                        ...ruleData.slice(0, index),
                                        {
                                            ...ruleData[index],
                                            log: !record.log
                                        },
                                        ...ruleData.slice(index + 1)
                                    ])
                                }
                            })
                    }}
                >
                    <Switch checked={record.log} checkedChildren={wordList["已启用"][Context.lan]} unCheckedChildren={wordList["未启用"][Context.lan]}></Switch>
                </Popconfirm>
            },
        },
        {
            title: wordList["匹配规则"][Context.lan],
            key: "pattern",
            dataIndex: "pattern",
            width: 100,
            align: 'center',
            render: (touchToday, record) => {
                return <div>{record.pattern.length}{wordList["条规则"][Context.lan]}</div>
            }
        },
        {
            title: wordList["更新时间"][Context.lan],
            key: "updateTime",
            align: 'center',
            dataIndex: "updateTime",
            width: 200,
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',


                }}>
                    {

                        moment(record?.updated_at || '').format('YYYY/MM/DD HH:mm:ss')
                        //moment(record.updateTime).format('YYYY/MM/DD HH:mm:ss')
                    }
                </div>
            }
        },
        {
            title: wordList["创建时间"][Context.lan],
            key: "updateTime",
            align: 'center',
            dataIndex: "updateTime",
            width: 200,
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',


                }}>
                    {
                        moment(record?.updated_at || '').format('YYYY/MM/DD HH:mm:ss')
                        //moment(record.updateTime).format('YYYY/MM/DD HH:mm:ss')
                    }
                </div>
            }
        },
        {
            title: '',
            dataIndex: 'opetation',
            width: 60,
            align: 'center',
            render: (_, record) => {
                let allowColor="#cc1212"
                let nowAllowColor="#999"
                return <Popover 
                    
                    trigger={"hover"}
                    content={
                        <div style={{
                            width: '100px'
                        }}>
                            <BugsLinkButton
                                onClick={() => {
                                    setOpenEdit({
                                        data: record,
                                        show: true,
                                        type: 'edit'
                                    })
                                }}
                                style={{
                                    color: "#cc1212",
                                    border:'none'
                                }}>{wordList["编辑"][Context.lan]}</BugsLinkButton>
                            <BugsLinkButton
                                onClick={() => {
                                    Modal.confirm({
                                        title: "操作确认",
                                        content: "确认删除此规则",
                                        onOk: () => {
                                            return new Promise<boolean>((resolve, reject) => {

                                                api.rules.deleteRules(record.id)
                                                    .then(res => {
                                                        if (res?.data?.data) {
                                                            message.success("删除成功")
                                                            resolve(true)
                                                            getRules(pageInfo.page, pageInfo.page_size)
                                                            return
                                                        }
                                                        resolve(false)
                                                    })
                                            })
                                        }
                                    })
                                }}
                                style={{
                                    color: "red",
                                    border:"none"
                                }}>
                                {wordList["删除"][Context.lan]}
                            </BugsLinkButton>
                        </div>
                    }>
                    <BugsLinkButton
                        
                        disabled={record?.builtin}
                        icon={
                            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="64884" width="24" height="24">
                                <path d="M512 42.666667a469.333333 469.333333 0 1 0 469.333333 469.333333A469.333333 469.333333 0 0 0 512 42.666667z m0 864a394.666667 394.666667 0 1 1 394.666667-394.666667 395.146667 395.146667 0 0 1-394.666667 394.666667z" p-id="64885" fill={record?.builtin?nowAllowColor:allowColor}></path>
                                <path d="M304.906667 512m-66.666667 0a66.666667 66.666667 0 1 0 133.333333 0 66.666667 66.666667 0 1 0-133.333333 0Z" p-id="64886"  fill={record?.builtin?nowAllowColor:allowColor}></path>
                                <path d="M512 512m-66.666667 0a66.666667 66.666667 0 1 0 133.333334 0 66.666667 66.666667 0 1 0-133.333334 0Z" p-id="64887"  fill={record?.builtin?nowAllowColor:allowColor}></path>
                                <path d="M719.093333 512m-66.666666 0a66.666667 66.666667 0 1 0 133.333333 0 66.666667 66.666667 0 1 0-133.333333 0Z" p-id="64888"  fill={record?.builtin?nowAllowColor:allowColor}></path></svg>
                        }></BugsLinkButton>
                </Popover>
            }
        }
    ]

    return <>
        <Modal
            open={openEdit.show}
            width={1200}
            title={wordList["自定义规则"][Context.lan]}
            destroyOnClose
            onCancel={() => {
                setOpenEdit({
                    ...openEdit,
                    show: false
                })
            }}
            footer={null}
        >
            <CustomeRulesModules onClose={(update = false) => {
                setOpenEdit({
                    ...openEdit,
                    show: false
                })
                if (update) {
                    getRules(pageInfo.page, pageInfo.page_size)
                }
            }} data={openEdit.data} type={openEdit.type}></CustomeRulesModules>
        </Modal>
        <CustomCard 
        style={{
            borderRadius: '10px',
            width: '100%',
            paddingLeft: 0,
            paddingRight: 0,
            height: '100%',
        }}
        title={
            <div style={{
                alignItems: 'center',
                display: 'flex',
                flexWrap: 'wrap',
                width: '100%',
                justifyContent: 'space-between',
                height: 'auto',
                gap: '10px'
            }}>
                <Space>
                    {wordList["自定义规则"][Context.lan]}
                    {/*<Select
                        defaultValue={'all'}
                        style={{
                            width: '200px'
                        }}
                        options={ruleTypes.map(item => {
                            return {
                                ...item,
                                label: wordList[item.label][Context.lan]
                            }
                        })}></Select>*/}
                </Space>

                <Space>
                    <Button type="primary"
                        onClick={() => {
                            setOpenEdit({
                                ...openEdit,
                                show: true,
                                type: "add"
                            })
                        }}
                        style={{ borderRadius: '10px', background: "#cc1212", border: 'none' }}>{wordList["添加规则"][Context.lan]}</Button>
                </Space>
            </div>
        }
            extra={
                null
            }
        >
            <div style={{
                width: '100%',
                height: "100%",
                paddingLeft: '20px',
                paddingRight: '20px',
                boxSizing: 'border-box'
            }}>
                <Spin spinning={getting}>
                    <CustomTable columns={columns}
                        useBorder={true}
                        dataSource={ruleData}
                        loading={false}
                        pagination={{
                            pageSize: pageInfo.page_size,
                            current: pageInfo.page,
                            total: pageInfo.total,
                            showSizeChanger: true
                        }}
                        onChange={(e) => {

                            getRules(e.current,e.pageSize)
                         }}
                        scroll={{ x: 'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
                        size={'middle'}
                    ></CustomTable>
                </Spin>




            </div>
        </CustomCard>
    </>
}
export default CustomeRules