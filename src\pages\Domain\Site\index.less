.domain-container {
    .ant-select-selector {
        border-radius: 10px;
    }

    .ant-table-cell {
        padding-top: 3px !important;
        padding-bottom: 3px !important;
    }

    width: 100%;
    height: 100%;

    .ant-card-body {
        padding-left: 0;
        padding-right: 0;
    }

    .ant-table-thead {
        .ant-table-cell {
            padding-top: 5px !important;
            padding-bottom: 5px !important;
        }

    }

   


}

.page-ip-list {
    width: 70%;
    height: 100%;
    border-radius: 10px;
    background-color: white;
    padding: 10px;
    box-sizing: border-box;
    .ant-table {
        border: none;
    }
}


.page-card-title {
    width: auto;
    height: 30px;
    line-height: 30px;
    position: relative;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 10px;
}

.page-card-title::before {
    content: '';
    position: absolute;
    left: -10px;
    z-index: 2;
    height: 20px;
    background-color: #cc1212;
    top: 5px;
    width: 5px;
    border-radius: 10px;
}

.analysi-table-container {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    position: relative;
    .ant-table-thead {
        position: sticky;
        top: 0;
        z-index: 2;
        background-color: white;
    }

    .ant-table-container {

        overflow-y: auto;
    }

    div {
        height: 100% !important;

        table {
            height: 100% !important;
        }
    }
}
.analysi-time-selector{
    border-radius: 10px;
    border:1px solid #ddd;
    .top-time-selector{
        height: 40px;
        background-color: transparent;
    }
}
.domain-type-selector {
    width: 100%;
    height: 38px;
    background: white;
    border-radius: 10px;
    display: flex;
    padding-left: 10px;
    box-sizing: border-box;
    justify-content: space-between;
    .ant-tabs-tab {
        .ant-tabs-tab-btn {
            color: #999 !important;
        }
    }

    .ant-tabs-tab-active {
        .ant-tabs-tab-btn {
            color: #cc1212 !important;
        }
    }

    .ant-tabs-ink-bar {
        background-color: #cc1212;
    }
}

.domain-list-container {
    .ant-table-cell {
        padding-top: 10px !important;
        padding-bottom: 10px !important;
    }
}
.analysi-card-container{
    margin-top: 20px;
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto
}
.analysi-card-container-layout{
    width: 100%;
    height: 50vh;
    display: flex;
    gap: 20px;
    .analysi-ip-chart{
        width: 70%;
        height: 100%;
    }

}

@media screen and (max-width: 1020px) {
    .analysi-card-container{
        .analysi-card-container-layout{
            height: auto;
            flex-direction: column;
            .page-ip-list{
                width: 100%;
            }
            .analysi-ip-chart{
                height: 300px;
                width: 100%;
            }
        }
    }
}
.analysi-overview-container{

    width: 100%;
    height: 300px;
    display: flex;
    gap: 20px;
    margin-top: 30px;
    .response-pie-chart{
        width: 50%;
        height: 100%;
    }
}
@media screen and (max-width: 820px) {
    .analysi-overview-container{
        height: auto;
        flex-direction: column;
        .home-page-domain-list{
            height: 300px;
            width: 100%;
        }
        .response-pie-chart{
            width: 100%;
            height: 300px;
        }
    }
}