import { Select } from "antd"
import { useState } from "react"

/**IP组选择器*/
const IPGroup=(props:any)=>{
    const [ipGrpoup,setIpGroup]=useState([{
        label:"谋乐IP组1",
        value:"moule-ipgroup-1"
    },{
        label:"谋乐IP组2",
        value:"moule-ipgroup-2"
    },{
        label:"谋乐IP组3",
        value:"moule-ipgroup-3"
    },{
        label:"谋乐IP组4",
        value:"moule-ipgroup-4"
    }])
    return <Select
        style={{
            maxWidth: '100%',flex:1,
            ...(props?.style || {})
        }}
        options={ipGrpoup}
    ></Select>
}
export default IPGroup