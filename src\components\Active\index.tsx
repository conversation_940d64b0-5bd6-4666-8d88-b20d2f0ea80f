import { Descriptions, Input, message, Modal, Spin } from "antd"
import { useEffect, useState } from "react"
import api from "../../api"
import BugsButton from "../BugsButton"
import { CopyOutlined } from "@ant-design/icons"
import moment from "moment"

const Active = (props: any) => {
    const [show, setShow] = useState(true)
    const [license_data, setLicenseData] = useState("")
    const [fingerprint, setFinger] = useState("")
    const [loading, setLoading] = useState(false)
    const [isError, setIsError] = useState(false)
    const [setting, setSetting] = useState(false)
    const [licneseInfo, setLicenseInfo] = useState<any>({})
    useEffect(() => {
        setLoading(true)
        api.license.getLicense()
            .then(res => {
                setIsError(false)
                setLoading(false)
                if (res?.data?.data?.fingerprint) {
                    setLicenseInfo(res.data.data)
                    setFinger(res.data.data.fingerprint)
                }
            })
            .catch(err => {
                setLoading(false)
                setIsError(true)
                message.error("无法获取设备License状态")
            })
    }, [])
    const active = () => {
        if (!license_data) {
            message.error("请输入License")
            return
        }
        setSetting(true)
        api.license.activeLicense({
            license_data
        })
            .then(res => {
                setSetting(false)
                if (res?.data) {
                    if (res.data?.code === 0) {
                        message.success("续期成功")
                        try {

                            window.location.reload();
                        } catch {
                            window.location.href = "/"
                        }
                    } else {
                        message.error(res.data.message)
                    }
                }
            })

    }
    return <Modal
        open={show}
        title="系统续期"
        width={600}
        footer={null}
        onCancel={() => {
            setShow(false)
        }}
        afterClose={() => {
            props?.close && props.close()
        }}
    >
        <Spin spinning={loading}>
            <div style={{
                height: '400px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
                gap: '20px'
            }}>
                {
                    isError
                        ? <div style={{
                            fontSize: '24px',
                            fontWeight: "bold",
                            fontFamily: 'AlimamaShuHeiTi'
                        }}>无法连接服务器，获取设备License状态异常</div>
                        : <>
                            <div style={{ color: "#999" }}>请向平台客服提供以下设备数据进行License生成</div>
                            <div style={{}}>
                                <span style={{ color: "#cc1212" }}>

                                    {
                                        fingerprint || '-'
                                    }
                                </span><span
                                    style={{
                                        cursor: "pointer"
                                    }}
                                    onClick={async () => {
                                        if (!fingerprint) return
                                        try {
                                            await navigator.clipboard.writeText(fingerprint);
                                            message.success("已复制")
                                        } catch (error) {
                                            console.log(error)
                                            message.error("无法写入剪贴板,请自行复制")
                                        }
                                    }}
                                > <CopyOutlined></CopyOutlined></span>
                            </div>


                            <Input.TextArea

                                size="large"
                                value={license_data}
                                onChange={(e) => {
                                    setLicenseData(e.target.value)
                                }}
                                placeholder="请输入License"
                                rows={5}
                                style={{
                                    resize: 'none',
                                    borderRadius: '10px !important',
                                    background: "#fafafa",
                                    overflowY: 'auto',
                                    padding: '10px',
                                    boxSizing: "border-box",
                                    width: "520px"
                                }}></Input.TextArea>

                            <div style={{
                                fontSize: "16px",
                                textAlign: 'left',
                                width: 'calc(100% - 20px)',
                                marginLeft: '10px',
                                fontFamily: 'AlimamaShuHeiTi'
                            }}>当前License信息</div>
                            <Descriptions
                            bordered
                            style={{
                                marginLeft:'10px',
                                width:'calc(100% - 20px)'
                            }}
                                column={6}
                             labelStyle={{
                                width: "200px"
                            }}>
                                <Descriptions.Item label="可添加站点" span={6}>{licneseInfo?.max_site_count || 0}</Descriptions.Item>
                                {
                                    licneseInfo?.type == "subscription"
                                        ? <Descriptions.Item label="订阅有效期"  span={6}>{
                                            moment(licneseInfo?.subscription_start || "").format("YYYY-MM-DD")
                                            +
                                            " 至 "
                                            +
                                            moment(licneseInfo?.subscription_end || "").format("YYYY-MM-DD")
                                        }</Descriptions.Item>
                                        : <Descriptions.Item label="试用有效期"  span={6}>{
                                            moment(licneseInfo?.trial_start || "").format("YYYY-MM-DD")
                                            +
                                            " 至 "
                                            +
                                            moment(licneseInfo?.trial_end || "").format("YYYY-MM-DD")
                                        }</Descriptions.Item>
                                }

                            </Descriptions>
                            <BugsButton size="large"
                                style={{ borderRadius: '5px', width: '300px' }} onClick={() => {
                                    active()
                                }}>续期</BugsButton>

                        </>
                }
            </div>
        </Spin>
    </Modal>
}
export default Active