const CracoAntDesignPlugin = require('craco-antd');

module.exports = {
    webpack: {
        configure: {
            ignoreWarnings: [
                function ignoreSourcemapsLoaderWarnings(warning) {
                    return (
                      warning.module &&
                      warning.module.resource.includes("node_modules") &&
                      warning.details &&
                      warning.details.includes("source-map-loader")
                    );
                },
            ],
        },
    },
    plugins: [
        {
            plugin: CracoAntDesignPlugin,
            options: {
                customizeTheme: {
                    '@primary-color': '#296FD1'
                }
            }
        }
    ]
};