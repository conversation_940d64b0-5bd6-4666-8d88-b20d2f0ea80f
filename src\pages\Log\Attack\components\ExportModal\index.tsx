import { Button, InputNumber, message, Modal, Radio, Spin } from "antd"
import { useEffect, useState } from "react"
import SearchBar from "../SearchBar"
import BugsButton from "../../../../../components/BugsButton"
import api from "../../../../../api"
import moment from "moment"

const ExportModal = (props) => {
    const [show, setShow] = useState(true)
    const [searchQuery, setSearchQuery] = useState({})
    const [loading, setShoaLoading] = useState(false)
    let { current, pageSize, total, query, nowPageSize } = props
    const [nowLength, setNowLength] = useState()
    const [pageInfo, setPageInfo] = useState({
        page: 1,
        page_size: 10
    })
    useEffect(() => {
        setNowLength(props.nowPageSize || 0)
        setPageInfo({
            page: current,
            page_size: pageSize
        })
    }, [])
    const [nowSelect, setNowSelect] = useState("current")
    const [startPage, setStartPage] = useState<any>("")
    const [endPage, setEndPage] = useState<any>("")
    const [error,setError]=useState(false)
    const confirmExport = () => {
        if(error){
            message.error("无法导出，页面参数有误")
            return
        }
        setShoaLoading(true)
        let headers = {};
        const auth = localStorage.getItem("AUTH")
        if (auth) {
            headers = {
                authorization: 'Bearer ' + auth
            };
        }
        fetch(api.record.exportLog(), {
            method: 'POST',
            headers: {

                ...headers
            },
            body: JSON.stringify({
                // 这里是你要发送的请求体数据
                ...query,
                ...pageInfo,
                sort:"-time",

            })
        })
            .then(response => {
                // 检查响应是否成功
                if (!response.ok) {
                    setShoaLoading(false)
                    throw new Error('Network response was not ok');
                }
                const contentDisposition = response.headers['content-disposition'] + '.csv';
                let fileName = moment(Date.now()).format("YYYY-MM-DD") + '_日志报告.csv'; // 默认文件名

                if (contentDisposition) {
                    const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (fileNameMatch && fileNameMatch.length > 1) {
                        fileName = fileNameMatch[1];
                    }
                }
                // 获取Blob数据
                return response.blob().then(blob => {
                    message.success("导出成功")
                    // 创建一个URL对象
                    const url = window.URL.createObjectURL(blob);
                    // 创建一个<a>元素
                    const a = document.createElement('a');
                    a.href = url;
                    // 设置文件名
                    a.download = fileName;
                    // 触发点击事件
                    a.click();
                    // 释放URL对象
                    window.URL.revokeObjectURL(url);

                    setShoaLoading(false)
                });
            })

            .catch(error => {

                setShoaLoading(false)
                console.error('导出失败', error);
            });
    }
    return <Modal
        footer={null}
        open={show}
        title="日志导出"
        width={430}
        style={{
            maxWidth: 'calc(100vw - 200px)'
        }}
        onCancel={() => {
            setShow(false)
        }}
        afterClose={() => {
            props.close && props.close()
        }}
    >
        <Spin spinning={loading}>
            <div style={{
                fontSize: '18px',
                lineHeight: "40px",
                height: '40px'
            }}>请选择导出选项</div>
            <div style={{
                marginTop: '20px'
            }}></div>
            {/*<SearchBar
                showAll={true}
                searchQuery={(searchData) => {
                    setSearchQuery(searchData)
                }} type={'log'} searchTrace={(traceId) => { }}></SearchBar>*/}
            <Radio.Group defaultValue="current"
                value={nowSelect}
                buttonStyle="solid"
                onChange={(e) => {
                    setNowSelect(e.target.value)
                    if (e.target.value == 'current') {
                        setNowLength(nowPageSize)
                        setPageInfo({
                            page: current,
                            page_size: pageSize
                        })
                    } else if (e.target.value == 'toThis') {
                        let prePage = current - 1
                        if (prePage < 0) prePage = 0
                        setNowLength(prePage * pageSize + nowPageSize)
                        setPageInfo({
                            page: 1,
                            page_size: prePage * pageSize + nowPageSize
                        })
                    } else {
                        setNowLength(total)
                        setPageInfo({
                            page: 1,
                            page_size: total
                        })
                    }
                }}
            >
                <Radio.Button value="current">仅当前页</Radio.Button>
                <Radio.Button value="toThis">首页至当前页</Radio.Button>
                <Radio.Button value="all">全部</Radio.Button>
            </Radio.Group>
            <div style={{
                marginTop: '20px'
            }}>选中数据{nowLength}条</div>
            <div style={{
                width: '100%',
                marginTop: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
            }}>
                <BugsButton
                    onClick={confirmExport}
                    style={{

                    }}>确认导出</BugsButton>
            </div>
        </Spin>
    </Modal>
}
export default ExportModal