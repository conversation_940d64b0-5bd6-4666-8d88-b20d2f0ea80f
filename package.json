{"name": "bug-suguard", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.7.0", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@jiaminghi/data-view-react": "^1.2.5", "@jspreadsheet/parser": "^2.4.3", "@taoqf/quill-image-resize-module": "^3.0.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.39", "@types/react": "^18.0.12", "@types/react-dom": "^18.0.5", "@uiw/codemirror-theme-github": "^4.23.7", "@uiw/react-codemirror": "^4.23.7", "antd": "^4.24.13", "antd-img-crop": "^4.13.0", "axios": "^0.27.2", "codemirror": "^6.0.1", "craco-antd": "^2.0.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "echarts-gl": "^2.0.9", "excel-parser": "^0.2.2", "exceljs": "^4.3.0", "github-markdown-css": "^5.5.0", "jexcel": "^4.6.1", "js-base64": "^3.7.2", "js-md5": "^0.7.3", "jspreadsheet": "^9.1.5", "jspreadsheet-ce": "^4.11.6", "jszip": "^3.10.1", "markdown-navbar": "^1.4.3", "node-exp-parser": "^1.0.36", "prismjs": "^1.29.0", "quill-image-drop-module": "^1.0.3", "raw-loader": "^4.0.2", "rc-color-picker": "^1.2.6", "react": "^18.1.0", "react-codemirror2": "^8.0.0", "react-color": "^2.19.3", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.1.0", "react-markdown": "^9.0.1", "react-quill": "^2.0.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-simple-code-editor": "^0.14.1", "relation-graph": "^2.0.26", "remark-gfm": "^4.0.0", "typescript": "^4.7.3", "validator": "^13.7.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "xss": "^1.0.15"}, "scripts": {"start": "set PORT=3001 & craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "options": {"allowedHosts": ["localhost", ".localhost"], "proxy": "http://**********:8000"}, "devDependencies": {"@antv/g6": "^4.6.15", "@craco/craco": "^6.4.3", "@types/js-md5": "^0.4.3", "@types/jspreadsheet-ce": "^4.7.3", "@types/markdown-navbar": "^1.4.4", "@types/validator": "^13.7.10", "braft-editor": "^2.3.9", "craco-less": "^2.0.0", "insert-css": "^2.0.0", "markdown-loader": "^8.0.0"}}