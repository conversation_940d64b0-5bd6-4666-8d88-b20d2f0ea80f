.ccprotection-container{
    width: 100%;
    height:auto;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
    .ant-radio-group{
        border-radius: 10px !important;
        overflow: hidden;
        border: 1px solid  #ddd;
    }
    .ant-radio-button-wrapper{
        border: none;
    }
    .ant-radio-button-checked{
        background-color: #cc1212;
    }
}
.ccprotection-container-item{
    
    width: calc(100% - 20px);
    border-radius: 10px;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    padding: 10px;
    position: relative;
    margin-left: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
    box-sizing: border-box;

}
.ccprotection-container-item::after{
    content: '';
    position: absolute;
    left: -10px;
    height: 100%;
    width: 5px;
    border-radius: 10px;
    background-color: #cc1212;
}
.ccprotection-container-item-title{
    width: auto;
    font-size: 14px;

}
.cc-protection-custom{
    .ant-select-selector{
        border-radius: 10px !important;
        border: 1px solid #ddd !important;
    }
    margin-top: 20px;
    height: auto;
    width: calc(100% - 20px);
    margin-left: 10px;
    border-radius: 10px;
    margin-top: 20px;
    background-color: #fafafa;
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 10px;
    .ant-input-number-group{
        border-radius: 10px !important;
        border: 1px solid #ddd;
        overflow: hidden;
        .ant-input-number{
            background-color: #fafafa !important;
            border: none;
        }
        .ant-input-number-group-addon{
            border: none;
        }
    }

}
.ccprotection-custom-item{
    width: calc(100% - 20px);
    border-radius: 10px;
    border-radius: 10px;
    padding: 10px;
    position: relative;
    margin-left: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
    box-sizing: border-box;
}
.cc-action-card{
    width: 100%;
    min-width: 200px;
    background-color: white;
    height: 80px;
    border-radius: 10px;
    box-shadow: 0 0 5px rgba(0,0,0,0.1);
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.cc-action-card-title{
    width: 100%;
    font-size: 12px;
    height: 30px;
    line-height: 30px;
    text-align: left;
    color: #999999;
}