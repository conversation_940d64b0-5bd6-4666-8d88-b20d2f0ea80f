# 目录说明
所有调整只会涉及到src目录里面内容的修改

## api目录
存放所有接口，接口按照功能划分写在各自的js文件中，然后在index.js中统一引入并暴露出去

## assets目录
存放所有图片文件，图片按照功能划分存在各自的文件夹中

## components目录
存放所有自己封装的组件，写页面的时候是否需要在这个目录下封装组件事先跟欧文讨论一下，不是所有的组件都适合抽出来特地封装，文件名以大驼峰命名

## config目录
存放所有配置文件，配置按照功能划分写在各自的js文件中，然后在index.js中统一引入并暴露出去

## delete目录
存放目前不用的文件，目前放着老项目的页面，不用管

## lib目录
存放项目基础组件

## pages目录
存放所有页面，功能相关的页面命名时把自身功能的命名放在前面，比如漏洞列表和漏洞详情，分别命名成VulnList和VulnDetail，这样目录排序的时候他们就会排在一起方便查找，文件名以大驼峰命名

## display目录
所有演示用的页面放在这个文件夹，包括基本组件的使用样例页面，平时调研后用来给其他成员演示的页面

## utils目录
存放所有工具文件，ajax.js为封装的请求方法，平时不用动，平时经常用到的工具方法就写在tools.js中

# 开发基本要求

## 命名
变量名和函数统一使用小驼峰命名法，例如 userName, handleSubmit() ；常量的命名使用全大写，例如 TOKEN；类名的命名统一以短横线命名，比如vuln-detail

## 文件后缀
所有react组件、页面文件的命名都是tsx，跟页面无关只是处理逻辑的文件，基本工具类文件，命名成js，以示区分

## 分号
所有代码语句都要求以分号（;）结尾。虽然js没有强制要求写的时候要分号结尾，但不写的话会影响编译效率，因为编译器会自动补上。而且为了代码风格统一，要求写的时候全部带上。

## 逻辑闭合，代码健壮性
所有代码逻辑必须闭合，例如 if...else，不要出现那种逻辑走到某个分支却没任何处理代码导致的报错，代码层面需处理所有异常情况。

## 代码简洁
不要出现大段冗余的代码，所有需要复用的功能进行封装。

## 统一变量声明规范
尽量使用新语法，例如ES6语法，其中最基本的要求是使用let、const声明替换var声明，避免有的地方用var有的地方不用，统一都不要出现var。

## 避免硬编码
擅长使用配置项，代码里多次用到的，例如硬编码的一些变量值，统一提取到最外围存成一个变量，以后改动的时候只需要改动这个变量，避免代码里需要多处修改。

## git提交
git各自单独维护一个自己的分支，每次从develop分支拉取最新代码，提交时填写的message要详细写清楚该次提交的内容是什么，同时不要出现漏掉文件忘记提交的情况，确保自己提交上去的代码是不出问题的经过反复测试的。