import { Divider, Select, Space } from "antd"
import { useEffect, useState } from "react";
import config from "../../config";
import api from "../../api";
/**应用选择器 */
const APPSelector = (props: any) => {
    const [value,setValue]=useState<any>([])
    const handleChange = (newValue: string[]) => {
        setValue(newValue)
        props.onChange && props.onChange(newValue)
    };
    const [appList, setAppList] = useState([
        /*{
            appId: "app1",
            type: "static",
            desc: "演示1",
            server_names: ['*'],
        }, {
            appId: 'app2',
            type: "static",
            desc: "演示2",
            server_names: ['*'],
        }*/
    ])
    let getSiteList=(page=1,pageSize=10)=>{
        api.site.getSiteList({
            page,
            page_size:pageSize
        })
        .then(res=>{
            if(res?.data?.data){
                setAppList(res.data.data.rows || [])
            }
        })
    }
    useEffect(()=>{
        setValue(props.value || [])
        getSiteList()
    },[])
    
    return <Select
        mode="multiple"
        value={value}
        allowClear
        style={{ maxWidth: '100%',flex:1 }}
        placeholder="请选择应用"
        onChange={handleChange}
        optionLabelProp="title"
        options={appList.map((record: any) => {
            return {
                value: record.id+'',
                title:record.title,
                label: <div style={{
                    height: '50px',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                }}>
                    <div style={{
                        display: 'flex',
                        width: '100%',
                        height: '30px',
                        fontWeight: 200,
                        lineHeight: '30px',
                        alignItems: 'center',
                        gap: '10px'
                    }}>{
                            config.siteInfo.typeIcon[record.type]
                        }
                        <div>
                            {
                                record?.title || '未备注'
                            }
                        </div>
                    </div>
                    <div
                        className="domain-column"
                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {
                            (record?.server_names || []).length == 0 ?
                                "未配置匹配方式"
                                : (record.server_names.length == 0 && record.server_names[0] == '*'
                                    ? "通配所有域名" : (<Space>
                                        {
                                            record?.server_names.map((item, index) => {
                                                return <>
                                                    {
                                                        index > 0
                                                            ? <Divider type="vertical"></Divider>
                                                            : null
                                                    }
                                                    {item}

                                                </>
                                            })
                                        }
                                    </Space>))
                        }
                    </div>
                </div>
            }
        })}
    />
}
export default APPSelector