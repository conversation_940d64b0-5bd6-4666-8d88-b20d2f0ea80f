import { useContext, useEffect, useState } from "react"
import "./index.less"
import { Button, Descriptions, Empty, message, Modal, Radio, Select, Space, Spin, Tag } from "antd"
import moment from "moment"
import config from "../../../../../config"
import { Link } from "react-router-dom"
import { CopyOutlined } from "@ant-design/icons"
import api from "../../../../../api"
import wordList from "../../../../../config/wordList"
import ProviderContext from "../../../../providerContext"
import BugsLinkButton from "../../../../../components/BugsLinkButton"
const { AttackTypeRefer } = config
const methodColor = {
    GET: "#52c41a",
    "POST": "#1677ff",
    "PATCH": '#eb2f96',
    "DELETE": "#f5222d"
}
const LogDetail = (props: any) => {
    const Context = useContext(ProviderContext)
    const [open, setOpen] = useState(true)
    const [viewContent, setViewContent] = useState("request")
    const [isNull, setIsNull] = useState(true)
    const [requestContent, setRequestContent] = useState<any>({
        method: "GET",
        path: "/hello.html?payload=%3Cscript%3Ealert%28%29%3C%2Fscript%3E",
        //profile: "HTTP/2",
        headers: {
        },
        body: {}
    })
    const [responseContent, setResponse] = useState<any>(null)
    const [loading, setLoading] = useState(false)
    const [data, setData] = useState<any>({})
    const [siteInfo, setSiteInfo] = useState<any>({})
    const getSiteById = (id) => {
        api.site.getSiteById(id)
            .then(res => {
                if (res?.data?.data) {
                    setSiteInfo(res.data.data)
                } else {
                    setSiteInfo({})
                }
            })
            .catch((res) => {
                setSiteInfo({})
            })
    }
    const getDetail = (id) => {
        const processData = (data) => {
            let newHeader: any = {}
            if (props?.data == null) {
                setIsNull(true)
            } else {
                setIsNull(false)
            }
            if (data?.req_header) {
                newHeader = {
                    ...(Object.prototype.toString.call(data.req_header) == '[object Object]'
                        ? data.req_header
                        : JSON.parse(data.req_header || "{}"))
                }


            }
            newHeader.host = data?.host || '-'
            let method = data?.method ? data.method.toUpperCase() : wordList['未知'][Context.lan]
            let path = data?.path || '-'
            if (data?.req_query) {
                path += "?" + data.req_query
            }
            setData({
                ...data,
                requestContent: {
                    method: method,
                    path: path,
                    body: data?.req_body || {},
                    headers: newHeader

                },
                responseContent: {
                    status: data?.res_status || '-',
                    body: data?.res_body || '',
                    header: Object.prototype.toString.call(data?.res_header || "") == '[object String]' ? JSON.parse(data?.res_header || "{}") : (data?.res_header || {})

                }
            })
            getSiteById(data?.site_id || '')


        }
        if (props?.provideData) {
            processData(props.provideData)
            return
        }
        setLoading(true)
        api.record.getRecordByTraceId(id)
            .then(res => {
                setLoading(false)
                if (res?.data?.data) {
                    let data = res.data.data
                    processData(data)
                }
            })
    }
    useEffect(() => {
        getDetail(props?.data?.trace_id || '-')
    }, [props])
    const DetailContent = (
        <div className="log-detail-container">
            <div className="log-detail-simple-info">
                <Space>
                    <Tag style={{ borderRadius: '5px' }} color="#faad14">{AttackTypeRefer[data?.attack_type] || wordList['未知'][Context.lan]}</Tag>
                    <div style={{
                        fontWeight: 'bold'
                    }}>
                        {data?.url || '-'}
                    </div>
                </Space>
                <Descriptions title="" labelStyle={{ color: "#999999", width: '150px' }} style={{ marginTop: '10px' }}>
                    <Descriptions.Item label={wordList['攻击来源'][Context.lan]} span={6}
                        labelStyle={{
                            lineHeight: '25px'
                        }}>
                        <Space style={{
                            paddingTop: 0,
                            paddingBottom: 0,
                            height: '25px',
                        }}>
                            <div>{data?.src_ip || '-'}</div>
                            <div>(:{data?.src_port || '-'})</div>
                        </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label={wordList['受击站点'][Context.lan]} span={6}
                        labelStyle={{
                            lineHeight: '25px'
                        }}>
                        <Space style={{
                            paddingTop: 0,
                            paddingBottom: 0,
                            height: '25px',
                        }}>
                            {
                                !siteInfo?.title
                                    ? <span style={{ color: "#999" }}>站点不存在或已删除</span>
                                    : <>
                                        <Link to={"/domain/siteDetail/" + data?.site_id}>{siteInfo?.title || '-'}</Link>
                                        <div>
                                            ({data?.dst_ip || '-'}:{data?.dst_port || '-'})
                                        </div>
                                    </>
                            }

                        </Space>
                    </Descriptions.Item>
                    {/*<Descriptions.Item label="攻击载荷" span={6}>
                        <Space style={{
                            paddingTop: 0,
                            paddingBottom: 0,
                            height: '25px',
                        }}>
                            <div style={{
                                fontWeight: 'bold'
                            }}>
                                {
                                    props?.data?.playload?.position || "未知"
                                }
                            </div>
                            <div style={{
                                borderRadius: '5px',
                                marginLeft: '10px',
                                background: "#eaeaea",
                                paddingTop: '3px',
                                paddingBottom: '3px',
                                paddingLeft: '10px',
                                paddingRight: '10px',
                                height: '25px',
                                boxSizing: 'border-box',
                                lineHeight: '19px',
                                color: "#999999"
                            }}>
                                {props?.data?.playload?.content || '未知'}
                            </div>
                        </Space>
                    </Descriptions.Item>*/}
                    {/*<Descriptions.Item label="命中模块" span={6}>{protectionModule[props?.data?.protection?.module] || '未定义'}</Descriptions.Item>*/}
                    <Descriptions.Item label={wordList['命中规则'][Context.lan]} span={6}>{data?.policy_id || wordList['系统内置规则'][Context.lan]}</Descriptions.Item>

                    <Descriptions.Item label={wordList['攻击时间'][Context.lan]} span={6}>
                        {

                            moment(data?.time || '-').format("YYYY/MM/DD HH:mm:ss")
                        }
                    </Descriptions.Item>
                    <Descriptions.Item label="Trace ID" span={6}>{data?.trace_id || '-'}
                        <span style={{ marginLeft: '10px', color: "#cc1212", cursor: "pointer" }} onClick={() => {
                            if (data?.trace_id && navigator.clipboard) {

                                navigator.clipboard.writeText(data.trace_id)
                                message.success('复制成功')
                            } else {
                                message.error("无法写入剪贴板")
                            }
                        }}>
                            <CopyOutlined></CopyOutlined>
                        </span>
                    </Descriptions.Item>
                </Descriptions>
            </div>

            <div className="log-detail-content-info">
                <div style={{
                    position: 'relative',
                    width: '100%',
                    height: '30px'

                }}>
                    <Radio.Group
                        style={{
                            flexShrink: 0,
                            marginTop: '5px'
                        }}
                        options={[
                            { label: wordList['请求报文'][Context.lan], value: 'request' },
                            { label: wordList['响应报文'][Context.lan], value: 'response' },
                        ]}
                        onChange={(e) => {
                            setViewContent(e.target.value)
                        }}
                        value={viewContent}
                        optionType="button"
                        buttonStyle="solid"
                    />
                    <Select style={{
                        width: "100px",
                        position: 'absolute',
                        right: 0,
                        top: '0',
                        borderRadius: '10px'
                    }} options={[
                        {
                            label: "UTF-8",
                            value: "utf8"
                        },
                        {
                            label: "GBK",
                            value: 'gbk'
                        }
                    ]} defaultValue={"utf8"}></Select>
                </div>
                {
                    viewContent == 'request'
                        ? <div style={{
                            height: '220px',
                            overflowY: 'auto',
                            width: '100%',
                            background: "#fafafa",
                            marginTop: '20px',
                            borderRadius: '10px',
                            padding: '10px',
                            boxSizing: 'border-box'
                        }}>
                            <Space>
                                <div style={{
                                    color: methodColor[data?.requestContent?.method]
                                }}>
                                    {data?.requestContent?.method || '-'}
                                </div>
                                <div style={{
                                    fontWeight: 'bold'
                                }}>
                                    {
                                        decodeURIComponent(data?.requestContent?.path || '-')
                                    }
                                </div>
                                {/*<div style={{
                                    color: "#aa5d00"
                                }}>
                                    {
                                        requestContent.profile
                                    }
                                </div>*/}
                            </Space>
                            {
                                Object.keys(data?.requestContent?.headers || {}).map(headerName => {
                                    return <div style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '10px'
                                    }}>
                                        <span style={{ color: "#aa5d00" }}>{headerName}:</span>
                                        <div style={{ lineHeight: '25px' }}>{data?.requestContent.headers[headerName]}</div>

                                    </div>
                                })
                            }
                        </div>
                        : null
                }
                {
                    viewContent == 'response'
                        ? <div style={{
                            height: '220px',
                            overflowY: 'auto',
                            width: '100%',
                            background: "#fafafa",
                            marginTop: '20px',
                            borderRadius: '10px',
                            padding: '10px',
                            boxSizing: 'border-box'
                        }}>

                            {
                                data?.responseContent
                                    ? <>
                                        {
                                            //响应码:

                                            <div>----Code:{data?.responseContent?.status || '-'}----------------------------------------------------------------------</div>
                                        }
                                        {
                                            /**响应头 */
                                            Object.keys(data?.responseContent?.header || {}).length > 0
                                                ? <>
                                                    {
                                                        Object.keys(data?.responseContent?.header || {}).map(headerName => {
                                                            
                                                            return <div style={{
                                                                display: 'flex',
                                                                gap: '10px'
                                                            }}>
                                                                <span style={{ color: "#aa5d00" }}>{headerName}:</span>
                                                                <div style={{ lineHeight: '25px' }}>{data?.responseContent.header[headerName]}</div>

                                                            </div>
                                                        })
                                                    }
                                                    <div>--------------------------------------------------------------------------------------</div>
                                                </>
                                                : null
                                        }
                                        {
                                            /**响应体 */
                                            data?.responseContent?.body || '-'
                                        }
                                    </>
                                    : wordList['无响应报文'][Context.lan]
                            }
                        </div>
                        : null
                }
            </div>
        </div>
    )
    if (props.useContent === true) {
        return DetailContent
    }
    const generateCurl = (data) => {
        let curlCommand = `curl -X ${data.method} `;
        // 添加请求头
        const reqHeaders = JSON.parse(data.req_header);
        for (const item of Object.entries(reqHeaders)) {
            let header: any = item[0]
            let values: any = item[1]
            for (const value of values) {
                curlCommand += `-H "${header}: ${value}" `;
            }
        }
        // 添加请求体
        if (data.req_body) {
            const escapedBody = data.req_body.replace(/"/g, '\\"');
            curlCommand += `-d "${escapedBody}" `;
        }
        // 构建完整的请求链接
        const protocol = data.url.split('://')[0];
        let fullUrl = `${protocol}://${data.host}`;
        if (data.dst_port && !((protocol === 'http' && data.dst_port === 80) || (protocol === 'https' && data.dst_port === 443))) {
            fullUrl += `:${data.dst_port}`;
        }
        fullUrl += `${data.path}${data.req_query}`;
        // 添加完整的请求链接到 curl 命令
        curlCommand += `"${fullUrl}"`;
        return curlCommand;
    }
    return <Modal
        open={open}
        footer={<Space>
            <BugsLinkButton onClick={async () => {
                const curlCommand = generateCurl(data);
                try {
                    await navigator.clipboard.writeText(curlCommand);
                    message.success("cURL已复制")
                } catch (error) {
                    Modal.info({
                        title: "cURL无法写入剪贴板",
                        width: 1000,
                        style: {

                            maxWidth: "calc(100vw - 200px)"

                        },
                        content: <pre style={{
                            fontFamily: 'initial',
                            borderRadius: '10px',
                            padding: '10px',
                            boxSizing: 'border-box',
                            background: "#fafafa"
                        }}>
                            {curlCommand}
                        </pre>
                    })
                }
            }}>{wordList['复制cURL'][Context.lan]}</BugsLinkButton>
        </Space>}
        onCancel={() => {
            setOpen(false)
        }}
        afterClose={() => [
            props.close && props.close()
        ]}
        title="攻击详情"
        width={1200}
    >
        <Spin spinning={loading} key={data?.trace_id}>
            {
                isNull
                    ? <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={wordList['暂无记录内容'][Context.lan]} />
                    : DetailContent
            }
        </Spin>

    </Modal>
}
export default LogDetail