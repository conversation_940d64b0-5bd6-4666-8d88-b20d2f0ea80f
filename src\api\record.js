import ajax from "../tools/ajax";
const getRecordList=(data)=>ajax(`/api/v1/record`,data,"GET")//获取分页攻击日志
const getRecordByTraceId=(trace_id)=>ajax(`/api/v1/record/${trace_id}`,{},'GET')//按TraceId获取日志
const getLimitList=(data)=>ajax(`/api/v1/record/limit`,data,"GET")//获取分页频率限制攻击日志
const getBanIpList=(data)=>ajax(`/api/v1/spam`,data,'GET')//获取被封禁IP列表
const unBanIp=(data)=>ajax(`/api/v1/spam/unban`,data,'POST')//解除IP封禁
const exportLog=(data)=>`/api/v1/record/export`//返回文件下载地址
export default {
    getRecordList,
    getRecordByTraceId,
    getLimitList,
    getBanIpList,
    unBanIp,
    exportLog
}