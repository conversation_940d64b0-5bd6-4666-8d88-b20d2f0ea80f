import wordList from "../../config/wordList"
import "./index.less"
import ProviderContext from "../../pages/providerContext"
import { useContext, useEffect, useRef, useState } from "react"
import { Progress } from "antd"
const ViewPageList=()=>{
    const Context=useContext(ProviderContext)
    const setViewPage = useRef(false)
    const [viewPageList, setViewPageList] = useState(([
        {
            page: "http://www.bugbank.cn/homepage",
            times: 1000,
            percent: 0
        },
        {
            page: "http://www.bugbank.cn/homepage",
            times: 970,
            percent: 0
        },
        {
            page: "http://www.bugbank.cn/homepage",
            times: 850,
            percent: 0
        },
        {
            page: "http://www.bugbank.cn/homepage",
            times: 770,
            percent: 0
        },
        {
            page: "http://www.bugbank.cn/homepage",
            times: 370,
            percent: 0
        }, {
            page: "http://www.bugbank.cn/homepage",
            times: 100,
            percent: 0
        }
    ]))
    useEffect(() => {
        //拦截，防止死循环
        if (setViewPage.current) {
            setViewPage.current = false
            return
        }
        let prePageList: any = [...viewPageList.slice()]
        setViewPage.current = true
        let maxNums = Math.max(...prePageList.map(item => item.times))
        prePageList = prePageList.map(item => {
            return {
                ...item,
                percent: parseFloat((item.times / maxNums).toFixed(2)) * 100
            }
        })
        setViewPageList(prePageList)
    }, [viewPageList])
    return <div className="home-page-domain-list">
    <div className="page-card-title">{wordList['受访页面前五'][Context.lan]}</div>
    <div className="progress-list-container">
        {
            viewPageList.map(item => {
                return <div className="progress-list-item">
                    <div className="progress-list-item-text-conainer">
                        <div className="progress-list-item-text-title">{item.page}</div>
                        <div className="progress-list-item-text-data">{item.times}</div>
                    </div>

                    <Progress percent={item.percent} size="small" style={{ width: '100%' }} strokeColor={"#feab12"} showInfo={false} />
                </div>
            })
        }


    </div>
</div>
}
export default ViewPageList