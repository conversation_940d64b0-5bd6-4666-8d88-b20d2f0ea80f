import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Di<PERSON>r, Input, InputNumber, Popover, Radio, Space, Table, TimePicker, Switch, Select } from "antd"
import "./index.less"
import { useContext, useEffect, useState } from "react"
import moment from "moment";
import { render } from "@testing-library/react";
import wordList from "../../../../../config/wordList";
import ProviderContext from "../../../../providerContext";
import CustomCard from "../../../../../components/CustomCard";
import { error } from "console";
let updateTimer: any = null
const SiteFrequency = (props: any) => {
    const Context = useContext(ProviderContext)
    const [data, updateData] = useState({
        "request": {
            "enabled": false,
            "count": 1,
            "period": 1,
            "duration": 1,
            "action": 0,
            "status_codes": null
        },
        "attack": {
            "enabled": false,
            "count": 1,
            "period": 1,
            "duration": 1,
            "action": 0,
            "status_codes": null
        },
        "error": {
            "enabled": false,
            "count": 1,
            "period": 1,
            "duration": 1,
            "action": 0,
            "status_codes": null
        }
    })


    const setData = (newData) => {
        clearTimeout(updateTimer)
        updateTimer = setTimeout(() => {
            let sendData={}
            for(let key in newData){
                for(let child in newData[key])
                if(['count','period','duration'].includes(child) && newData[key][child]===0){
                    newData[key][child]=1
                }
                sendData[key]=newData[key]
            }
            props.onChange && props.onChange(sendData)
        }, 200)
        updateData(newData)
    }
    useEffect(() => {
        if (props?.data?.attack && props?.data?.request && props?.data?.error) {

            updateData(props?.data || {})
        }
    }, [])
    return <>
        <div style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            gap: '10px'
        }}>

            <div style={{
                width: '100%',
                height: "100%",
                boxSizing: 'border-box'
            }}>
                <div className="fre-protection-custom">
                    <div className="fre-protection-custom-item">
                        <div className="fre-protection-item-title">{wordList["高频访问限制"][Context.lan]}</div>
                        <Switch
                            checked={data.request.enabled}
                            onChange={(e) => {
                                setData({
                                    ...data,
                                    request: {
                                        ...data.request,
                                        enabled: e
                                    }
                                })
                            }}></Switch>
                    </div>
                    {

                        data.request.enabled ?
                            <>
                                <Space style={{
                                    gap: '15px'
                                }}>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">
                                            {wordList["经过时间"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.request?.period || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    request: {
                                                        ...data.request,
                                                        period: e
                                                    }
                                                })
                                            }} addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                    </div>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">

                                            {wordList["请求次数达到"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.request?.count || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    request: {
                                                        ...data.request,
                                                        count: e
                                                    }
                                                })
                                            }}
                                            addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                    </div>

                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">

                                            {wordList["处理方式"][Context.lan]}
                                        </div>
                                        <Select
                                            defaultValue="ban"
                                            style={{ width: '100%' }}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    request: {
                                                        ...data.request,
                                                        action: e
                                                    }
                                                })
                                            }}
                                            options={[
                                                /*{
                                                    value: 'bot',
                                                    label: wordList["人机验证"][Context.lan],
                                                },*/
                                                {
                                                    value: 'ban',
                                                    label: wordList["封禁"][Context.lan],
                                                },
                                            ]}
                                        />
                                    </div>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">
                                            {data.error.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.request?.duration || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    request: {
                                                        ...data.request,
                                                        duration: e
                                                    }
                                                })
                                            }}
                                            addonAfter="分钟" style={{ width: '100%' }}></InputNumber>
                                    </div>
                                </Space>

                                {/*<Divider orientation="left" style={{ marginTop: '5px', marginBottom: '5px', fontSize: '12px', color: "#999999" }}>{wordList["排除 Content-Type"][Context.lan]}</Divider>
                                <Select
                                    size="large"
                                    mode="tags"
                                    style={{ width: '100%', borderRadius: '10px' }}
                                    placeholder="Tags Mode"
                                    onChange={(e) => {
                                       
                                    }}
                                    options={[]}
                                />*/}
                            </>

                            : null
                    }
                </div>
                <div className="fre-protection-custom">
                    <div className="fre-protection-custom-item">
                        <div className="fre-protection-item-title">{wordList["高频攻击限制"][Context.lan]}</div>
                        <Switch
                            checked={data.attack.enabled}
                            onChange={(e) => {
                               
                                setData({
                                    ...data,
                                    attack: {
                                        ...data.attack,
                                        enabled: e
                                    }
                                })
                            }}></Switch>
                    </div>
                    {

                        data.attack.enabled ?
                            <>
                                <Space style={{
                                    gap: '15px'
                                }}>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">
                                            {wordList["经过时间"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.attack?.period || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    attack: {
                                                        ...data.attack,
                                                        period: e
                                                    }
                                                })
                                            }}
                                            addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                    </div>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">

                                            {wordList["攻击阻断次数达到"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.attack?.count || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    attack: {
                                                        ...data.attack,
                                                        count: e
                                                    }
                                                })
                                            }}
                                            addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                    </div>

                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">

                                            {wordList["处理方式"][Context.lan]}
                                        </div>
                                        <Select
                                            defaultValue={0}
                                            style={{ width: '100%' }}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    attack: {
                                                        ...data.attack,
                                                        action: e
                                                    }
                                                })
                                            }}

                                            value={data?.attack?.action || 0}
                                            options={[
                                                /*{
                                                    value: 'bot',
                                                    label: wordList["人机验证"][Context.lan],
                                                },*/
                                                {
                                                    value: 0,
                                                    label: wordList["封禁"][Context.lan],
                                                },
                                            ]}
                                        />
                                    </div>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">
                                            {data.attack.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.attack?.duration || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    attack: {
                                                        ...data.attack,
                                                        duration: e
                                                    }
                                                })
                                            }}
                                            addonAfter="分钟"
                                            style={{ width: '100%' }}></InputNumber>
                                    </div>
                                </Space>
                            </>

                            : null
                    }
                </div>
                <div className="fre-protection-custom">
                    <div className="fre-protection-custom-item">
                        <div className="fre-protection-item-title">{wordList["高频错误限制"][Context.lan]}</div>
                        <Switch
                            checked={data.error.enabled}
                            onChange={(e) => {
                               
                                setData({
                                    ...data,
                                    error: {
                                        ...data.error,
                                        enabled: e
                                    }
                                })
                            }}></Switch>
                    </div>
                    {

                        data.error.enabled ?
                            <>
                                <Divider orientation="left"
                                    style={{ marginTop: '5px', marginBottom: '5px', fontSize: '12px', color: "#999999" }}>
                                    {wordList["状态码"][Context.lan]}
                                </Divider>
                                <Select
                                    dropdownStyle={
                                        {
                                            display: 'none'
                                        }
                                    }
                                    size="large"
                                    mode="tags"
                                    value={data?.error?.status_codes || []}
                                    style={{ width: '100%', borderRadius: '10px' }}
                                    placeholder="HTTP状态码"
                                    onChange={(e) => {
                                        setData({
                                            ...data,
                                            error: {
                                                ...data.error,
                                                status_codes: e
                                            }
                                        })
                                    }}
                                    options={[]}
                                />
                                <Space style={{
                                    gap: '15px'
                                }}>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">
                                            {wordList["经过时间"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.error?.period || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    error: {
                                                        ...data.error,
                                                        period: e
                                                    }
                                                })
                                            }}
                                            addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                    </div>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">

                                            {wordList["错误次数达到"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.error?.count || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    error: {
                                                        ...data.error,
                                                        count: e
                                                    }
                                                })
                                            }}
                                            addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                    </div>

                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">

                                            {wordList["处理方式"][Context.lan]}
                                        </div>
                                        <Select
                                            defaultValue={0}
                                            style={{ width: '100%' }}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    error: {
                                                        ...data.error,
                                                        action: e
                                                    }
                                                })
                                            }}
                                            value={data?.error?.action || 0}
                                            options={[
                                                /*{
                                                    value: 'bot',
                                                    label: wordList["人机验证"][Context.lan],
                                                },*/
                                                {
                                                    value: 0,
                                                    label: wordList["封禁"][Context.lan],
                                                },
                                            ]}
                                        />
                                    </div>
                                    <div className="fre-action-card">
                                        <div className="fre-action-card-title">
                                            {data.error.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                        </div>
                                        <InputNumber
                                            value={data?.error?.duration || 0}
                                            onChange={(e) => {
                                                setData({
                                                    ...data,
                                                    error: {
                                                        ...data.error,
                                                        duration: e
                                                    }
                                                })
                                            }}
                                            addonAfter="分钟"
                                            style={{ width: '100%' }}></InputNumber>
                                    </div>
                                </Space>
                            </>

                            : null
                    }
                </div>
            </div>


        </div>
    </>
}
export default SiteFrequency
