



import { ruleTouchType,touchMethod } from './ruleTouchType';
import account from './account';
import placementNames from './placementNames';
import siteInfo from './siteInfo';
import appIcons from "./appIcons"
import defendModuleIcons from './defendModuleIcons';
const ruleTypes=[
    {
        value:"all",
        label:'全部'
    },{
        value:0,
        label:'白名单'
    },{
        value:1,
        label:'黑名单'
    }/*,{
        value:"bot",
        label:'人机验证'
    },{
        value:"auth",
        label:'身份验证'
    }*/
]
const ruleTypeRefer={
    "all":{
        "name":'全部',
        icon:''
    },
    0:{
        name:'白名单',
        icon:<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10214" width="20" height="20"><path d="M168.789333 855.338667h310.186667a31.232 31.232 0 1 1 0 62.464H147.370667c-23.68 0-42.837333-18.645333-42.837334-41.642667v-208.64c0-101.248 84.352-183.296 188.416-183.296h189.568a31.232 31.232 0 0 1 0 62.464H292.992c-68.608 0-124.16 54.101333-124.16 120.789333l-0.042667 187.861334z" fill="#303133" p-id="10215"></path><path d="M481.194667 484.138667a162.901333 162.901333 0 1 0 0-325.802667 162.901333 162.901333 0 0 0 0 325.802667z m0 61.098666a224 224 0 1 1 0-448 224 224 0 0 1 0 448z" fill="#303133" p-id="10216"></path><path d="M917.248 530.773333a32 32 0 0 1 2.688 45.141334l-227.584 256a32 32 0 0 1-47.786667 0l-113.792-128a32 32 0 1 1 47.786667-42.496l89.898667 101.12 203.605333-229.12a32 32 0 0 1 45.226667-2.688z" fill="#1E6DF5" p-id="10217"></path></svg>
    },
    1:{
        name:'黑名单',
        icon:<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11538" width="16" height="16"><path d="M777.344 556.032a230.4 230.4 0 1 0 0 460.8 230.4 230.4 0 0 0 0-460.8z m-171.776 230.4a171.776 171.776 0 0 1 171.776-171.776 170.24 170.24 0 0 1 104.32 36.096l-249.152 227.2a170.432 170.432 0 0 1-26.88-91.52z m171.776 171.776a170.24 170.24 0 0 1-104.32-36.16l249.216-227.2c16.832 26.624 26.88 57.856 26.88 91.584a171.776 171.776 0 0 1-171.776 171.776z" fill="#d81e06" p-id="11539" data-spm-anchor-id="a313x.search_index.0.i29.75303a8133pZXU" ></path><path d="M587.136 595.2c2.752 0.768 5.504 1.664 8.576 1.664a30.528 30.528 0 1 0 15.04-57.408l0.384-0.832c-1.088-0.384-2.176-0.64-3.2-1.088l-0.576-0.128a431.488 431.488 0 0 0-22.528-7.616c85.76-48.832 144.256-140.032 144.256-245.824a283.968 283.968 0 0 0-567.872 0c0 109.12 62.208 202.752 152.512 250.304a430.976 430.976 0 0 0-288 391.744H26.24c-0.064 0.832-0.448 1.472-0.448 2.304 0 17.024 13.824 30.912 30.912 30.912 21.312 0 37.312-13.824 37.312-30.912 0-0.832-0.384-1.472-0.448-2.304h0.64c7.872-196.928 163.2-347.904 362.048-347.904 46.08 0 90.048 2.048 130.688 17.472l0.192-0.448zM445.184 504.768a220.8 220.8 0 1 1 0-441.728 220.8 220.8 0 0 1 0 441.728z" fill="" p-id="11540"></path></svg>
    },
    "bot":{
        name:"人机验证",
        icon:<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15397" width="24" height="24"><path d="M554.666667 251.733333V341.333333h341.333333v426.666667H170.666667V341.333333h341.333333V251.733333c-25.6-8.533333-42.666667-34.133333-42.666667-59.733333 0-34.133333 29.866667-64 64-64s64 29.866667 64 64c0 29.866667-17.066667 51.2-42.666666 59.733333zM512 384H213.333333v341.333333h640V384h-341.333333z m-384 85.333333v213.333334H85.333333v-213.333334h42.666667z m853.333333 0v213.333334h-42.666666v-213.333334h42.666666zM384 597.333333c-25.6 0-42.666667-17.066667-42.666667-42.666666s17.066667-42.666667 42.666667-42.666667 42.666667 17.066667 42.666667 42.666667-17.066667 42.666667-42.666667 42.666666z m298.666667 0c-25.6 0-42.666667-17.066667-42.666667-42.666666s17.066667-42.666667 42.666667-42.666667 42.666667 17.066667 42.666666 42.666667-17.066667 42.666667-42.666666 42.666666z" fill="#165dff" p-id="15398"></path></svg>
    },
    "auth":{
        name:"身份验证",
        icon:<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16494" width="20" height="20"><path d="M209.513187 890.861078c-62.613136 0-113.529313-50.916177-113.529313-113.529313L95.983874 278.834873c0-62.613136 50.916177-113.529313 113.529313-113.529313L832.548295 165.30556c62.613136 0 113.529313 50.916177 113.529313 113.529313l0 498.496892c0 62.613136-50.916177 113.529313-113.529313 113.529313L209.513187 890.861078zM209.513187 226.198555c-28.898371 0-52.636318 23.565933-52.636318 52.636318l0 498.496892c0 28.898371 23.565933 52.636318 52.636318 52.636318L832.548295 829.968083c28.898371 0 52.636318-23.565933 52.636318-52.636318L885.184613 278.834873c0-28.898371-23.565933-52.636318-52.636318-52.636318L209.513187 226.198555zM791.092894 703.88174c-16.857383 0-30.446498-13.761129-30.446498-30.446498 0-51.948261-42.143457-94.091718-94.091718-94.091718s-94.091718 42.143457-94.091718 94.091718c0 16.857383-13.761129 30.446498-30.446498 30.446498-16.857383 0-30.446498-13.761129-30.446498-30.446498 0-53.15236 27.350244-102.176382 72.417941-130.55871-19.781623-20.985721-30.790526-48.507979-30.790526-77.234336 0-62.613136 50.916177-113.529313 113.529313-113.529313s113.529313 50.916177 113.529313 113.529313c0 28.726356-11.180917 56.0766-30.790526 77.234336 44.895683 28.382328 72.417941 77.234336 72.417941 130.55871C821.711406 690.120611 807.950277 703.88174 791.092894 703.88174zM666.554678 413.177893c-28.898371 0-52.636318 23.565933-52.636318 52.636318s23.565933 52.636318 52.636318 52.636318c29.070385 0 52.636318-23.565933 52.636318-52.636318S695.453049 413.177893 666.554678 413.177893z" fill="#165dff" p-id="16495"></path><path d="M434.507643 413.86595 248.216361 413.86595c-17.717453 0-31.994625-14.277171-31.994625-31.994625 0-17.717453 14.277171-31.994625 31.994625-31.994625l186.291282 0c17.717453 0 31.994625 14.277171 31.994625 31.994625C466.502268 399.588779 452.225097 413.86595 434.507643 413.86595z" fill="#165dff" p-id="16496"></path><path d="M434.507643 559.90593 248.216361 559.90593c-17.717453 0-31.994625-14.277171-31.994625-31.994625s14.277171-31.994625 31.994625-31.994625l186.291282 0c17.717453 0 31.994625 14.277171 31.994625 31.994625S452.225097 559.90593 434.507643 559.90593z" fill="#165dff" p-id="16497"></path><path d="M434.507643 704.397783 248.216361 704.397783c-17.717453 0-31.994625-14.277171-31.994625-31.994625s14.277171-31.994625 31.994625-31.994625l186.291282 0c17.717453 0 31.994625 14.277171 31.994625 31.994625S452.225097 704.397783 434.507643 704.397783z" fill="#165dff" p-id="16498"></path></svg>
    }
}



/**自定义规则类型 */
const customRuleType={
    "src_ip":'源 IP',
    'website':'应用',
    'uri':"URL",
    "uri_no_query":"URL路径",
    "host":"Host",
    "req_header":"Header",
    "get_param":"GET参数",
    "post_param":"POST参数",
    "req_body":"Body",
    "method":"请求方法"
}
const config = {
    customRuleType,
    placementNames,
    account,
    appIcons,
    AttackTypeRefer: {
        '0':'非攻击',
        "1": 'SQL注入',
        "2": "XSS攻击",
        "-1": "白名单",
        "-2": "黑名单",
        "3":"扫描器",
        '4':"OWASP",
        '5':"频率限制",
        '6':"文件上传"
        /*'CSRF': "CSRF",
        "SSRF": "SSRC",
        "dumps": '反序列化',
        'backDoor': '后门',
        'codeExec': "代码执行",
        'codeInjection': "代码注入",
        'cmdInjection': "命令注入",
        'upload': '文件上传',
        'fileInclude': '文件包含',
        'redirect': "重定向",
        'unAuth': '未授权访问',
        'informationLeakage': '信息泄露',
        'XXE': "XXE",
        'XPath': "Xpath注入",
        'dir': "目录穿越",
        'scaner': '扫描器',
        'fileChange': "文件修改",
        "fileRead": '文件读取',
        'fileDelete': '文件删除',
        "timeOut": '超时',
        "cookie": 'Cookie篡改',
        "news": '威胁情报'*/
    },
    modeulesName:{
        'scanner':'扫描器',
        "sqli":"SQL注入检测",
        "xss":"XSS检测",
        'owasp':'OWASP',
        "fileUpload":"文件上传检测",
        "fileInclude":"文件包含检测",
        "cmdInjection":"命令注入检测",
        "javaCodeInjection":"JAVA代码注入检测",
        "javacDumps":"JAVA反序列化检测",
        "phpDumps":"PHP反序列化检测",
        "phpCodeInjection":"PHP代码注入检测",
        "aspCodeInjection":"ASP代码注入检测",
        "templateInjection":"模板注入检测",
        "CSRF":"CSRF检测",
        "SSRF":'SSRF检测',
        "strangeHttp":"畸形HTTP协议检测"
    },
    defendModuleIcons,
    ruleTypes,
    ruleTypeRefer,
    ruleTouchType,
    touchMethod,
    siteInfo
   
};

export default config;