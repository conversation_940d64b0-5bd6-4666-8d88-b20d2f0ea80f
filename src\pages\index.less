.menu-button {
    width: 170px;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: .2s;
}

.bugsguard {
    font-size: 18px;
    font-weight: 700;
    height: 40px;
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
    margin-top: 8px;
    justify-content: center;
}

.bugsguard-icon {
    display: none;
}

.ant-menu-item:hover {
    color: #cc1212 !important;
}

.ant-pagination-item-active {
    border-color: #cc1212 !important;

    a {
        color: #cc1212 !important;
    }
}

.ant-table-tbody {
    tr:last-child {
        td:has(.ant-empty) {
            border-bottom: none !important;
        }

    }

}

/*.ant-menu-submenu-title {
    cursor: default !important;
}*/
.ant-tag-checkable:hover{
    color: #cc1212 !important;
}
.ant-radio-button-wrapper-checked{
    span{
        
        color:  white !important;
    }
}
.ant-radio-button-wrapper-checked:hover{
    span{
        
        color:  white !important;
    }
}
.ant-tag-checkable-checked:hover{
    color:  white !important;
}
.ant-radio-button-wrapper:hover{
    color: #cc1212 !important;
}
.menu-container {
    width: 228px;

    .ant-menu {

        width: 200px;

        li {

            margin-top: 0px;
            min-height: 4 v0px;
        }
    }

    height: 100vh;

    display: flex;
    flex-direction: column;

    .ant-menu-inline {
        background-color: transparent !important;

    }

    li {
        ul {
            li {
                .ant-menu-title-content {
                    margin-left: 2px;
                }
            }

        }
    }

    padding: 10px;
    box-sizing: border-box;
    background-color: transparent;

    .ant-menu-submenu-title {
        height: 50px;
    }

    .ant-menu-inline {
        border: none !important;
    }

    .ant-menu-item-selected::after {
        display: none;
    }

    .ant-menu-item-selected {
        border-radius: 10px !important;
        background: #cc1212 !important;
        box-shadow: 0 0 10px #cc121299;
    }


    .menu-button-text {
        font-size: 14px;
        color: black;
        gap: 5px;
    }

    .ant-menu-item-selected {
        .menu-button-text {
            color: white;
            gap: 5px;
        }
    }

    .ant-menu-submenu-arrow {
        display: none;
    }

    .ant-menu-submenu-open {
        .ant-menu-submenu-title {

            height: 50px;
            /*background-image: linear-gradient(to right, #cc1212, #2365fe, #2f6dfd, #3a75fb, #457cf9);*/
            background: transparent;
            border-radius: 10px;
            color: black;
            /*box-shadow: 0 2px 15px #004effbd;*/
        }

        .menu-button-text {
            color: black;
            gap: 5px;
        }

        .ant-menu {
            .ant-menu-item-selected {
                position: relative;
                background-color: transparent !important;
                border-radius: 10px;
                box-shadow: none;

                span {
                    color: #cc1212;
                    font-size: 14px;
                }

            }

            .ant-menu-item-selected::after {
                content: '';
                display: none;
            }
        }

    }

    .ant-menu-item::before {
        content: '';
        position: absolute;
        left: 31px;
        top: 18px;
        height: 4px;
        width: 4px;
        border-radius: 10px;
        background-color: #999;
    }

    .ant-menu-submenu-selected {
        .ant-menu-submenu-title {

            height: 50px;
            /*background-image: linear-gradient(to right, #cc1212, #2365fe, #2f6dfd, #3a75fb, #457cf9);*/
            background: #cc1212;
            border-radius: 10px;
            color: white;

            /*box-shadow: 0 2px 15px #004effbd;*/
            .menu-button-text {
                color: white;
                gap: 5px;
            }

            position: relative;
        }

        .ant-menu-item-selected::before {
            content: '';
            position: absolute;
            left: 30px;
            top: 18px;
            height: 6px;
            width: 6px;
            border-radius: 10px;
            background-color: #cc1212;
        }
    }
}

.page-content {
    width: calc(100% - 228px)
}

.close-menu {
    .bugsguard {
        display: none
    }

    .page-content {
        width: calc(100% - 95px);
    }

    .menu-container {
        width: 100px;

        li {
            ul {
                li {
                    .ant-menu-title-content {
                        margin-left: -40px;
                    }
                }
            }
        }

        .ant-menu {
            width: 85px;

            .menu-button-text {
                span {
                    display: none;
                }
            }
        }
    }
}

.close-menu-button {}

@media screen and (max-width: 830px) {
    .close-menu-button {
        display: none;
    }

    /*.bugsguard {
        display: none
    }
    .bugsguard-icon{
        display:initial;
        width: 60px;
        height: 30px;
    }
    .page-content {
        width: calc(100% - 95px);
    }

    .menu-container {
        width: 100px;

        li {
            ul {
                li {
                    .ant-menu-title-content {
                        margin-left: -40px;
                    }
                }
            }
        }

        .ant-menu {
            width: 85px;

            .menu-button-text {
                span {
                    display: none;
                }
            }
        }
    }*/
}

.header-container {
    width: 100%;
    height: 50px;
    background: transparent;
    display: flex;
    position: relative;
    z-index: 2;
    justify-content: space-between;
    padding-left: 10px;
    padding-right: 10px;
    box-sizing: border-box;
    margin-bottom: 8px;
}

/**基础信息框*/
.baseinfo-border-container {
    width: calc(100% - 30px);
    margin-left: 15px;
    padding: 10px;
    box-sizing: border-box;
    height: 240px;
    border-radius: 10px;
    border: 1px solid #ddd;
    box-sizing: border-box;
    margin-top: 10px;
}

.card-title {
    width: auto;
    height: 30px;
    line-height: 30px;
    position: relative;
    font-weight: bold;
    font-size: 14px;
}


.card-title::before {
    content: '';
    position: absolute;
    left: -10px;
    z-index: 2;
    height: 20px;
    background-color: #cc1212;
    top: 5px;
    width: 5px;
    border-radius: 10px;
}

.lan-trans {
    border: none;
    border-radius: 5px;
    overflow: hidden;

    label {
        border: none !important;
    }
}

a:hover {
    color: #cc1212 !important;
}