import { Tag, <PERSON><PERSON>, <PERSON>, DatePicker, Divider, Input, InputNumber, Popover, Radio, Space, Table, TimePicker, Select } from "antd"
import "./index.less"
import { useContext, useState } from "react"
import moment from "moment";
import { render } from "@testing-library/react";
import ProviderContext from "../../providerContext";
import wordList from "../../../config/wordList";
import CustomTable from "../../../components/CustomTable";
import CustomCard from "../../../components/CustomCard";
const LogAuth = (props: any) => {
    const Context = useContext(ProviderContext)
    const [findTarget, setFindTarget] = useState("event")
    const [authData, setAuthData] = useState([
        {
            type: 'static',
            username: "<EMAIL>",
            ip: {
                number: "***********",
                placement: "上海-上海"
            },
            domainRule: ['*'],
            result: "success",
            touchTime: Date.now()
        },
        {
            username: "<EMAIL>",
            ip: {
                number: "***********",
                placement: "上海-上海"
            },
            domainRule: ['*'],
            result: "fail",
            touchTime: Date.now()
        }
    ])

    const columns = [
        {
            title: wordList["用户账号"][Context.lan],
            key: "username",
            dataIndex: 'username',
            width: 150
        },
        {
            title:  wordList["认证结果"][Context.lan],
            key: "result",
            dataIndex: 'result',
            width: 100,
            render: (res) => {
                return res == "success" ? wordList["成功"][Context.lan] : wordList["失败"][Context.lan]
            }
        },
        {
            title: wordList["源IP"][Context.lan],
            key: 'ip',
            dataIndex: 'ip',
            width: 150,
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '50px',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                }}>
                    <div style={{
                        display: 'flex',
                        width: '100%',
                        height: '30px',
                        fontWeight: 200,
                        lineHeight: '30px',
                        alignItems: 'center',
                        gap: '10px'
                    }}>
                        <div>
                            {
                                record?.ip?.number || '-'
                            }
                        </div>
                    </div>
                    <div

                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {record?.ip?.placement || '未知'}
                    </div>
                </div>
            }
        }, {
            title: wordList["站点"][Context.lan],
            key: 'domain',
            dataIndex: 'domain',
            width: 400,
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '50px',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                }}>
                    <div style={{
                        display: 'flex',
                        width: '100%',
                        height: '30px',
                        fontWeight: 200,
                        lineHeight: '30px',
                        alignItems: 'center',
                        gap: '10px'
                    }}>{
                            {
                                "static": <svg viewBox="0 0 1241 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="24781" width="32" height="32"><path d="M10.86 0h1024v1024h-1024z" fill="#D8D8D8" fill-opacity="0" p-id="24782"></path><path d="M433.26 76.8a89.6 89.6 0 0 1 57.089 158.669l71.526 130.099a101.94 101.94 0 0 1 98.97 13.056l47.257-47.36a51.2 51.2 0 1 1 17.357 18.842l-46.131 46.284a101.99 101.99 0 0 1 16.077 100.813l174.54 98.1a76.8 76.8 0 1 1-11.264 23.04L683.373 519.73a102.554 102.554 0 0 1-56.832 39.834L641.9 665.65a64 64 0 1 1-25.396 3.584l-15.36-106.189-1.484 0.103a102.042 102.042 0 0 1-70.35-28.007l-185.855 147.61a153.6 153.6 0 1 1-15.923-20.07L513.03 515.379a101.888 101.888 0 0 1-15.053-66.918l-58.06-14.439a51.2 51.2 0 1 1 6.144-24.422l-0.052-0.46 58.164 14.54c7.168-18.432 19.558-34.253 35.276-45.722l-71.065-129.126A89.6 89.6 0 1 1 433.26 76.8z" fill="#3E76FF" p-id="24783"></path></svg>,
                                "default": <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="31422" width="32" height="32"><path d="M371.488422 87.88386a255.249904 255.249904 0 1 0 130.620843 336.53841A255.549493 255.549493 0 0 0 371.488422 87.88386z" fill="#F8C44F" p-id="31423"></path><path d="M532.168037 862.022066A390.963761 390.963761 0 0 1 373.485682 828.86754a394.858419 394.858419 0 0 1 159.780848-756.16286 395.058145 395.058145 0 0 1 360.805126 553.640636 395.257871 395.257871 0 0 1-361.803756 235.67675z" fill="#FFFFFF" p-id="31424"></path><path d="M533.26653 92.277833a374.885813 374.885813 0 1 1-343.628685 223.79305A372.78869 372.78869 0 0 1 533.26653 92.277833z m0-39.945211a414.831025 414.831025 0 0 0-167.76989 794.4104 410.736641 410.736641 0 0 0 166.671397 35.25165A414.831025 414.831025 0 0 0 700.137652 87.584271a410.337189 410.337189 0 0 0-166.871122-35.251649z" fill="#282D33" p-id="31425"></path><path d="M903.858233 515.197764l-52.128501 40.54439-94.070974 79.09152-4.194248-76.694807-60.916448 11.084796-6.890549-70.703025-41.942472 22.369319-12.083427-9.287262-36.150417-36.25028-11.484248 49.132611 27.662059 27.861785L490.42529 669.885597l-32.854937 5.192877v-63.413023l23.667538-95.868509-43.040965-21.071099-4.393974-41.243431 54.525215-57.221516 40.444527 7.489727 16.17781 24.965757 59.917818 9.986303-45.038226-58.619598-45.837131-29.958909 3.994521-16.077948 59.917818-6.59096-6.59096-39.945212 67.806997-22.668907 43.740007 40.843979 97.166728 10.984933s43.340555-3.29548 91.674261-6.59096a374.985676 374.985676 0 0 1 32.155896 205.118663zM434.601857 105.859205L400.548563 131.024689l-61.116174 47.035487a5.891919 5.891919 0 0 0-2.196986 5.392603l7.090275 48.43357a6.491097 6.491097 0 0 0 3.395343 4.493836 5.59233 5.59233 0 0 0 5.592329-0.599178l34.552609-24.166853 18.774249 67.008093-109.350017 35.551238L250.754019 298.894442a5.792056 5.792056 0 0 0-5.991782 1.497945l-21.470551 22.968497a5.991782 5.991782 0 0 0-1.398083 5.392604L235.275249 379.483907l-38.147677-43.040966-6.291371-22.469182A374.086909 374.086909 0 0 1 434.601857 105.859205zM240.767716 520.98982l-4.393973-72.50056 63.013571-57.421242 55.723571 125.228239-149.794544 126.826048V599.182572l34.153156-75.79604a5.192878 5.192878 0 0 0 1.298219-2.396712zM461.564875 231.986212l-3.195617-97.965632c68.506038-3.794795 108.850702 6.291371 123.930019 11.284522l-49.332336 67.307682z" fill="#34CA9D" p-id="31426"></path><path d="M960.680297 76.299749a65.709874 65.709874 0 0 0-9.187399-12.283153l28.860416-27.562196a109.849333 109.849333 0 0 1 14.779728 19.972606zM159.279484 966.878247l-2.696302-39.945212a285.208813 285.208813 0 0 0 37.24891-5.29274l7.989042 39.146307a310.773748 310.773748 0 0 1-42.54165 6.091645z m-44.439048-1.398082a137.511392 137.511392 0 0 1-44.838501-14.280413l18.874113-35.151787a98.065495 98.065495 0 0 0 31.756443 9.986303z m127.924541-15.179181L230.781413 912.053444c11.783838-3.595069 24.166853-7.889179 36.649732-12.782468l14.480139 37.24891c-13.18192 5.192878-26.463703 9.786577-39.146307 13.781098z m77.593574-29.958909l-16.577263-36.350142c11.584111-5.292741 23.467812-11.084796 35.351512-17.276305l18.374798 35.551239c-12.582742 6.491097-25.06562 12.482879-36.849458 18.075208zM33.152477 918.744267a120.834266 120.834266 0 0 1-18.774249-44.239322l39.046444-8.388495a78.392478 78.392478 0 0 0 12.383016 29.958909z m360.505537-36.050554l-19.972605-34.752334c11.284522-6.391234 22.768771-13.18192 34.153156-19.972606l20.971236 33.95343c-11.683974 6.890549-23.567675 14.080687-35.151787 20.77151z m69.904121-43.040966l-22.069729-33.254388c10.88507-7.190138 21.870003-14.679865 32.854936-22.469182l23.06836 32.655211Q480.438987 828.86754 463.462272 839.652747zM51.02796 831.16439L11.082748 828.86754a267.33333 267.33333 0 0 1 5.692192-43.040966l39.146308 8.088906A224.392228 224.392228 0 0 0 51.02796 831.16439z m479.342542-38.646993l-23.967127-31.956169c9.986303-7.889179 21.071099-15.978085 31.556717-24.266716l24.766032 31.356991q-16.177811 12.782468-32.355622 24.865894zM65.807688 757.465474l-38.047814-12.283153c4.194247-12.682605 9.087536-25.864525 14.580002-39.24617l36.949321 15.279043c-5.192878 12.383016-9.686714 24.666168-13.481509 36.25028z m528.674879-15.578633l-25.564935-30.65795c9.986303-8.488358 19.972606-17.076578 29.958908-25.864524l26.263977 29.958908c-9.986303 9.187399-20.272195 17.975345-30.65795 26.563566z m61.515626-53.626447l-26.963018-29.958908c8.787947-8.088905 17.575893-16.277674 26.263977-24.666169l2.796165-2.696302 27.562196 28.960279-2.696302 2.596439c-8.987673 9.087536-17.975345 17.47603-26.963018 25.764661z m-560.531185-2.696301l-35.950691-17.476031c5.792056-11.883701 12.283153-24.266716 19.173702-36.649731l34.95206 19.273564a791.114921 791.114921 0 0 0-18.175071 34.852198z m619.150784-53.72631l-28.261238-28.261238c9.986303-9.486988 18.77425-18.973976 27.761923-28.560826l29.060141 27.562196c-9.187399 9.786577-18.674387 19.473291-28.3611 29.259868z m56.322749-59.218777l-29.958909-26.763292c8.987673-9.986303 17.775619-19.972606 26.363839-29.958909l29.958909 26.064251q-12.383016 15.378907-26.164113 30.65795z m53.226994-61.915078l-30.957539-25.265347c8.488358-9.986303 16.776989-20.77151 24.666168-31.157265L849.932197 478.647895c-8.388494 10.685344-16.876852 21.270825-25.564936 32.056033z m49.931515-64.91097l-32.255758-23.367949c7.889179-10.88507 15.47877-21.77014 22.76877-32.555347l33.154526 22.369318c-7.889179 11.084796-15.378907 22.269456-23.467812 33.553978zM919.836318 377.786235l-33.95343-21.170962c7.090275-11.284522 13.880961-22.768771 19.972606-33.95343l34.852197 19.972606c-6.191508 11.384385-13.281783 23.268086-20.871373 35.151786z m39.945212-71.901381l-34.952061-17.875482c5.991782-11.983564 11.584111-23.967127 16.577263-35.451376l36.649732 15.678496c-5.292741 12.283153-11.184659 24.965757-17.575893 37.648362zM993.135782 229.689362l-37.748226-12.982194a376.683348 376.683348 0 0 0 10.685344-36.949321l38.846719 9.087536c-2.995891 12.682605-6.990412 26.36384-11.783837 40.843979z m18.674386-83.38563l-39.945212-3.195617c0-4.993151 0.599178-9.986303 0.599178-14.580002a139.1092 139.1092 0 0 0-1.497945-20.971236l39.945212-6.091645a168.269205 168.269205 0 0 1 1.99726 27.062881c-0.399452 5.392604-0.599178 11.284522-1.098493 17.376167zM133.614685 616.458876l-34.053293-20.971236c3.495206-5.692193 7.090275-11.384385 10.785207-17.176441l33.653841 21.470551z" fill="#282D33" p-id="31427"></path><path d="M142.302769 966.678521c-42.042335 0-76.095629-11.584111-99.263852-35.950691l28.860416-27.562196c69.904121 72.700286 320.360599-17.975345 583.399819-269.031002 119.835636-114.143443 216.003733-238.173326 271.028263-349.520603 51.329597-103.458099 59.917818-183.947701 25.165483-220.996885C909.850015 19.977 801.298902 37.153441 668.980387 108.455644L650.206138 73.303858C803.495888-9.382731 923.830839-22.764377 980.353314 36.4544c48.333706 50.630556 41.842609 145.200845-18.274935 266.234837-57.02179 115.04221-156.185778 243.066614-279.616483 360.405674-198.727429 190.039345-408.439791 303.58361-540.159127 303.58361z" fill="#282D33" p-id="31428"></path><path d="M298.488547 920.84139a83.185904 83.185904 0 1 1-83.185904-83.285766 83.185904 83.185904 0 0 1 83.185904 83.285766z" fill="#FFFFFF" p-id="31429"></path><path d="M215.302643 1023.9999a103.258373 103.258373 0 1 1 103.15851-103.15851 103.258373 103.258373 0 0 1-103.15851 103.15851z m0-166.47167a63.313161 63.313161 0 1 0 63.213298 63.31316 63.413024 63.413024 0 0 0-63.213298-63.31316z" fill="#282D33" p-id="31430"></path></svg>,
                                "off": <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="32535" width="32" height="32"><path d="M790.4 742.4c-12.8 12.8-32 12.8-44.8 0s-12.8-32 0-44.8C800 640 832 563.2 832 480c0-176-144-320-320-320S192 304 192 480c0 83.2 32 160 86.4 220.8 12.8 12.8 12.8 32 0 44.8-12.8 12.8-32 12.8-44.8 0C166.4 672 128 579.2 128 480 128 268.8 300.8 96 512 96s384 172.8 384 384c0 99.2-38.4 192-105.6 262.4z" fill="#fa541c" p-id="32536" data-spm-anchor-id="a313x.search_index.0.i24.75303a81B5suLu" ></path><path d="M476.8 915.2l-134.4-134.4c-12.8-12.8-12.8-32 0-44.8s32-12.8 44.8 0l112 112 112-112c12.8-12.8 32-12.8 44.8 0s12.8 32 0 44.8l-134.4 134.4c-9.6 12.8-32 12.8-44.8 0z" fill="#50E3C2" p-id="32537"></path><path d="M476.8 723.2l-134.4-134.4c-12.8-12.8-12.8-32 0-44.8s32-12.8 44.8 0l112 112 112-112c12.8-12.8 32-12.8 44.8 0 12.8 12.8 12.8 32 0 44.8l-134.4 134.4c-9.6 12.8-32 12.8-44.8 0z" fill="#50E3C2" p-id="32538"></path></svg>
                            }[record?.type || 'off']
                        }
                        <div>
                            {
                                record?.desc || '未备注'
                            }
                        </div>
                    </div>
                    <div
                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {
                            (record?.domianRule || []).length == 0 ?
                                "未配置匹配方式"
                                : (record.domainRule.length == 0 && record.domainRule[0] == '*'
                                    ? "通配所有域名" : (<Space>
                                        {
                                            record?.domainRule.map((item, index) => {
                                                return <>
                                                    {
                                                        index > 0
                                                            ? <Divider type="vertical"></Divider>
                                                            : null
                                                    }
                                                    {item}

                                                </>
                                            })
                                        }
                                    </Space>))
                        }
                    </div>
                </div>
            }
        }, {
            title:  wordList["时间"][Context.lan],
            key: 'touchTime',
            dataIndex: 'touchTime',
            width: 100,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    gap: '5px',

                }}>
                    {
                        moment(record.touchTime).format('YYYY/MM/DD HH:mm:ss')
                    }
                </div>
            }
        },
    ]

    return <>

        <CustomCard title={
            wordList["身份验证"][Context.lan]
        }
            extra={
                null
            }
            style={{
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: '100%'
            }}>
            <div style={{
                alignItems: 'center',
                display: 'flex',
                flexWrap: 'wrap',
                width: '100%',
                height: 'auto',
                gap: '10px'
            }}>
                <Space>
                    <span style={{ "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["用户账号"][Context.lan]}</span>
                    <Input style={{ width: '150px' }}></Input>
                    <Divider type="vertical" style={{ marginLeft: 0, marginRight: 0 }}></Divider>
                    <span style={{ "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["认证结果"][Context.lan]}</span>
                    <Select
                        style={{
                            width: '100px'
                        }}
                        options={[
                            {
                                label: wordList["全部"][Context.lan],
                                value: 'all'
                            },
                            {
                                label: wordList["成功"][Context.lan],
                                value: "success"
                            },
                            {
                                label: wordList["失败"][Context.lan],
                                value: 'fail'
                            }
                        ]}></Select>
                    <Divider type="vertical" style={{ marginLeft: 0, marginRight: 0 }}></Divider>
                    <span style={{ "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>IP</span>
                    <Input style={{ width: '150px' }}></Input>
                    <Divider type="vertical" style={{ marginLeft: 0, marginRight: 0 }}></Divider>
                    <span style={{ "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["域名"][Context.lan]}</span>
                    <Input style={{ width: '150px' }}></Input>
                    <Divider type="vertical" style={{ marginLeft: 0, marginRight: 0 }}></Divider>
                    <span style={{ "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["端口"][Context.lan]}</span>
                    <InputNumber style={{ width: '150px', borderRadius: '10px' }}></InputNumber>
                    <Divider type="vertical" style={{ marginLeft: 0, marginRight: 0 }}></Divider>
                    <span style={{ "lineHeight": "30px", "fontSize": "12px", "color": "#999999" }}>{wordList["攻击时间"][Context.lan]}</span>
                    <DatePicker.RangePicker showTime style={{ borderRadius: '10px' }}></DatePicker.RangePicker>
                </Space>
            </div>
            <div style={{
                width: '100%',
                height: "100%",
                marginTop: '20px'
            }}>
                <CustomTable columns={columns}
                    dataSource={authData}
                    loading={false}
                    onChange={() => { }}
                    scroll={{ x: 'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
                    size={'middle'}
                ></CustomTable>



            </div>
        </CustomCard>
    </>
}
export default LogAuth
