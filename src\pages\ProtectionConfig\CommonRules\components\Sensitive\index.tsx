import { Card, Checkbox } from "antd"
import ProviderContext from "../../../../providerContext"
import wordList from "../../../../../config/wordList"
import { useContext } from "react"
const Sensitive = (props: any) => {
    const Context=useContext(ProviderContext)
    return <Card title={
        <div style={{
            fontWeight: 'bold'
        }}>{wordList["敏感词库"][Context.lan]}</div>
    }
        bordered={false}
        style={{
            marginTop:'20px',
            border:'1px solid #ddd',
            borderRadius: '10px',
            width: '100%',
            paddingLeft: 0,
            paddingRight: 0,
            height: 'auto',
        }}
    >
        
    </Card>
}
export default Sensitive