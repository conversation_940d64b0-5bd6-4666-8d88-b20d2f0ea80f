import { Button, Divider, Input, InputNumber, Modal, Radio, Select, Space, Switch } from "antd"
import { useContext, useState } from "react"
import "./index.less"
import ProviderContext from "../../../../providerContext"
import wordList from "../../../../../config/wordList"
const CCProtection = (props: any) => {
    const Context = useContext(ProviderContext)
    const [open, setOpen] = useState(true)
    const [data, setData] = useState({
        frequencyType: "custom",
        times: {
            use: false,
            time: -1,
            action: 0
        },
        attack: {
            use: false,
            time: -1,
            action: 0
        },
        error: {
            use: false,
            time: -1,
            action: 0
        },
    })
    return <>    
    <div className="ccprotection-container">
            <div className="ccprotection-container-item">
                <div className="ccprotection-container-item-title">{wordList["等候室"][Context.lan]}</div>
                <Switch ></Switch>
                <div style={{ color: "#999999" }}> {wordList["开启等候室后，当网站同时访问人数过多时可以起到有效的削峰作用。详情请查看"][Context.lan]}</div>
            </div>
            <div className="ccprotection-container-item" style={{
                marginTop: '20px'
            }}>
                <div className="ccprotection-container-item-title">{wordList["频率限制"][Context.lan]}</div>
                <Radio.Group
                    style={{
                        flexShrink:0
                    }}
                    options={[
                        { label: '跟随全局配置', value: 'global' },
                        { label: '自定义配置', value: 'custom' },
                    ]}
                    onChange={(e) => {
                        setData({
                            ...data,
                            frequencyType: e.target.value
                        })
                    }}
                    value={data.frequencyType}
                    optionType="button"
                    buttonStyle="solid"
                />
                <div style={{ color: "#999999" }}>{wordList["开启频率限制后，严格限制每个 IP 的访问频率，阻止超限的 IP。详情请查看"][Context.lan]}</div>
            </div>
            <br />
            {
                data.frequencyType == 'custom'
                    ? <>
                        <div className="cc-protection-custom">
                            <div className="ccprotection-custom-item">
                                <div className="ccprotection-container-item-title">{wordList["高频访问限制"][Context.lan]}</div>
                                <Switch onChange={(e) => {
                                   
                                    setData({
                                        ...data,
                                        times: {
                                            ...data.times,
                                            use: e
                                        }
                                    })
                                }}></Switch>
                            </div>
                            {

                                data.times.use ?
                                    <>
                                        <Space style={{
                                            gap: '15px'
                                        }}>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">
                                                    {wordList["经过时间"][Context.lan]}
                                                </div>
                                                <InputNumber addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                            </div>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">

                                                    {wordList["请求次数达到"][Context.lan]}
                                                </div>
                                                <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                            </div>

                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">

                                                    {wordList["处理方式"][Context.lan]}
                                                </div>
                                                <Select
                                                    defaultValue="bot"
                                                    style={{ width: '100%' }}
                                                    onChange={() => { }}
                                                    options={[
                                                        {
                                                            value: 'bot',
                                                            label: wordList["人机验证"][Context.lan],
                                                        },
                                                        {
                                                            value: 'ban',
                                                            label: wordList["封禁"][Context.lan],
                                                        },
                                                    ]}
                                                />
                                            </div>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">
                                                    {data.times.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                                </div>
                                                <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                            </div>
                                        </Space>
                                        <Divider orientation="left" style={{ marginTop: '5px', marginBottom: '5px', fontSize: '12px', color: "#999999" }}>{wordList["排除 Content-Type"][Context.lan]}</Divider>
                                        <Select
                                            size="large"
                                            mode="tags"
                                            style={{ width: '100%', borderRadius: '10px' }}
                                            placeholder="Tags Mode"
                                            onChange={(e) => {
                                               
                                            }}
                                            options={[]}
                                        />
                                    </>

                                    : null
                            }
                        </div>
                        <div className="cc-protection-custom">
                            <div className="ccprotection-custom-item">
                                <div className="ccprotection-container-item-title">{wordList["高频攻击限制"][Context.lan]}</div>
                                <Switch onChange={(e) => {
                                   
                                    setData({
                                        ...data,
                                        attack: {
                                            ...data.attack,
                                            use: e
                                        }
                                    })
                                }}></Switch>
                            </div>
                            {

                                data.attack.use ?
                                    <>
                                        <Space style={{
                                            gap: '15px'
                                        }}>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">
                                                    {wordList["经过时间"][Context.lan]}
                                                </div>
                                                <InputNumber addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                            </div>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">

                                                    {wordList["攻击阻断次数达到"][Context.lan]}
                                                </div>
                                                <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                            </div>

                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">

                                                    {wordList["处理方式"][Context.lan]}
                                                </div>
                                                <Select
                                                    defaultValue="bot"
                                                    style={{ width: '100%' }}
                                                    onChange={() => { }}
                                                    options={[
                                                        {
                                                            value: 'bot',
                                                            label: wordList["人机验证"][Context.lan],
                                                        },
                                                        {
                                                            value: 'ban',
                                                            label: wordList["封禁"][Context.lan],
                                                        },
                                                    ]}
                                                />
                                            </div>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">
                                                    {data.times.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                                </div>
                                                <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                            </div>
                                        </Space>
                                    </>

                                    : null
                            }
                        </div>
                        <div className="cc-protection-custom">
                            <div className="ccprotection-custom-item">
                                <div className="ccprotection-container-item-title">{wordList["高频错误限制"][Context.lan]}</div>
                                <Switch onChange={(e) => {
                                   
                                    setData({
                                        ...data,
                                        error: {
                                            ...data.error,
                                            use: e
                                        }
                                    })
                                }}></Switch>
                            </div>
                            {

                                data.error.use ?
                                    <>
                                        <Divider orientation="left"
                                            style={{ marginTop: '5px', marginBottom: '5px', fontSize: '12px', color: "#999999" }}>

                                            {
                                                wordList["状态码"][Context.lan]
                                            }
                                        </Divider>
                                        <Select
                                            size="large"
                                            mode="tags"
                                            style={{ width: '100%', borderRadius: '10px' }}
                                            placeholder="Tags Mode"
                                            onChange={(e) => {
                                               
                                            }}
                                            options={[]}
                                        />
                                        <Space style={{
                                            gap: '15px'
                                        }}>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">

                                                    {

                                                        wordList["经过时间"][Context.lan]
                                                    }
                                                </div>
                                                <InputNumber addonAfter="秒" style={{ width: '100%' }}></InputNumber>
                                            </div>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">
                                                    {
                                                        wordList["错误次数达到"][Context.lan]}
                                                </div>
                                                <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                            </div>

                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">

                                                    {
                                                        wordList["处理方式"][Context.lan]}
                                                </div>
                                                <Select
                                                    defaultValue="bot"
                                                    style={{ width: '100%' }}
                                                    onChange={() => { }}
                                                    options={[
                                                        {
                                                            value: 'bot',
                                                            label: wordList["人机验证"][Context.lan],
                                                        },
                                                        {
                                                            value: 'ban',
                                                            label: wordList["封禁"][Context.lan],
                                                        },
                                                    ]}
                                                />
                                            </div>
                                            <div className="cc-action-card">
                                                <div className="cc-action-card-title">
                                                    {data.times.action == 0 ? wordList["封禁"][Context.lan] : wordList["验证有效期"][Context.lan]}
                                                </div>
                                                <InputNumber addonAfter="次" style={{ width: '100%' }}></InputNumber>
                                            </div>
                                        </Space>
                                    </>

                                    : null
                            }
                        </div>
                    </>
                    : null
            }
        </div>
        </>
    //</Modal>
}
export default CCProtection