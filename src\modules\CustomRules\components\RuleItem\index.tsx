/**AND条件组 */
import { useContext, useEffect, useRef, useState } from "react"
import "./index.less"
import RuleContent from "../RuleContent"
import { <PERSON><PERSON>, Modal } from "antd"
import { PlusOutlined } from "@ant-design/icons"
import ProviderContext from "../../../../pages/providerContext"
import wordList from "../../../../config/wordList"
import BugsLinkButton from "../../../../components/BugsLinkButton"
const RuleItem = (props: any) => {
    const Context=useContext(ProviderContext)
    const [andConditions, setAndConditions] = useState<any>([])
    const isFirst=useRef(true)
    const addCondition = () => {
        let preData=JSON.parse(JSON.stringify([...andConditions.slice()]))
        preData.push({
            key:Math.random().toString(32).slice(2,10),
            k:"src_ip",//源IP
            op:"",//匹配方式
            v:[],
            sub_key:""
        })
        setAndConditions(preData)
    }
    useEffect(()=>{
        setAndConditions(props.preList || [])
    },[])
    const removeCondition = (index) => {
        if(andConditions.length==1){
            //只有一个再删除那就是删除整个条件
            props.onRemove && props.onRemove(true)
            return
        }
        setAndConditions([
            ...andConditions.slice(0, index),
            ...andConditions.slice(index + 1)
        ])
    }
    const confirmRemove=()=>{
        props.onRemove && props.onRemove()
    }
    useEffect(()=>{
        //向父组件更新参数
        if(andConditions.length>0)
            props.updateAndConditions && props.updateAndConditions(andConditions)
    },[andConditions])
    return <div
        style={{
            background: "#f6f8fa",
            borderRadius: '10px',
            padding: '10px',
            boxSizing: 'border-box',
            width:'100%',
            height: 'auto',
            position:'relative',
            ...(props.style || {})
        }}>
         <div style={{
            marginLeft: '-40px',
            position:'absolute',
            top:'50%',
            height: '2px',
            width: '30px',
            background: (!props.isFirst && !props.isEnd) ? '#e3e8ef' : "transparent"
        }}></div>
        <div style={{
            width: '100%',
            display: 'flex',
            height: 'auto'
        }}>
            {/**左侧AND条件链接 */}
            {
                andConditions.length > 1
                    ? <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        marginTop: '30px',
                        maxHeight: '100%'

                    }}>
                        <div style={{
                            zIndex: 2,
                            height: 'calc(100% - 30px)',
                            width: '30px',
                            flexShrink: 0,
                            display: 'flex',
                            marginLeft: '20px',
                            position: 'relative',
                            justifyItems: 'center',
                            borderTopLeftRadius: '5px',
                            borderBottomLeftRadius: '5px',
                            alignItems: 'center',
                            borderTop: '1px solid #e3e8ef',
                            borderBottom: '1px solid #e3e8ef',
                            borderLeft: '1px solid #e3e8ef'
                        }}>
                            <div style={{
                                width: '40px',
                                height: '22px',
                                background: "white",
                                border: '1px solid #ddd',
                                marginLeft: '-20px',
                                lineHeight: '20px',
                                fontSize: '12px',
                                borderRadius: '5px',
                                textAlign: 'center'
                            }}>AND</div>
                        </div>
                        <div style={{
                            height: '30px',
                            flexShrink: 0,
                            width: '20px'
                        }}></div>
                    </div>
                    : null
            }
            <div style={{
                width: '100%',
                height: 'auto',

            }}>
                {
                    andConditions.map((item, index) => {
                        return <RuleContent
                            onRemove={() => {
                                Modal.confirm({
                                    title: wordList["操作确认"][Context.lan],
                                    content: wordList["确认删除此条件"][Context.lan],
                                    onOk: () => {
                                        removeCondition(index)
                                    }
                                })
                            }}
                            onChange={(newConditon)=>{
                                console.log("newCondition",newConditon)
                                setAndConditions([
                                    ...andConditions.slice(0,index),
                                    newConditon,
                                    ...andConditions.slice(index+1)
                                ])
                            }}
                            key={item.key}
                            data={item}
                            isFirst={index == 0} isEnd={index == andConditions.length - 1} ></RuleContent>
                    })
                }

            </div>
        </div>
        <div style={{
            width: 'calc(100% - 60px)',
            marginLeft: '60px',
            marginTop: '20px',
            display: 'flex',
            justifyContent: 'space-between'
        }}>
            <Button onClick={() => {
                addCondition()
                isFirst.current=false
            }} type="primary" style={{ borderRadius: '10px', background: "#cc1212", border: 'none' }} icon={<PlusOutlined></PlusOutlined>}>{ wordList["添加一个AND条件"][Context.lan]}</Button>
            <BugsLinkButton style={{ color: "red" }} onClick={confirmRemove}>{ wordList["删除此条件组"][Context.lan]}</BugsLinkButton>
        </div>
    </div>
}
export default RuleItem