/**AND条件组件 */
import { AutoComplete, Button, Input, Select, Tooltip } from "antd"
import "./index.less"
import { DeleteOutlined } from "@ant-design/icons"
import config from "../../../../config"
import { useContext, useEffect, useRef, useState } from "react"
import IPGroupSelector from "../../../../components/IPGroupSelector"
import APPSelector from "../../../../components/APPSelector"
import IPPlaceSelector from "../../../../components/IPPlaceSelector"
import ProviderContext from "../../../../pages/providerContext"
import wordList from "../../../../config/wordList"
import BugsLinkButton from "../../../../components/BugsLinkButton"
const { customRuleType, ruleTouchType, touchMethod } = config

const RuleContent = (props: any) => {
    const Context = useContext(ProviderContext)
    const [nowTarget, setNowTarget] = useState("")
    const [nowTouchType, setNowTouchType] = useState("")
    const [subKey, setSubKey] = useState("")
    const [value, setValue] = useState([])
    const isFirst = useRef(true)
    const [renderKey, setRenderKey] = useState("none")
    const ALL_Component = (
        <Select
            mode="tags"
            value={value}
            onChange={(e) => {
                updateNewValue(e)
            }}
            style={{ maxWidth: '100%', flex: 1 }}
            placeholder={wordList["输入匹配内容"][Context.lan]}
            options={[]}
        />
    )

    useEffect(() => {
        let data = props.data || {}
        isFirst.current = true
        if (data?.k) {
            setNowTarget(data.k)
        }
        if (data?.op) {
            setNowTouchType(data.op)
            setRenderKey(Math.random().toString(32).slice(2, 10))
        }
        if (data?.sub_k) {
            setSubKey(data.sub_k)
        }
        if (data?.v) {
            setValue(data.v)
        }
    }, [])

    /**更新 */
    const update = (newItems) => {
        props.onChange && props.onChange({
            k: nowTarget,
            op: nowTouchType,
            sub_k: subKey,
            v: value,
            ...newItems
        })
    }
    const updateNewValue = (newValue) => {
        setValue(newValue)
        update({
            v: newValue
        })

    }
    const updateSubKey = (newSubKey) => {
        setSubKey(newSubKey)
        update({
            sub_k: newSubKey
        })
    }

    const updateTouchType = (newTouchType) => {
        setNowTouchType(newTouchType)
        update({
            op: newTouchType
        })
    }
    const updateNewTarget = (newTarget) => {
        setNowTarget(newTarget)
        let nowKey = Object.keys(ruleTouchType[newTarget] || {})[0]

        setNowTouchType(nowKey)
        update({
            k: newTarget,
            op:nowKey
        })
    }
    const speceialLine = {
        src_ip: {
            afterFix: {
                ALL: ALL_Component,
                "ipGroup": <IPGroupSelector></IPGroupSelector>,
                "notIntGroup": <IPGroupSelector></IPGroupSelector>,
                inPlace: <IPPlaceSelector></IPPlaceSelector>,
                notInPlace: <IPPlaceSelector></IPPlaceSelector>
            }
        },
        website: {
            afterFix: {
                ALL: <APPSelector value={value} onChange={(value) => {
                    updateNewValue(value)
                }}></APPSelector>
            }
        },
        uri: {
            afterFix: {
                ALL: ALL_Component
            }
        },
        uri_no_query: {
            afterFix: {
                ALL: ALL_Component
            }
        },
        host: {
            afterFix: {
                ALL: ALL_Component
            }

        },
        req_header: {
            prefix: {
                ALL: <>
                    <div className="rule-item-title">{wordList["参数名"][Context.lan]}</div>
                    <AutoComplete
                        value={subKey}
                        onChange={(e) => {
                            updateSubKey(e)
                        }}
                        style={{ width: '180px', flex: 1 }}
                        options={[
                            {
                                value: "User-Agent"
                            }, {
                                value: "Refer"
                            }, {
                                value: "Cookie"
                            }, {
                                value: "X-Forwarded-For"
                            }, {
                                value: "X-Forwarded-Forasd"
                            }
                        ]}
                        placeholder={wordList["输入参数名"][Context.lan]}
                    /></>
            },
            afterFix: {
                ALL: ALL_Component,
                exist: "-",//不能用Null
                'not_exist': "-"
            }
        },
        get_param: {
            prefix: {
                ALL: <>
                    <div className="rule-item-title">{wordList["参数名"][Context.lan]}</div>
                    <AutoComplete
                        value={subKey}
                        onChange={(e) => {
                            updateSubKey(e)
                        }}
                        style={{ width: '180px', flex: 1 }}
                        options={[

                        ]}
                        placeholder={wordList["输入参数名"][Context.lan]}
                    /></>
            },
            afterFix: {
                ALL: ALL_Component,
                exist: "-",//不能用Null
                'not_exist': "-"
            }
        },
        post_param: {
            prefix: {
                ALL: <>
                    <div className="rule-item-title">{wordList["参数名"][Context.lan]}</div>
                    <AutoComplete
                        value={subKey}
                        onChange={(e) => {
                            updateSubKey(e)
                        }}
                        style={{ width: '180px', flex: 1 }}
                        options={[

                        ]}
                        placeholder={wordList["输入参数名"][Context.lan]}
                    /></>
            },
            afterFix: {
                ALL: ALL_Component,
                exist: "-",//不能用Null
                'not_exist': "-"
            }
        },
        req_body: {
            prefix: {
                ALL: <>
                    <div className="rule-item-title">{wordList["参数名"][Context.lan]}</div>
                    <AutoComplete
                        value={subKey}
                        onChange={(e) => {
                            updateSubKey(e)
                        }}
                        style={{ width: '180px', flex: 1 }}
                        options={[

                        ]}
                        placeholder={wordList["输入参数名"][Context.lan]}
                    /></>
            },
            afterFix: {
                ALL: ALL_Component,
                exist: "-",//不能用Null
                'not_exist': "-"
            }
        }
        ,
        method: {
            afterFix: {
                ALL: <Select
                    mode="tags"
                    style={{ maxWidth: '100%', flex: 1 }}
                    placeholder={wordList["选择请求方法"][Context.lan]}
                    onChange={(e) => {
                        updateNewValue(e)
                    }}
                    value={value}
                    options={touchMethod.map(item => {
                        return {
                            value: item,
                            label: item
                        }
                    })}
                ></Select>,
            }
        }
    }


    return <div
        style={{
            height: 'auto',
            minHeight: '50px',
            display: 'flex',
            width: '100%',
            marginTop: '10px',
            gap: '10px',
            flexShrink: 0,
            justifyContent: !nowTarget ? "flex-start" : "space-between",

            alignItems: 'flex-start',
            position: 'relative',
        }}>
        <div style={{
            marginLeft: '-30px',
            position: "absolute",
            top: '15px',
            height: '2px',
            width: '30px',
            background: (!props.isFirst && !props.isEnd) ? '#e3e8ef' : "transparent"
        }}></div>

        <div className="rule-item-title">{wordList["匹配目标"][Context.lan]}</div>
        <Select style={{
            width: "150px",
            position: "relative",
            flexShrink: 0,
        }}
            value={nowTarget}
            onChange={(e) => {

                updateNewTarget(e)
                isFirst.current = false
            }}
            options={Object.keys(customRuleType).map(item => {
                return {
                    value: item,
                    label: wordList[customRuleType[item]][Context.lan]
                }
            })}></Select>
        {
            /**前缀 */
            speceialLine[nowTarget]?.prefix
                ? (
                    speceialLine[nowTarget]?.prefix[nowTouchType]
                    ||
                    speceialLine[nowTarget]?.prefix.ALL
                )
                : null
        }
        {
            nowTarget
                ? <>

                    <div className="rule-item-title">{wordList["匹配方式"][Context.lan]}</div>
                    <Select
                        value={nowTouchType}
                        onChange={(e) => {
                            console.log(e)
                            updateTouchType(e)
                        }}
                        options={Object.keys(ruleTouchType[nowTarget] || {}).map(item => {
                            return {
                                value: item,
                                label: wordList[ruleTouchType[nowTarget][item]][Context.lan]
                            }
                        })}
                        defaultValue={"eq"}
                        style={{
                            flexShrink: 0,
                            width: "180px",
                            position: "relative"
                        }}></Select>
                    {
                        speceialLine[nowTarget]?.afterFix[nowTouchType] == '-'
                            ? null
                            : <div className="rule-item-title">{wordList["匹配内容"][Context.lan]}</div>
                    }

                    {/*<Select style={{
            width: "150px",
            position: "relative"
        }}></Select>
        */}
                    {
                        speceialLine[nowTarget]?.afterFix
                            ? (
                                speceialLine[nowTarget]?.afterFix[nowTouchType]
                                    ? (speceialLine[nowTarget]?.afterFix[nowTouchType] == '-' ? <></> : speceialLine[nowTarget]?.afterFix[nowTouchType])
                                    : speceialLine[nowTarget]?.afterFix.ALL
                            )
                            : null
                    }</>
                : null
        }

        <div style={{
            width: '50px',
            flexShrink: 0
        }}></div>
        <Tooltip title={wordList["删除此条件"][Context.lan]}>
            <BugsLinkButton onClick={() => {
                props.onRemove && props.onRemove()
            }} style={{ position: 'absolute', right: '10px' }}><DeleteOutlined style={{ color: "#cc1212" }}></DeleteOutlined></BugsLinkButton>
        </Tooltip>

    </div>
}
export default RuleContent