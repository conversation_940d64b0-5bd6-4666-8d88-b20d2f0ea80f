import { useContext, useEffect, useMemo, useRef, useState } from "react"
import "./index.less"
import { Carousel, Empty } from "antd"
import api from "../../../../api"
import moment from "moment"
import wordList from "../../../../config/wordList"
import ProviderContext from "../../../providerContext"
let resizeTimer: any = null
const AttackFrom = () => {
    const Context = useContext(ProviderContext)
    const listContainer = useRef<any>(null)
    const [data, setData] = useState(new Array(Math.floor((listContainer?.current?.offsetHeight || 0) / 100)).fill("EMPTY"))
    const [maxRow, setMaxRow] = useState(7)
    const isFirst = useRef(true)
    useEffect(() => {

        let timeout: any = null
        const get = () => {
            api.record.getLimitList({
                page: 1,
                page_size: 20,
                sort: "-latest_time",
            })
                .then(res => {
                    if (res?.data?.data?.rows) {
                        let rows = res.data.data.rows
                        if (rows.length == 0) return
                        setData(rows)
                        setMaxRow(Math.floor(listContainer.current.offsetHeight / 100))
                    }
                })
            timeout = setTimeout(() => {
                get()
            }, isFirst.current ? 0 : 5000)
            if (isFirst.current) {
                isFirst.current = false
            }
        }
        get()
        const resize = () => {
            clearTimeout(resizeTimer)
            resizeTimer = setTimeout(() => {
                setMaxRow(Math.floor(listContainer.current.offsetHeight / 100))
            }, 200)
        }
        window.addEventListener("resize", resize)
        return () => {
            clearTimeout(timeout)
            window.removeEventListener('resize', resize)
        }

        return
    }, [])
    return <div className="attack-from-bar">
        <div style={{ fontSize: '20px' }}>{wordList["实时监察拦截"][Context.lan]}</div>

        {data.length == 0
            ? <div style={{ height: '100%', width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={'暂无数据'} />
            </div>
            : <div className="attack-from-list" ref={listContainer}

                style={{
                    maxHeight: maxRow > 0 && maxRow >= data.length ? (data.length * 100 + 'px') : "",
                    overflowY: maxRow > 0 && maxRow >= data.length ? "hidden" : "auto"//maxRow大于总数
                }}>
                <Carousel

                    autoplay
                    slidesToShow={maxRow}
                    autoplaySpeed={2000}
                    slidesToScroll={1}
                    verticalSwiping={true}
                    vertical={true} >

                    {
                        data.map((item: any, index) => {
                            if (item == "EMPTY") return <Empty></Empty>
                            return <div className="attack-from-item" key={item?.src_ip || '-'} >
                                <div className="attack-from-ip-placement">
                                    <div>{item?.src_ip || '-'}</div>
                                    <div>{item?.src_country || '内网IP'}{item?.src_subdivision ? ('-' + (item?.src_subdivision || '内网IP')) : ""}</div>
                                </div>
                                <div className="attack-from-ip-text">
                                    <div>{moment(item?.latest_time || '-').format("YYYY/MM/DD HH:mm:ss")}</div>
                                    <div style={{ fontFamily: 'AlimamaShuHeiTi' }}>
                                        <span style={{ color: "#ff2156" }}>{item?.count || '-'}</span>次
                                    </div>
                                </div>
                            </div>
                        })
                    }
                </Carousel>





            </div>}
    </div>
}
export default AttackFrom