import "./index.less"
import LoginBG from "../../assets/login_bg.png"
import { Button, Input, Tabs, Form, message } from "antd"
import { useNavigate } from "react-router-dom"
import logo from "../../assets/logo.png"
import { useState } from "react"
import { LeftCircleFilled } from "@ant-design/icons"
import BugsButton from "../../components/BugsButton"
import api from "../../api"
import md5 from "js-md5"
const Login = (props: any) => {
    const [nowView, setNowView] = useState("login")
    const navigate = useNavigate()
    const [registerForm] = Form.useForm()
    const [inLoging, setInLoging] = useState(false)
    const [loginData, setLoginData] = useState({
        username: "",
        password: ""
    })
    const confirmRegister = () => {
        let { username, password, confirmPassword } = registerForm.getFieldsValue()
        if (password != confirmPassword) {
            message.error("两次输入的密码不一致")
            return
        }
        api.account.register({
            username: username,
            password: md5(password),
            role: 101
        })
            .then(res => {
                if (res?.data?.data?.id) {
                    message.success('注册成功')
                    //发起登录换区code

                }
            })
    }
    const loging = () => {
        setInLoging(true)
        api.account.login({
            username: loginData.username,
            password: md5(loginData.password)
        })
            .then(res => {
                setInLoging(false)
                if (res?.data?.data?.token) {

                    localStorage.setItem("AUTH", res?.data?.data?.token)
                    navigate("/homePage")
                }
            })
    }
    return <div className="login-container">
        <div style={{
            width: "100px",
            height: '100px',
            borderRadius: '50px',
            overflow: "hidden",
            padding: "10px",
            boxShadow: "0 0px 10px rgba(0,0,0,0.15)"
        }}>
            <img src={logo} style={{ width: "100%", height: '100%' }} />
        </div>
        <div style={{
            fontSize: '42px',
            letterSpacing: "3px",

            fontWeight: '400',
            fontFamily: "AlimamaShuHeiTi"
        }}>登录漏洞卫士监察防御系统</div>
        <div className="login-info">
            <div style={{
                width: '100%',
                height: "auto"
            }}>
                <div className="phone-input">
                    <div style={{ fontWeight: 'bold' }}>账号</div>
                    <Input
                        onPressEnter={()=>{
                            loging()
                        }}
                        placeholder="输入账号"
                        onChange={(e) => {
                            setLoginData({
                                ...loginData,
                                username: e.target.value
                            })
                        }}
                        value={loginData.username}
                        style={{
                            height: '50px',
                            lineHeight: '50px',
                            borderRadius: '10px !important',
                            background: "#fff"
                        }}
                    />
                </div>

                <div className="phone-input" style={{
                    marginTop: '30px',
                }}>
                    <div style={{ fontWeight: 'bold' }}>密码</div>
                    <Input.Password
                        onPressEnter={()=>{
                            loging()
                        }}
                        placeholder="输入密码"
                        onChange={(e) => {
                            setLoginData({
                                ...loginData,
                                password: e.target.value
                            })
                        }}
                        value={loginData.password}
                        style={{
                            height: '50px',
                            lineHeight: '50px',
                            borderRadius: '10px',
                            background: "#fff"
                        }}
                    />
                </div>
                <Button size="large" type="primary"
                    loading={inLoging}
                    onClick={() => {
                        loging()
                    }}
                    style={{
                        height: '50px',
                        marginTop: '30px',
                        width: '100%',
                        borderRadius: '10px',
                        background: "#cc1212",
                        border: 'none'
                    }}>登录</Button>

            </div>
        </div>
    </div>
}
export default Login