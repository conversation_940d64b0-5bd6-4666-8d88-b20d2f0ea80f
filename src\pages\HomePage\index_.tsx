/**主页 */
import { Divider, message, Progress, Table, Tag } from "antd"
import ProviderContext from "../providerContext"
import wordList from "../../config/wordList"
import "./index.less"
import React, { useContext, useEffect, useRef, useState } from "react"
import type { ColumnsType } from 'antd/es/table';
import LineChart from "./components/LineChart"
import ViewPageList from "../../modules/ViewPageList"
import TimeRangeSelector from "../../components/TimeRangeSelector"
import CustomTable from "../../components/CustomTable"

const { CheckableTag } = Tag;

interface DataType {
    rank: number;
    ip: string;
    times: number;
    placement: string;
}

function getTimeAndWeekday() {
    const date = new Date();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const weekday = days[date.getDay()];
  
    return {
        time:`${hours}:${minutes}:${seconds}`,
        weekday
    };
  }
let updateTimer:any=null
const HomePage = () => {
    const dividerStyle: React.CSSProperties =
    {
        marginLeft: '0px',
        marginRight: '0px',
        width: '2px',
        height: '30px',
        borderWidth: '2px',
    }
    const Context = useContext(ProviderContext)
    const [viewIp, setViewIp] = useState<DataType[]>([{
        rank: 1,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 2,
        ip: "*********",
        placement: "美国",
        times: 30
    }, {
        rank: 3,
        ip: "*********",
        placement: "德国",
        times: 30
    }, {
        rank: 4,
        ip: "*********",
        placement: "日本",
        times: 30
    }, {
        rank: 5,
        ip: "*********",
        placement: "俄罗斯",
        times: 30
    }, {
        rank: 6,
        ip: "*********",
        placement: "韩国",
        times: 30
    }, {
        rank: 7,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 8,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 9,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 10,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }])

    const [attackIP, setAttackIp] = useState<DataType[]>([{
        rank: 1,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 2,
        ip: "*********",
        placement: "美国",
        times: 30
    }, {
        rank: 3,
        ip: "*********",
        placement: "德国",
        times: 30
    }, {
        rank: 4,
        ip: "*********",
        placement: "日本",
        times: 30
    }, {
        rank: 5,
        ip: "*********",
        placement: "俄罗斯",
        times: 30
    }, {
        rank: 6,
        ip: "*********",
        placement: "韩国",
        times: 30
    }, {
        rank: 7,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 8,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 9,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 10,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }])

    const IpColumns_1: ColumnsType<DataType> = [
        {
            title: wordList['排名'][Context.lan],
            dataIndex: 'rank',
            align: 'center',
            key: 'rank',
            render: (rank: number) => {
                if (rank == 1) {
                    return <span style={{ color: "#f4424d" }}>1</span>
                }
                if (rank == 2) {
                    return <span style={{ color: "#feab12" }}>2</span>
                }
                if (rank == 3) {
                    return <span style={{ color: "#cc1212" }}>3</span>
                }
                return <span style={{ color: "#999" }}>{rank}</span>
            },
        },
        {
            title: wordList['访问IP'][Context.lan],
            dataIndex: 'ip',
            align: 'center',
            key: 'ip',
        },
        {
            title:  wordList['归属地'][Context.lan],
            dataIndex: 'placement',
            key: 'placement',
            align: 'center',
        },
        {
            title:  wordList['访问次数'][Context.lan],
            key: 'times',
            align: 'center',
            dataIndex: 'times',
        },
    ];

    const IpColumns_2: ColumnsType<DataType> = [
        {
            title:  wordList['排名'][Context.lan],
            dataIndex: 'rank',
            align: 'center',
            key: 'rank',
            render: (rank: number) => {
                if (rank == 1) {
                    return <span style={{ color: "#f4424d" }}>1</span>
                }
                if (rank == 2) {
                    return <span style={{ color: "#feab12" }}>2</span>
                }
                if (rank == 3) {
                    return <span style={{ color: "#cc1212" }}>3</span>
                }
                return <span style={{ color: "#999" }}>{rank}</span>
            },
        },
        {
            title:   wordList['攻击IP'][Context.lan],
            align: 'center',
            dataIndex: 'ip',
            key: 'ip',
        },
        {
            title:   wordList['归属地'][Context.lan],
            dataIndex: 'placement',
            align: 'center',
            key: 'placement',
        },
        {
            title:   wordList['攻击次数'][Context.lan],
            key: 'times',
            align: 'center',
            dataIndex: 'times',
        },
    ];
    const setViewDomain = useRef(false)
    const [viewDomainList, setViewDomainList] = useState(([
        {
            domain: "www.bugbank.cn",
            times: 1000,
            percent: 0
        },
        {
            domain: "www.bugbank.cn",
            times: 970,
            percent: 0
        },
        {
            domain: "www.bugbank.cn",
            times: 850,
            percent: 0
        },
        {
            domain: "www.bugbank.cn",
            times: 770,
            percent: 0
        },
        {
            domain: "www.bugbank.cn",
            times: 370,
            percent: 0
        }, {
            domain: "www.bugbank.cn",
            times: 100,
            percent: 0
        }
    ]))

    useEffect(()=>{
        updateTimer=setInterval(()=>{
            let node:any=document.getElementById("nowTime")
            if(node){
                node.innerText=getTimeAndWeekday().time
            }
        },1000)
        return ()=>{
            clearInterval(updateTimer)
        }
    },[])
    useEffect(() => {
        //拦截，防止死循环
        if (setViewDomain.current) {
            setViewDomain.current = false
            return
        }
        let preDomainList: any = [...viewDomainList.slice()]
        setViewDomain.current = true
        let maxNums = Math.max(...preDomainList.map(item => item.times))
        preDomainList = preDomainList.map(item => {
            return {
                ...item,
                percent: parseFloat((item.times / maxNums).toFixed(2)) * 100
            }
        })
        setViewDomainList(preDomainList)
    }, [viewDomainList])
    console.log(getTimeAndWeekday().weekday)
    return <div className="home-page-container">
        <div className="home-page-container-top">
            <div className="home-page-top-overview-container">
                <div className="home-page-card-title">{wordList['访问概况'][Context.lan]}</div>
                <div style={{
                    width: '100%',
                    display: 'flex',
                    gap: '5px',
                    alignItems: 'center'
                }}>
                    <div className="home-page-overview-item" style={{ marginLeft: '5%' }}>
                        <div className="home-page-overview-item-title">{wordList['访问次数'][Context.lan]}</div>
                        <div className="home-page-overview-item-content">36.4K</div>
                    </div>
                    <Divider  type="vertical" style={dividerStyle}></Divider>

                    <div className="home-page-overview-item">
                        <div className="home-page-overview-item-title">{wordList['独立访客'][Context.lan]}</div>
                        <div className="home-page-overview-item-content">126</div>
                    </div>
                    <Divider  type="vertical" style={dividerStyle}></Divider>

                    <div className="home-page-overview-item">
                        <div className="home-page-overview-item-title">{wordList['独立IP'][Context.lan]}</div>
                        <div className="home-page-overview-item-content">86</div>
                    </div>
                    <Divider type="vertical" style={dividerStyle}></Divider>

                    <div className="home-page-overview-item">
                        <div className="home-page-overview-item-title">{wordList['来源国家'][Context.lan]}</div>
                        <div className="home-page-overview-item-content">2</div>
                    </div>
                </div>
            </div>
            <div className="home-page-top-overview-time">
                {/**时间阶段选择器 */}
                <TimeRangeSelector ></TimeRangeSelector>
                <div className="home-page-top-time-date">
                    <div id="nowTime">{getTimeAndWeekday().time}</div>

                    <div>{wordList[getTimeAndWeekday().weekday][Context.lan]}</div>

                </div>
            </div>
        </div>
        <div className="home-page-container-center">
            <div className="home-page-ip-list">
                <div className="home-page-card-title">{wordList['访问&攻击 IP 前十'][Context.lan]}</div>
                <div
                className="home-page-ip-list-layout"
                >
                    <div
                        className="home-page-table-container"
                        >
                        <CustomTable
                            size="small"
                            
                            rowClassName={()=>"home-page-table-row"}
                            style={{
                                border: 'none',
                                width: '100%'
                            }}
                            dataSource={viewIp}
                            columns={IpColumns_1}
                            pagination={
                                {
                                    hideOnSinglePage: true
                                }
                            }
                        ></CustomTable>
                    </div>

                    <Divider
                        type="vertical"
                        style={{
                            ...dividerStyle,
                            height: '100%',
                            marginLeft: '10px',
                            marginRight: '10px',
                            width: '2px',
                            flexShrink: 0,
                        }}></Divider>
                    <div
                        className="home-page-table-container">
                        <CustomTable
                            size="small"
                            rowClassName={()=>"home-page-table-row"}
                            style={{
                                border: 'none',
                                width: '100%'
                            }}
                            dataSource={attackIP}
                            columns={IpColumns_2}
                            pagination={
                                {
                                    hideOnSinglePage: true
                                }
                            }
                        ></CustomTable>
                    </div>


                </div>
            </div>
            <div
            className="attack-overview"
             >
                <div className="home-page-attack-overview">
                    
                    <div className="home-page-card-title">{wordList['攻击概览'][Context.lan]}</div>
                    <div className="attack-overview-container">
                        <div className="attack-overview-item">
                            <div style={{
                                fontSize:'12px'
                            }}>{wordList['拦截次数'][Context.lan]}</div>
                            <div style={{fontSize:'24px'}}>12.8K</div>
                        </div>
                        <div className="attack-overview-item">
                            <div style={{
                                fontSize:'12px'
                            }}>{wordList['攻击IP'][Context.lan]}</div>
                            <div style={{fontSize:'24px'}}>24</div>
                        </div>
                        <div className="attack-overview-item">
                            <div style={{
                                fontSize:'12px'
                            }}>{wordList['受攻击站点'][Context.lan]}</div>
                            <div style={{fontSize:'24px'}}>3</div>
                        </div>
                    </div>
                </div>
                <div className="home-page-ip-chart">

                    <div className="home-page-card-title">{wordList['拦截&访问分析'][Context.lan]}</div>

                    <LineChart></LineChart>
                </div>
            </div>

        </div>
        <div className="home-page-container-bottom">
            <div className="home-page-domain-list">
                <div className="home-page-card-title">{wordList['受访域名'][Context.lan]}</div>
                <div className="progress-list-container">
                    {
                        viewDomainList.map(item => {
                            return <div className="progress-list-item">
                                <div className="progress-list-item-text-conainer">
                                    <div className="progress-list-item-text-title">{item.domain}</div>
                                    <div className="progress-list-item-text-data">{item.times}</div>
                                </div>

                                <Progress percent={item.percent} size="small" style={{ width: '100%' }} strokeColor={"#cc1212"} showInfo={false} />
                            </div>
                        })
                    }


                </div>
            </div>
            <ViewPageList></ViewPageList>
        </div>
    </div>
}
export default HomePage