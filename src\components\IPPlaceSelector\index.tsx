import { Cascader, Select } from "antd"
import config from "../../config";
import ProviderContext from "../../pages/providerContext";
import wordList from "../../config/wordList";
import { useContext } from "react";
const {placementNames} =config
const { SHOW_CHILD } = Cascader;
/**IP地理位置选择器 */
const IPPlaceSelector=(props:any)=>{
    const Context=useContext(ProviderContext)
    return <Cascader
    style={{ maxWidth: '100%',flex:1 }}
    options={[
        {
            label: wordList["境内"][Context.lan],
            value:"china",
            children:placementNames.province.map(item=>{
                return {
                    value:item.value,
                    label:Context.lan=='en'?item.name_en:item.label
                }
            })
        },
        {
            label: wordList["境外/港澳台"][Context.lan],
            value:"other",
            children: placementNames.other.map(item=>{
                return {
                    value:item.value,
                    label:Context.lan=='en'?item.name_en:item.label
                }
            })
        },
        {
            label: wordList["全球"][Context.lan],
            value:'global',
            children:placementNames.countries.map(item=>{
                return {
                    value:item.value,
                    label:Context.lan=='en'?item.name_en:item.label
                }
            })
        }
    ]}
    onChange={()=>{}}
    multiple
    placeholder={wordList["选择IP地理位置"][Context.lan]}
    maxTagCount="responsive"
  />
}
export default IPPlaceSelector