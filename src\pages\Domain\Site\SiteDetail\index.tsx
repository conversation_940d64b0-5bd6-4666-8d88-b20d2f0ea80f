import { <PERSON><PERSON>, Card, Descriptions, Divider, Modal, Space, Tag, Form, Input, Select, InputNumber, Switch, Radio, Checkbox, Tooltip, message, Popconfirm, Spin } from "antd"
import "./index.less"
import wordList from "../../../../config/wordList"
import ProviderContext from "../../../providerContext"
import { useContext, useEffect, useState } from "react"
import moment from "moment"
import { DeleteOutlined, EditOutlined, PlusOutlined, SyncOutlined } from "@ant-design/icons"
import CCProtection from "../components/CCProtection"
import BOTProtection from "../components/BOTProtection"
import AuthProtection from "../components/AuthProtection"
import BugsButton from "../../../../components/BugsButton"
import LineChart from "../components/LineChart"
import tools from "../../../../tools"
import { useNavigate, useParams } from "react-router-dom"
import api from "../../../../api"
import config from "../../../../config"
import TextArea from "antd/lib/input/TextArea"
import SiteFrequency from "../components/Frequency"
import BugsLinkButton from "../../../../components/BugsLinkButton"
import BindLicense from "../components/BindLicense"
const ErrorStatusIcon = <svg viewBox="0 0 1191 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23583" width="128" height="128">
    <path d="M10.191983 927.292832c-5.631168 0-10.191949-4.560781-10.191949-10.145411s4.560781-10.14541 10.191949-10.14541h890.283027c5.631168 0 10.191949 4.560781 10.191949 10.14541s-4.560781 10.14541-10.191949 10.145411H10.191983z m941.149694 0c-5.631168 0-10.191949-4.560781-10.191949-10.145411s4.560781-10.14541 10.191949-10.14541h31.78585c5.631168 0 10.191949 4.560781 10.191949 10.14541s-4.560781 10.14541-10.191949 10.145411h-31.78585z m114.484906 0c-5.631168 0-10.191949-4.560781-10.191949-10.145411s4.560781-10.14541 10.191949-10.14541h114.438367c5.631168 0 10.191949 4.560781 10.191949 10.14541s-4.560781 10.14541-10.191949 10.145411h-114.438367zM302.733494 1022.464227c-3.630009 0-6.980787-1.908082-8.795791-5.072705-1.815005-3.118085-1.815005-7.027326 0-10.145411 1.815005-3.118085 5.165782-5.072705 8.795791-5.072705h89.028303c3.630009 0 6.980787 1.95462 8.795791 5.072705 1.815005 3.118085 1.815005 7.027326 0 10.145411-1.815005 3.164623-5.165782 5.072705-8.795791 5.072705H302.733494z m146.270756 0c-3.630009 0-6.980787-1.908082-8.795792-5.072705-1.815005-3.118085-1.815005-7.027326 0-10.145411 1.815005-3.118085 5.165782-5.072705 8.795792-5.072705h387.89906c3.630009 0 6.980787 1.95462 8.795792 5.072705 1.815005 3.118085 1.815005 7.027326 0 10.145411-1.815005 3.164623-5.165782 5.072705-8.795792 5.072705H449.00425z m568.468749-352.669356c0-13.542727 20.337359-13.542727 20.337359 0v38.068558a10.191949 10.191949 0 0 1-10.14541 10.145411h-38.161636c-13.542727 0-13.542727-20.290821 0-20.290821h27.969687v-27.923148z m20.383897 76.137116c0 13.542727-20.337359 13.542727-20.337359 0v-38.068558c0-2.699238 1.070387-5.258859 2.978469-7.166941 1.908082-1.908082 4.514242-2.978469 7.21348-2.978469h38.161635c13.589265 0 13.589265 20.290821 0 20.290821h-27.969686v27.923147zM50.91324 365.339484c0-13.542727 20.337359-13.542727 20.33736 0v38.068558c0 5.58463-4.560781 10.14541-10.191949 10.14541h-38.161635c-13.589265 0-13.589265-20.290821 0-20.29082h27.969686v-27.923148z m942.406236-320.930044h34.345472c13.542727 0 13.542727 20.290821 0 20.29082h-34.345472v34.252395c0 13.542727-20.337359 13.542727-20.337359 0v-34.252395h-34.345472c-13.542727 0-13.542727-20.290821 0-20.29082h34.345472V10.157045c0-13.542727 20.337359-13.542727 20.337359 0v34.252395zM71.2506 441.4766c0 13.542727-20.337359 13.542727-20.33736 0v-38.068558c0-5.58463 4.560781-10.14541 10.191949-10.14541h38.161636c13.542727 0 13.542727 20.290821 0 20.29082h-27.969687v27.923148z m0 0" fill="#333745" p-id="23584"></path>
    <path d="M179.266643 775.623601c6.003477-6.003477 14.985423 2.978469 8.981946 8.981946l-13.496188 13.449649c-1.210003 1.210003-2.792315 1.861543-4.514243 1.861544-1.675389 0-3.304239-0.65154-4.514242-1.861544l-13.496188-13.449649c-6.003477-6.003477 2.978469-14.938884 8.981946-8.981946l8.981946 8.981946 9.075023-8.981946z m-17.963892 35.881245c-6.003477 6.003477-14.985423-2.978469-8.981946-8.981946l13.496188-13.449649c1.210003-1.210003 2.792315-1.861543 4.514242-1.861544 1.675389 0 3.304239 0.65154 4.514243 1.861544l13.496188 13.449649c6.003477 6.003477-2.978469 14.938884-8.981946 8.981946l-8.981946-8.981946-9.075023 8.981946zM291.564235 111.657687c6.003477-6.003477 14.985423 2.978469 8.981946 8.981946l-13.496188 13.449649c-1.210003 1.210003-2.792315 1.861543-4.514242 1.861543-1.675389 0-3.304239-0.65154-4.514243-1.861543l-13.496188-13.449649c-6.003477-6.003477 2.978469-14.938884 8.981946-8.981946l8.981946 8.981946 9.075023-8.981946z m-18.01043 35.881245c-6.003477 5.956938-14.985423-2.978469-8.981946-8.981946l13.496188-13.44965c1.210003-1.210003 2.792315-1.861543 4.514242-1.861543 1.675389 0 3.304239 0.65154 4.514242 1.861543l13.496189 13.44965c6.003477 5.956938-2.978469 14.938884-8.981946 8.981946l-8.981946-8.981946-9.075023 8.981946z m0 0" fill="#657180" p-id="23585"></path>
    <path d="M391.761797 416.113074h381.569813c21.081977 0 38.161635 17.03312 38.161636 38.068558v291.750355c0 21.035438-17.079659 38.068558-38.161636 38.068558H391.761797c-21.081977 0-38.161635-17.03312-38.161635-38.068558v-291.796893c0-20.988899 17.079659-38.022019 38.161635-38.02202z m0 0" fill="#9EA7B4" p-id="23586"></path>
    <path d="M315.438526 416.113074h534.169816c21.081977 0 38.161635 17.03312 38.161635 38.068558v88.79561c0 21.035438-17.079659 38.068558-38.161635 38.068558H315.438526c-21.081977 0-38.161635-17.03312-38.161635-38.068558V454.135094c0-20.988899 17.079659-38.022019 38.161635-38.02202z m0 215.613239h534.169816c21.081977 0 38.161635 17.03312 38.161635 38.068558v88.79561c0 21.035438-17.079659 38.068558-38.161635 38.068558H315.438526c-21.081977 0-38.161635-17.03312-38.161635-38.068558v-88.79561c0-20.988899 17.079659-38.068558 38.161635-38.068558z m0 0" fill="#f4424d" p-id="23587" data-spm-anchor-id="a313x.search_index.0.i19.4eb23a81hSiSIO" ></path>
    <path d="M474.414314 530.27221c-11.355413 0-21.873132-6.050015-27.550839-15.869656a31.70208 31.70208 0 0 1 0-31.739311 31.832388 31.832388 0 0 1 27.550839-15.869656c17.545044 0 31.78585 14.194267 31.78585 31.692773s-14.194267 31.78585-31.78585 31.78585z m-101.733335 0c-11.355413 0-21.873132-6.050015-27.550839-15.869656a31.70208 31.70208 0 0 1 0-31.739311 31.832388 31.832388 0 0 1 27.550839-15.869656c17.545044 0 31.78585 14.194267 31.78585 31.692773s-14.240805 31.78585-31.78585 31.78585z m101.733335 215.659777c-17.545044 0-31.78585-14.194267-31.78585-31.692772s14.240805-31.692773 31.78585-31.692773 31.78585 14.194267 31.78585 31.692773-14.194267 31.692773-31.78585 31.692772z m-101.733335 0c-17.545044 0-31.78585-14.194267-31.78585-31.692772s14.240805-31.692773 31.78585-31.692773 31.78585 14.194267 31.78585 31.692773-14.240805 31.692773-31.78585 31.692772z m0 0" fill="#FCFFE7" p-id="23588" data-spm-anchor-id="a313x.search_index.0.i18.4eb23a81hSiSIO"></path>
    <path d="M881.114961 243.59456l152.088077 221.570177c8.004636 11.634645 8.888869 26.759683 2.280391 39.232023a38.189558 38.189558 0 0 1-33.74047 20.29082h-304.176156c-14.147728 0-27.131992-7.818481-33.74047-20.29082a37.914981 37.914981 0 0 1 2.28039-39.232023l152.088078-221.570177a38.175597 38.175597 0 0 1 31.506618-16.567734c12.518878 0.046539 24.293139 6.23617 31.413542 16.567734z m0 0" fill="#FFDB00" p-id="23589"></path><path d="M879.16034 340.953268l-10.610796 72.320953a12.728302 12.728302 0 0 1-12.611955 10.843489h-7.073864c-6.329247 0-11.681183-4.607319-12.611955-10.843489l-10.610796-72.320953c-1.070387-7.306557 1.070387-14.659653 5.910399-20.244282a25.465911 25.465911 0 0 1 19.220434-8.795791h3.118085c7.399634 0 14.42696 3.211162 19.220433 8.795791a24.949332 24.949332 0 0 1 6.050015 20.244282z m-25.596219 131.518026h-1.489234c-9.447332 0-17.126197-7.678866-17.126197-17.079659 0-9.447332 7.678866-17.079659 17.126197-17.079658h1.489234c9.447332 0 17.126197 7.632327 17.126198 17.079658 0 9.400793-7.678866 17.079659-17.126198 17.079659z m0 0" fill="#333745" p-id="23590"></path></svg>
const HelthyStatusIcon = <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12449" width="48" height="48"><path d="M428.256 42.656h510.4A85.344 85.344 0 0 1 1024 128v85.344a85.344 85.344 0 0 1-85.344 85.312H85.344A85.344 85.344 0 0 1 0 213.344V128a85.344 85.344 0 0 1 85.344-85.344h83.744v128H256v-128h85.344v128h86.912v-128z m0 341.344h510.4A85.344 85.344 0 0 1 1024 469.344v85.312c0 23.52-9.504 44.8-24.864 60.224A272 272 0 0 0 608.608 640H85.344A85.344 85.344 0 0 1 0 554.656v-85.312A85.344 85.344 0 0 1 85.344 384h83.744v128H256v-128h85.344v128h86.912v-128z m131.296 341.344h-131.296v128h-86.912v-128H256v128H169.088v-128H85.344A85.344 85.344 0 0 0 0 810.656V896a85.344 85.344 0 0 0 85.344 85.344H600a272 272 0 0 1-40.48-256z" fill="#4E5969" p-id="12450"></path><path d="M816 608c-113.6 0-208 94.4-208 208s94.4 208 208 208 208-94.4 208-208-94.4-208-208-208z m129.408 125.888a24.992 24.992 0 0 0-35.36 0l-128 128-60.096-60.096a24.992 24.992 0 1 0-35.36 35.328l77.792 77.792a24.992 24.992 0 0 0 35.36 0l145.664-145.664a24.992 24.992 0 0 0 0-35.36z" fill="#52C41A" p-id="12451"></path></svg>
/**网站详情 */
const { CheckableTag } = Tag



const EditBaseInfo = (props: any) => {
    const Context = useContext(ProviderContext)
    const [open, setOpen] = useState(true)
    const [renderKey, setRenderKey] = useState("none")
    const [form] = Form.useForm()
    const [listeningPort, setListeningPort] = useState<any>([])
    useEffect(() => {
        let data = { ...props.data }
        form.setFieldsValue({
            ...data,
        })
        setListeningPort((props?.data?.ports || []).map(item => {
            let ports = item.split('_')
            let useSSL = ports.includes("ssl")
            let port = parseInt(ports[0])
            return {
                port: port,
                useSSL,
            }
        }))
        setRenderKey(Math.random().toString(32).slice(2, 10))
    }, [props])
    const onSave = () => {
        let data = form.getFieldsValue()
        let ports = listeningPort.map(item => {
            if (item.useSSL) return `${item.port}_ssl`
            return item.port
        })
        let saveData: any = {
            description: data.description,
            server_names: data.server_names,
            type: data.type,
            title: data.title,
            ports: ports,
            mode: data.mode,


        }

        if (data['source_ip_config.headers']) {
            if (!saveData.source_ip_config) {
                saveData.source_ip_config = {}
            }
            saveData.source_ip_config.headers = data['source_ip_config.headers']

        }
        if (!isNaN(data['source_ip_config.index'])) {
            if (!saveData.source_ip_config) {
                saveData.source_ip_config = {}
            }
            saveData.source_ip_config.index = data['source_ip_config.index']

        }
        if (data['source_ip_config.trust_proxies']) {
            if (!saveData.source_ip_config) {
                saveData.source_ip_config = {}
            }
            saveData.source_ip_config.trust_proxies = data['source_ip_config.trust_proxies']

        }
        props.saveSite && props.saveSite(
            saveData
            , () => {
                setOpen(false)
                message.success(wordList['修改成功'][Context.lan])
            }
        )
    }
    return <Modal
        className="site-detail-modal"
        open={open}
        afterClose={() => {
            props.close && props.close()
        }}
        title={wordList['编辑站点'][Context.lan]}
        footer={
            <Space>
                <BugsLinkButton style={{ color: "#cc1212" }} onClick={() => { setOpen(false) }}>{wordList['取消'][Context.lan]}</BugsLinkButton>
                <BugsButton style={{ background: "#cc1212" }} onClick={() => { onSave() }}>{wordList['保存'][Context.lan]}</BugsButton>
            </Space>
        }
        width={800}
        onCancel={() => {
            setOpen(false)
        }}>
        <div style={{
            maxHeight: 'calc(100vh - 300px)',
            padding: '10px',
            boxSizing: "border-box",
            overflowY: 'auto'
        }}>
            {/**站点名称、匹配规则、监听端口、后端端口 后端路由 运行模式*/}
            <Form form={form} key={renderKey} >
                <Form.Item
                    label={wordList['站点名称'][Context.lan]}
                    name="title"
                    required
                >
                    <Input style={{ width: '100%' }} placeholder={wordList['请输入站点名称'][Context.lan]}></Input>
                </Form.Item>
                <Form.Item
                    label={wordList['匹配规则'][Context.lan]}
                    name="server_names"
                    required

                >
                    <Select
                        size="middle"
                        mode="tags"
                        style={{ width: '100%', borderRadius: '10px' }}
                        placeholder={wordList['请输入域名匹配规则'][Context.lan]}
                        onChange={(e) => {

                        }}
                        options={[]}
                    />
                </Form.Item>
                <Form.Item
                    label={wordList['监听端口'][Context.lan]}
                    required
                    name="port"
                >
                    {
                        listeningPort.map((item: { port: number, useSSL: boolean }, index) => {
                            return <div
                                style={{
                                    width: '100%',
                                    height: "30px",
                                    marginBottom: '20px',
                                    display: 'flex',
                                    gap: '10px',
                                    alignItems: 'center'
                                }}
                            >
                                <InputNumber
                                    onChange={(e) => {
                                        setListeningPort([
                                            ...listeningPort.slice(0, index),
                                            {
                                                ...listeningPort[index],
                                                port: e
                                            },
                                            ...listeningPort.slice(index + 1)
                                        ])
                                    }}
                                    style={{ width: '100%' }}
                                    value={item.port}></InputNumber>
                                <Checkbox onChange={(e) => {
                                    setListeningPort([
                                        ...listeningPort.slice(0, index),
                                        {
                                            ...listeningPort[index],
                                            useSSL: e.target.checked
                                        },
                                        ...listeningPort.slice(index + 1)
                                    ])

                                }} checked={item.useSSL} style={{ flexShrink: 0 }}>SSL</Checkbox>
                                <Popconfirm
                                    title={wordList['确认删除此端口'][Context.lan]}
                                    onConfirm={() => {
                                        let tempPort = JSON.parse(JSON.stringify([
                                            ...listeningPort.slice(0, index),
                                            ...listeningPort.slice(index + 1)
                                        ]))
                                        setListeningPort(tempPort)
                                    }}
                                >
                                    <DeleteOutlined style={{ cursor: "pointer", flexShrink: 0 }}></DeleteOutlined>
                                </Popconfirm>
                            </div>
                        })
                    }
                    <BugsLinkButton style={{ color: "#cc1212" }} icon={<PlusOutlined></PlusOutlined>} onClick={() => {
                        for (let i = 0; i < listeningPort.length; i++) {
                            if (!listeningPort[i].port) {
                                message.error(wordList['存在未填写端口的项，无法新增'][Context.lan])
                                return
                            }
                        }
                        setListeningPort([
                            ...listeningPort,
                            {
                                port: "",
                                useSSL: false
                            }
                        ])
                    }}>{wordList['添加一个监听端口'][Context.lan]}</BugsLinkButton>
                </Form.Item>
                {/*<Form.Item
                    label="启用SSL"
                    name="useSSL"
                >
                    <Switch checkedChildren="启用" defaultChecked={form.getFieldsValue()['useSSL']} unCheckedChildren="未启用"></Switch>
                </Form.Item>
                <Form.Item
                label="SSL证书"
                name='license'
            >
                <Select
                    size="middle"
                    style={{ width: '100%', borderRadius: '10px' }}
                    placeholder="请选择SSL证书"
                    onChange={(e) => {
                       
                    }}
                    options={[]}
                />
            </Form.Item>
                <Form.Item
                    label="SSL端口"
                    name="sslPort"
                >
                    <InputNumber style={{ width: '100%' }}></InputNumber>
                </Form.Item>*/}
                {/*<Form.Item
                label="后端路由"
                name="backendPath"
            >
                <Input style={{ width: '100%' }} placeholder="请输入后端路由" defaultValue={"/api"}></Input>
            </Form.Item>*/}
                <Form.Item
                    label={wordList['站点类型'][Context.lan]}
                    name="type"
                    required
                >
                    <Radio.Group defaultValue={0} buttonStyle="solid">
                        <Radio.Button value={0}>{wordList['反向代理'][Context.lan]}</Radio.Button>
                    </Radio.Group>
                </Form.Item>
                <Form.Item
                    label={wordList['运行模式'][Context.lan]}
                    name="mode"
                    required
                >
                    <Radio.Group defaultValue={0} buttonStyle="solid">
                        <Radio.Button value={0}>{wordList['防护'][Context.lan]}</Radio.Button>
                        <Radio.Button value={1}>{wordList['观察'][Context.lan]}</Radio.Button>
                        <Radio.Button value={2}>{wordList['维护'][Context.lan]}</Radio.Button>
                    </Radio.Group>
                </Form.Item>
                <Form.Item
                    label='IP Header'
                    name="source_ip_config.headers"
                    initialValue={props.data?.source_ip_config?.headers || []}

                >
                    <Select
                        size="middle"
                        mode="tags"
                        dropdownStyle={
                            {
                                display: 'none'
                            }
                        }
                        style={{ width: '100%', borderRadius: '10px' }}
                        placeholder={'获取源IP的headers'}
                        onChange={(e) => {

                        }}
                        options={[]}
                    />
                </Form.Item>
                <Form.Item
                    label={"Index"}

                    name="source_ip_config.index"
                    initialValue={props.data?.source_ip_config?.index ?? ""}
                >
                    <InputNumber
                        controls={false}
                        placeholder="获取源IP的Header的索引，0为左边第一个，-1为右边第一个"
                        style={{ width: '100%', borderRadius: '5px !important' }}
                    ></InputNumber>
                </Form.Item>
                <Form.Item
                    label={"信任代理"}

                    name="source_ip_config.trust_proxies"
                    initialValue={props.data?.source_ip_config?.trust_proxies || []}
                >
                    <Select
                        size="middle"
                        mode="tags"
                        dropdownStyle={
                            {
                                display: 'none'
                            }
                        }
                        style={{ width: '100%', borderRadius: '10px' }}
                        placeholder={'信任的代理IP，支持CIDR'}
                        onChange={(e) => {

                        }}
                        options={[]}
                    />
                </Form.Item>
                <Form.Item
                    label={wordList['站点描述'][Context.lan]}
                    name="description"
                >
                    <Input style={{ width: '100%' }} placeholder={wordList['请输入描述'][Context.lan]} ></Input>
                </Form.Item>
            </Form>
        </div>
    </Modal>
}

const EditUpstreamNode = (props: any) => {
    const [open, setOpen] = useState(true)
    const Context = useContext(ProviderContext)
    const [form] = Form.useForm()
    useEffect(() => {
        let preAddress = props?.data || ""
        if (preAddress) {
            form.setFieldsValue({
                ip: preAddress,
            })
        }
    }, [])
    return <Modal
        className="site-detail-modal"
        open={open}
        afterClose={() => {
            props.close && props.close()
        }}
        title={props.type == 'add' ? wordList['添加上游服务器'][Context.lan] : wordList['编辑上游服务器'][Context.lan]}
        footer={
            <div style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'space-between'
            }}>
                {
                    props.type !== 'add'
                        ? <BugsLinkButton style={{ color: "#f4424d" }} onClick={() => {
                            Modal.confirm({
                                title: wordList['操作确认'][Context.lan],
                                content: wordList['确认删除此上游服务器'][Context.lan],
                                onOk: () => {
                                    return new Promise<boolean>((resolve, reject) => {
                                        props.saveNewUp && props.saveNewUp("remove", () => {
                                            resolve(true)
                                            setOpen(false)
                                        })
                                    })

                                }
                            })
                        }}>{wordList['删除节点'][Context.lan]}</BugsLinkButton>
                        : <div></div>
                }

                <Space>
                    <BugsLinkButton onClick={() => { setOpen(false) }}>{wordList['取消'][Context.lan]}</BugsLinkButton>
                    <BugsButton onClick={() => {
                        let data = form.getFieldsValue()
                        props.saveNewUp && props.saveNewUp(data.ip, () => {
                            setOpen(false)
                        })
                    }}>{wordList['保存'][Context.lan]}</BugsButton>
                </Space>
            </div>

        }
        onCancel={() => {
            setOpen(false)
        }}>
        <Form form={form}>
            {/*<Form.Item
                label="节点名称"
                name="name"
            >
                <Input style={{ width: '100%' }}></Input>
            </Form.Item>*/}
            <Form.Item
                label={wordList['IP地址'][Context.lan]}
                name="ip"
                required
            >
                <Input style={{
                    width: '100%'
                }}></Input>
            </Form.Item>
            {/*<Form.Item
                label={wordList['端口'][Context.lan]}
                name="port"
                required
            >
                <InputNumber style={{
                    width: '100%'
                }}></InputNumber>
            </Form.Item>*/}
        </Form>
    </Modal>
}
let updateSelect: any = null
const SiteDetail = (props: any) => {
    const Context = useContext(ProviderContext)
    const [data, setData] = useState<any>({})
    const [resetRender, setResetRender] = useState("")
    const [showSaveFre, setShowSaveFre] = useState(false)
    const tagStyle: any = {
        border: 'none', width: 'auto', minWidth: '50px', height: '25px', fontSize: '14px', borderRadius: '5px', textAlign: 'center', lineHeight: '25px'
    }
    const [showEdit, setShowEdit] = useState({
        show: false,
        data: {}
    })
    const [showEditUp, setShowEditUp] = useState<any>({
        show: false,
        type: 'edit',
        index: -1
    })
    const params = useParams()
    const [showAddUp, setShowUp] = useState(false)

    const [tempFreq, setTempFreq] = useState<any>({})
    const [licenseData, setLicenseData] = useState<any>({})
    const getLicense = (id) => {
        if (!id) {
            setLicenseData({})
            return
        }
        api.keyLicense.getLicenseById(id)
            .then(res => {
                if (res?.data?.data) {
                    setLicenseData(res?.data?.data)
                }
            })
    }
    const getDetail = () => {
        api.site.getSiteById(params.id || props.id)
            .then(res => {
                if (res?.data?.data) {
                    let data = res.data.data
                    let file_upload_module = data?.file_upload_module
                    if (!file_upload_module?.disallow_exts || file_upload_module?.disallow_exts == null) {
                        file_upload_module = {
                            ...file_upload_module || {},
                            disallow_exts: ["exe", "bat", "cmd", "sh", "php", "jsp", "asp", "aspx"],
                        }
                        data.file_upload_module = file_upload_module
                    }
                    setData(data)
                    setTempFreq(JSON.parse(JSON.stringify(res.data.data?.limit_module || {})))
                    getLicense(res.data.data.cert_id)
                }
            })
    }
    useEffect(() => {
        getDetail()
    }, [])
    const [setting, setSetting] = useState(false)
    const saveNewUp = (address, cb) => {
        let newUpStreams: any = []
        if (address == 'remove') {
            newUpStreams = [
                ...(data?.upstreams || []).slice(0, showEditUp.index),
                ...(data?.upstreams || []).slice(showEditUp.index + 1),
            ]
        }
        else if (showEditUp.type == 'edit') {
            newUpStreams = [
                ...(data?.upstreams || []).slice(0, showEditUp.index),
                address,
                ...(data?.upstreams || []).slice(showEditUp.index + 1),
            ]
        } else {
            newUpStreams = [
                ...(data?.upstreams || []),
                address
            ]
        }
        api.site.editSite(data.id, {
            upstreams: newUpStreams
        })
            .then(res => {
                if (res?.data?.data) {
                    getDetail()
                    cb && cb()
                } else {
                    message.error(wordList['修改失败'][Context.lan])
                }
            })
    }
    const saveSite = (newSiteData, cb: any = null) => {
        setSetting(true)
        api.site.editSite(data.id, newSiteData)
            .then(res => {
                setSetting(false)
                if (res?.data?.data) {
                    getDetail()
                    if (cb) {

                        cb && cb()
                    } else {

                        message.success(wordList['修改成功'][Context.lan])
                    }
                    setData({
                        ...data,
                        ...newSiteData
                    })
                } else {
                    message.error(wordList['修改失败'][Context.lan])
                }
            })
    }
    const navigate = useNavigate()
    const [showLicense, setShowLicense] = useState(false)
    return <Card title={wordList["站点详情"][Context.lan]}
        extra={
            <Space>
                <BugsLinkButton style={{ color: '#f4424d' }} onClick={() => {
                    Modal.confirm({
                        cancelText: wordList['取消'][Context.lan],
                        okText: wordList['确认'][Context.lan],
                        title: wordList['操作确认'][Context.lan],
                        content: wordList['确认删除此站点'][Context.lan],
                        onOk: () => {
                            return new Promise<boolean>((resolve, reject) => {
                                api.site.deleteSite(data?.id)
                                    .then(res => {
                                        if (res.data?.data) {
                                            message.success(wordList['站点已删除'][Context.lan])

                                            setTimeout(() => {
                                                if (props.id) {
                                                    //组件抽屉调用
                                                    props.onUpdate && props.onUpdate()
                                                    return
                                                }
                                                navigate("/domain/domainList")
                                            }, 2000)
                                            resolve(true)
                                        } else {
                                            resolve(false)
                                        }
                                    })
                            })
                        }
                    })
                }}>{wordList['删除站点'][Context.lan]}</BugsLinkButton>
                <BugsButton
                    style={{
                        background: "#cc1212"
                    }}
                    onClick={() => {
                        setShowEdit({
                            show: true,
                            data: data
                        })
                    }}>{wordList["编辑站点"][Context.lan]}</BugsButton>
            </Space>

        }
        style={{
            borderRadius: '10px',
            width: '100%',
            paddingLeft: 0,
            height: '100%',
            paddingRight: 0,
        }}>
        {
            showLicense ? <BindLicense
                onBind={(cert_id, cb) => {
                    saveSite({
                        cert_id
                    }, cb)
                }}
                close={() => {
                    setShowLicense(false)
                }}></BindLicense> : null
        }
        {
            /**编辑站点 */
            showEdit.show
                ? <EditBaseInfo
                    saveSite={saveSite}
                    data={showEdit.data}
                    close={() => {
                        setShowEdit({
                            ...showEdit,
                            show: false
                        })
                    }}></EditBaseInfo>
                : null
        }

        {
            /**编辑上游服务器 */

            showEditUp.show
                ? <EditUpstreamNode
                    type={showEditUp.type}
                    data={(data?.upstreams || [])[showEditUp.index] || ''}
                    saveNewUp={saveNewUp}
                    close={() => {
                        setShowEditUp({
                            ...showEditUp,
                            show: false
                        })
                    }}></EditUpstreamNode>
                : null

        }

        <div style={{
            height: 'calc(100vh - 215px)',
            width: "100%",
            overflowY: 'auto'
        }}>
            <div className="baseinfo-border-container site-baseinfo-container" style={{ position: 'relative', display: 'flex', gap: '10px', height: 'auto' }}>
                {
                    /**站点名称 */
                    /**匹配方式 监听端口 后端端口 后端路由 上游服务器节点（负载均衡） 创建时间 运行模式  今日防护情况， */
                }
                <Descriptions labelStyle={{ color: "#999", height: '30px', lineHeight: '30px', whiteSpace: 'nowrap' }}
                    contentStyle={{
                        lineHeight: '30px'
                    }}
                    style={{
                        width: "100%",
                        height: '100%',
                        lineHeight: '30px',
                    }}>
                    <Descriptions.Item label={wordList['站点名称'][Context.lan]} span={1}>
                        {data?.title || '-'}

                    </Descriptions.Item>
                    <Descriptions.Item label={wordList['创建时间'][Context.lan]} span={3}>
                        {
                            moment(data?.created_at || '').format('YYYY/MM/DD HH:mm:ss')
                        }
                    </Descriptions.Item>
                    <Descriptions.Item label={wordList['监听端口'][Context.lan]} span={1}>

                        <div style={{
                            display: 'flex',
                            width: '100%',
                            textAlign: 'center',
                            gap: '10px'
                        }}>
                            {(data?.ports || []).map(item => {
                                try {
                                    item = item.replace('_', '/')
                                } catch { }
                                return <div   >
                                    {item}
                                </div>
                            })}
                        </div>

                    </Descriptions.Item>
                    <Descriptions.Item label={wordList['运行模式'][Context.lan]} span={3} contentStyle={{ marginTop: '3px' }}>
                        {[
                            <Tag style={tagStyle} color="#0fc6c2" >
                                {wordList["防护"][Context.lan]}
                            </Tag>,//防护
                            <Tag style={tagStyle} color="#feab12">
                                {wordList["观察"][Context.lan]}
                            </Tag>,//观察
                            <Tag style={tagStyle} color="#f5222d">
                                {wordList["维护"][Context.lan]}
                            </Tag>//维护
                        ][data.mode]}

                    </Descriptions.Item>

                    <Descriptions.Item label={wordList['运行情况'][Context.lan]} span={1}>
                        <Switch
                            loading={setting}
                            key={data?.id}
                            style={{ marginTop: '5px' }}
                            checked={data?.enabled}
                            onChange={() => {
                                saveSite({
                                    enabled: !data.enabled,
                                }, () => {
                                    message.success(wordList['站点状态已修改'][Context.lan])
                                })
                            }}
                            checkedChildren={wordList['已启用'][Context.lan]}
                            unCheckedChildren={wordList['未启用'][Context.lan]}></Switch>
                    </Descriptions.Item>
                    <Descriptions.Item label={wordList['站点类型'][Context.lan]} span={1}>
                        {
                            config.siteInfo.type[data.type]
                        }
                    </Descriptions.Item>
                    <br></br>
                    <Descriptions.Item label={wordList['匹配方式'][Context.lan]} span={4} contentStyle={{ flexWrap: "wrap" }}>
                        {
                            (data?.server_names || []).map(item => {
                                return <Tag
                                    style={{ background: "white", marginTop: '5px', border: '1px solid #cc1212', color: '#cc1212', borderRadius: '5px', fontSize: '14px', width: 'auto', paddingLeft: '10px', paddingRight: '10px', marginLeft: '10px', textAlign: 'center' }}>
                                    {item}
                                </Tag>
                            })
                        }
                    </Descriptions.Item>
                    <Descriptions.Item label={wordList['站点描述'][Context.lan]} span={4}>
                        {data?.description || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label={"站点证书"} span={4}>
                        {licenseData?.domains

                            ? <div>
                                {
                                    (licenseData?.domains || []).map((item, index) => {
                                        return <>
                                            {
                                                index > 0
                                                    ? <Divider type="vertical"></Divider>
                                                    : null
                                            }
                                            {item}

                                        </>
                                    })

                                }
                                <span style={{ color: "#cc1212", marginLeft: '10px', cursor: "pointer" }} onClick={() => {
                                    Modal.confirm({
                                        title: '操作确认',
                                        content: "确认解绑此证书",
                                        onOk: () => {
                                            saveSite({
                                                cert_id: 0
                                            }, () => {
                                                message.success("解绑成功")
                                            })
                                        }
                                    })
                                }}>解除绑定</span>
                            </div>

                            : <BugsLinkButton onClick={() => {
                                setShowLicense(true)
                            }}>未绑定证书</BugsLinkButton>
                        }

                    </Descriptions.Item>
                    {
                        /**
                         * <Descriptions.Item label="高级防护" span={3}>
                        <Space>

                            <CheckableTag
                                key={"CC"}
                                style={{
                                    width: 'auto'
                                }}
                                checked={data?.ccProtection?.open}
                            >
                                <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="61446" data-spm-anchor-id="a313x.search_index.0.i51.75303a81B5suLu" width="18" height="18"><path d="M358.4 153.6l92.16 327.68c5.12 10.24-5.12 25.6-15.36 30.72H291.84c-15.36 0-25.6-10.24-25.6-25.6v-5.12L358.4 153.6zM665.6 870.4l-92.16-327.68v-5.12c0-15.36 10.24-25.6 25.6-25.6H742.4c15.36 5.12 20.48 20.48 15.36 30.72L665.6 870.4z" fill="#ffffff" p-id="61447" data-spm-anchor-id="a313x.search_index.0.i48.75303a81B5suLu" ></path><path d="M25.6 537.6c-15.36 0-25.6-10.24-25.6-25.6s10.24-25.6 25.6-25.6h143.36c10.24 0 20.48-5.12 25.6-20.48L307.2 71.68c10.24-25.6 35.84-40.96 61.44-35.84 20.48 10.24 35.84 20.48 40.96 35.84l256 860.16 117.76-394.24c10.24-30.72 40.96-56.32 71.68-56.32h143.36c15.36 0 25.6 10.24 25.6 25.6s-10.24 25.6-25.6 25.6h-143.36c-10.24 0-20.48 5.12-25.6 20.48L716.8 947.2c-5.12 15.36-15.36 30.72-35.84 35.84-25.6 10.24-56.32-5.12-61.44-35.84L358.4 87.04 240.64 481.28c-10.24 30.72-40.96 56.32-71.68 56.32H25.6z" fill="" p-id="61448"></path></svg>

                                {
                                    wordList["CC防护"][Context.lan]
                                }
                            </CheckableTag>

                            <CheckableTag
                                key={"BOT"}
                                style={{
                                    width: 'auto'
                                }}
                                checked={data?.botProtection?.open}
                            >
                                <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="62651" width="18" height="18"><path d="M757.461333 597.333333a96 96 0 0 1 96 96v38.613334A160 160 0 0 1 797.696 853.333333C730.88 910.677333 635.306667 938.709333 512 938.709333c-123.306667 0-218.794667-28.032-285.482667-85.333333a160 160 0 0 1-55.722666-121.344v-38.698667A96 96 0 0 1 266.794667 597.333333h490.666666z m0 64h-490.666666a32 32 0 0 0-32 32v38.698667c0 27.989333 12.202667 54.570667 33.450666 72.789333C321.92 850.986667 402.773333 874.709333 512 874.709333c109.226667 0 190.208-23.722667 244.010667-69.930666a96 96 0 0 0 33.450666-72.832v-38.613334a32 32 0 0 0-32-32zM507.648 85.632L512 85.333333a32 32 0 0 1 31.701333 27.690667l0.298667 4.309333v32h149.333333a96 96 0 0 1 96 96v192.213334a96 96 0 0 1-96 96h-362.666666a96 96 0 0 1-96-96V245.333333A96 96 0 0 1 330.666667 149.333333h149.333333v-32a32 32 0 0 1 27.648-31.701333L512 85.333333l-4.352 0.298667zM693.333333 213.333333h-362.666666a32 32 0 0 0-32 32v192.213334c0 17.664 14.336 32 32 32h362.666666a32 32 0 0 0 32-32V245.333333a32 32 0 0 0-32-32z m-277.333333 64a53.333333 53.333333 0 1 1 0 106.624 53.333333 53.333333 0 0 1 0-106.624z m191.658667 0a53.333333 53.333333 0 1 1 0 106.624 53.333333 53.333333 0 0 1 0-106.624z" fill="#212121" p-id="62652"></path></svg>
                                {
                                    wordList["BOT防护"][Context.lan]
                                }
                            </CheckableTag>

                            <CheckableTag
                                style={{
                                    width: 'auto'
                                }}
                                key={"AUTH"}
                                checked={data?.authProtection?.open}
                            >
                                <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="63766" width="18" height="18"><path d="M839.682194 205.238857v318.464c0 96.256-53.906286 192.731429-160.182857 286.866286-36.352 31.378286-74.898286 59.830857-114.541714 84.626286-8.484571 5.339429-16.969143 9.947429-25.161143 14.409142l-10.971429 5.997715c-50.907429-28.818286-102.4-65.097143-148.918857-105.033143C273.55648 716.434286 219.577051 619.958857 219.577051 523.702857V205.238857l310.857143-77.312L839.682194 205.238857z m45.129143-63.926857L538.479909 54.710857a27.062857 27.062857 0 0 0-17.334858-0.073143L174.521051 141.238857A36.205714 36.205714 0 0 0 146.287909 177.005714v346.697143c0 119.003429 62.756571 233.910857 186.514285 341.577143a1134.226286 1134.226286 0 0 0 165.814857 115.565714c2.413714 1.170286 4.608 2.194286 6.729143 3.072 3.510857 1.609143 6.582857 2.925714 9.654857 5.046857 4.827429 2.925714 10.605714 4.388571 16.384 4.388572a31.451429 31.451429 0 0 0 16.091429-4.242286 880.859429 880.859429 0 0 0 179.126857-123.904c123.684571-107.593143 186.441143-222.500571 186.441143-341.577143v-346.697143a36.205714 36.205714 0 0 0-28.233143-35.620571z" fill="#000000" p-id="63767"></path><path d="M586.315337 375.296a51.785143 51.785143 0 0 1-52.516571 50.907429c-27.428571 0-52.516571-24.283429-52.516572-50.907429 0-28.013714 23.552-50.834286 52.516572-50.834286 31.451429 0 52.516571 20.48 52.516571 50.834286m-179.931428-1.682286c0 49.883429 31.305143 94.866286 78.262857 112.932572v272.091428c0 20.845714 15.067429 35.84 35.84 35.84 20.772571 0 35.84-14.994286 35.84-35.84v-27.428571h52.516571a27.867429 27.867429 0 0 0 29.110857-29.184 27.867429 27.867429 0 0 0-29.110857-29.184h-52.516571v-21.430857l55.881143 3.072c17.846857 0 30.72-12.214857 30.72-29.110857a27.867429 27.867429 0 0 0-29.110858-29.184h-52.516571V492.105143a121.344 121.344 0 0 0 96.694857-118.491429c0-67.510857-56.466286-122.441143-125.805714-122.441143-68.242286 0-125.805714 56.100571-125.805714 122.441143" fill="#000000" p-id="63768"></path></svg>
                                {
                                    wordList["身份防护"][Context.lan]
                                }
                            </CheckableTag>
                        </Space>
                    </Descriptions.Item>
                    
                         * 
                         */
                    }


                </Descriptions>
                {
                    /**
                     *  <div style={{
                    width: '10%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexDirection: 'column',
                    gap: '30px'
                }}>
                    <div className="site-detail-today">
                        <div style={{ fontSize: '12px', color: "#999" }}>今日访问</div>
                        <div style={{
                            fontSize: '20px',
                            fontWeight: 'bold'
                        }}>{tools.formatNumber(data?.requestTimes || '-')}
                            <span style={{ color: data?.requestTimes > data?.requestTimesBefore ? "#cc1212" : "#f4424d" }}>{data?.requestTimes > data?.requestTimesBefore ? "↗" : "↘"}</span></div>
                    </div>
                    <div className="site-detail-today">
                        <div style={{ fontSize: '12px', color: "#999" }}>今日拦截</div>
                        <div style={{
                            fontSize: '18px',
                            fontWeight: 'bold'
                        }}>{tools.formatNumber(data?.interceptTimes || '-')}
                            <span style={{ color: data?.interceptTimes > data?.interceptTimesBefore ? "#cc1212" : "#f4424d" }}>{data?.interceptTimes > data?.interceptTimesBefore ? "↗" : "↘"}</span>
                        </div>
                    </div>
                </div>
                <div style={{
                    marginLeft: '5%',
                    width: '35%',
                    height: '100%'
                }}>
                    <div style={{
                        fontSize: '18px',
                        width: '100%',
                        textAlign: 'center',
                        lineHeight: '30px',
                        height: '30px'
                    }}>站点近7日访问与拦截分析</div>
                    <LineChart></LineChart>
                </div>
                     * 
                    */
                }

            </div>

            <Divider orientation="left" style={{ marginTop: '50px' }}>{wordList["上游节点"][Context.lan]}({wordList["负载均衡"][Context.lan]})</Divider>
            <div className="site-detail-modules ">
                {
                    (data?.upstreams || []).map((item, index) => {
                        return <div className="upstream-node">
                            <div className="edit-upstream-node" onClick={() => {
                                setShowEditUp({
                                    show: true,
                                    type: 'edit',
                                    index: index
                                })
                            }}>
                                <EditOutlined></EditOutlined>
                            </div>
                            {
                                item.status == 0
                                    ? ErrorStatusIcon
                                    : HelthyStatusIcon
                            }
                            {/*<div style={{
                                color: item.status == 0 ? "#f4424d" : "#cc1212"
                            }}>
                                {item.status == 0 ? "服务异常" : "运行正常"}<span style={{ marginLeft: '10px' }}><Tooltip title="重新测试"><SyncOutlined></SyncOutlined></Tooltip></span>
                            </div>
                            */}
                            {/*<div style={{ fontSize: "16px", textAlign: 'center', lineHeight: '30px' }}>{item}</div>*/}
                            <div style={{ fontSize: "14px", color: "#999", textAlign: 'center', lineHeight: '30px', whiteSpace: "wrap", wordBreak: "break-all" }}>{item}</div>
                            {/*<div style={{ fontSize: "14px", color: "#999", textAlign: 'center', lineHeight: '30px' }}>{item.ip}:<span style={{ color: "#feab12" }}>{item.port}</span></div>*/}
                        </div>
                    })
                }
                <div className="upstream-node" onClick={() => {
                    setShowEditUp({
                        show: true,
                        type: 'add',
                        index: -1
                    })
                }}>
                    <PlusOutlined style={{ fontSize: '48px', color: "#999" }}></PlusOutlined>
                    <div style={{ whiteSpace: "nowrap" }}>{wordList["添加上游服务器"][Context.lan]}</div>
                </div>
            </div>
            {/*<Checkbox style={{
                marginLeft: '15px',
                marginTop: '20px'
            }}>{wordList["启用上游服务器健康检查"][Context.lan]}</Checkbox>*/}


            <Divider orientation="left" style={{ marginTop: '50px' }}>{wordList["监察阻断设置"][Context.lan]}</Divider>
            <div className="site-detail-modules "
                style={{
                    height: "auto",
                    maxHeight: "none"
                }}
                key={data?.id}>
                <SiteFrequency
                    key={resetRender}
                    data={data?.limit_module || {}} onChange={(e) => {

                        setData({
                            ...data,
                            limit_module: e
                        })
                        setShowSaveFre(true)
                    }}></SiteFrequency>
            </div>
            {
                showSaveFre
                    ? <div style={{
                        marginTop: '20px',
                        width: 'calc(100% - 15px)',
                        display: 'flex',
                        flexDirection: 'row-reverse'
                    }}>
                        <Space>
                            <Popconfirm
                                trigger={"click"}
                                title="确认恢复当前已更改的内容"
                                onConfirm={() => {
                                    setResetRender(Math.random().toString(32).slice(2, 10))
                                    setData({
                                        ...data,
                                        limit_module: JSON.parse(JSON.stringify(tempFreq))
                                    })
                                }}
                            >
                                <BugsLinkButton onChange={(e) => {

                                }}>恢复更改</BugsLinkButton>
                            </Popconfirm>

                            <BugsButton
                                loading={setting}
                                onClick={() => {
                                    saveSite(data, () => {
                                        setShowSaveFre(false)
                                        message.success("保存成功")
                                    })

                                }}>保存配置</BugsButton>
                        </Space>
                    </div>
                    : null
            }

            <Divider orientation="left" style={{ marginTop: '50px' }}>{wordList["防护模块"][Context.lan]}</Divider>
            <Spin spinning={setting}>
                <div className="site-detail-modules ">

                    {
                        (Object.keys(data?.defence_module || {})).map((item, index) => {
                            return <div className="defend-item-container">
                                <div style={{ width: "50%", height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', gap: '10px' }}>
                                    {config.defendModuleIcons[item]}
                                    {config.modeulesName[item]}
                                </div>
                                <div style={{ width: "50%", height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', gap: '10px' }}>
                                    <Radio.Group defaultValue={0} buttonStyle="solid" value={data.defence_module[item] ?? 2} onChange={(e) => {

                                        setSetting(true)
                                        let newData = {
                                            defence_module: {
                                                ...data.defence_module,
                                                [item]: e.target.value
                                            }
                                        }
                                        api.site.editSite(data.id, newData)
                                            .then(res => {
                                                setSetting(false)
                                                if (res?.data?.data) {
                                                    message.success(wordList["修改成功"][Context.lan])
                                                    setData({
                                                        ...data,
                                                        ...newData
                                                    })
                                                }

                                            })
                                    }}>
                                        <Radio value={0}>{wordList["禁用"][Context.lan]}</Radio>
                                        <Radio value={1}>{wordList["仅观察"][Context.lan]}</Radio>
                                        <Radio value={2}>{wordList["开启拦截"][Context.lan]}</Radio>
                                    </Radio.Group>
                                </div>
                            </div>
                        })
                    }
                </div>
            </Spin>

            <Divider orientation="left" style={{ marginTop: '50px' }}>文件上传限制</Divider>
            <div className="site-detail-modules "
                style={{
                    height: "70px",
                    maxHeight: "none",
                    minHeight: "145px",
                    gap: '10px'
                }}
                key={data?.id}>
                <div style={{ display: 'flex', width: '100%', gap: '20px', alignItems: 'center', height: '30px' }}>
                    <div style={{ width: '150px' }}>模块配置</div>
                    <div style={{
                        display: "flex",
                        width: '100%',
                        height: '40px',
                        gap: '10px',
                        alignItems: 'center'
                    }}>
                        <div>是否启用</div>
                        <Switch checked={data?.file_upload_module?.enabled}
                            onChange={(e) => {
                                saveSite({
                                    file_upload_module: {
                                        ...data.file_upload_module || {},
                                        enabled: e
                                    }
                                }, () => {
                                    message.success("设置成功")
                                })
                                setData({
                                    ...data,
                                    file_upload_module: {
                                        ...data.file_upload_module || {},
                                        enabled: e
                                    }
                                })
                            }}></Switch>
                        <Tooltip title="严格模式，启用后会对文件内容是否匹配扩展名进行检查">

                            <Checkbox style={{ marginLeft: '30px' }} checked={data?.file_upload_module?.strict}
                                onChange={(e) => {
                                    saveSite({
                                        file_upload_module: {
                                            ...data.file_upload_module || {},
                                            strict: e.target.checked
                                        }
                                    }, () => {
                                        message.success("设置成功")
                                    })
                                    setData({
                                        ...data,
                                        file_upload_module: {
                                            ...data.file_upload_module || {},
                                            strict: e.target.checked
                                        }
                                    })
                                }}
                            >严格模式</Checkbox>
                        </Tooltip>
                    </div>
                </div>

                <div style={{ display: 'flex', width: '100%', gap: '20px', alignItems: 'center', height: '30px', marginTop: '10px' }}>
                    <div style={{ width: '150px' }}>文件类型</div>
                    <div style={{
                        height: 'auto',
                        display: "flex",
                        flexDirection: 'column',
                        width: '100%',
                        marginTop:"20px",
                        gap: '5px'
                    }}>
                    <Select
                        size="large"
                        dropdownStyle={
                            {
                                display: 'none'
                            }
                        }
                        mode="tags"
                        value={data?.file_upload_module?.disallow_exts || []}
                        style={{ width: '100%', borderRadius: '10px' }}
                        placeholder={`输入文件类型 php/exe/jsp 等`/**wordList['输入服务域名匹配规则'][Context.lan] */}
                        onChange={(e) => {
                            clearTimeout(updateSelect)
                            updateSelect = setTimeout(() => {
                                saveSite({
                                    file_upload_module: {
                                        ...data.file_upload_module || {},
                                        disallow_exts: e
                                    }
                                }, () => {
                                    message.success("设置成功")
                                })
                            }, 200)
                            setData({
                                ...data,
                                file_upload_module: {
                                    ...data.file_upload_module || {},
                                    disallow_exts: e
                                }
                            })

                        }}
                        options={[]}
                    />
                        <div style={{color:"#999"}}>启用后将<span style={{color:"#f5222d"}}>禁止上传</span>上述文件类型</div>
                    </div>
                </div>

            </div>
            {
                /**
                 * 
                 * <Divider orientation="left" style={{ marginTop: '50px' }}>CC防护</Divider>
            <div className="site-detail-modules" style={{ height: 'auto', maxHeight: "max-content" }}>

                <CCProtection></CCProtection>
            </div>
            <Divider orientation="left" style={{ marginTop: '50px' }}>BOT防护</Divider>
            <div className="site-detail-modules" style={{ height: 'auto', maxHeight: "max-content" }}>

                <BOTProtection></BOTProtection>
            </div>
            <Divider orientation="left" style={{ marginTop: '50px' }}>身份验证</Divider>
            <div className="site-detail-modules" style={{ height: 'auto', maxHeight: "max-content" }}>

                <AuthProtection></AuthProtection>
            </div>
                 */
            }

        </div>
    </Card>
}
export default SiteDetail