import { Tag } from "antd"
import { useContext, useEffect, useState } from "react";
import ProviderContext from "../../pages/providerContext";
import wordList from "../../config/wordList";
import "./index.less"
const { CheckableTag } = Tag;
const TimeRangeSelector = (props: any) => {
    const Context = useContext(ProviderContext)
    const [nowSelectTime, setNowSelectime] = useState("_today")
    const [options, setOptions] = useState([
        {
            key: "_today",
            label: '今日'
        }, {
            key: "_7days",
            label: '近7天'
        }, {
            key: "_30days",
            label: '近30天'
        }
    ])
    useEffect(() => {
        if (props.options) {
            setOptions(props.options)
        } else if (props.extend) {
            setOptions([
                ...options,
                ...props.extend
            ])
        }
    }, [])
    return <div className="top-time-selector"
        key={Context.lan}
        style={{
            ...(props.style || {})
        }}>
        {
            options.map(item => {
                return <CheckableTag
                    key={item.key}
                    checked={nowSelectTime == item.key}
                    onChange={checked => {
                        setNowSelectime(item.key)
                        props.onChange && props.onChange({
                            value: item.key,
                            text: item.label
                        })
                    }}
                >
                    
                    {wordList[item.label]?wordList[item.label][Context.lan]:'-'}
                </CheckableTag>
            })
        }

    </div>
}
export default TimeRangeSelector