import { <PERSON><PERSON>, Card, Divider, Input, Modal, Radio, Select, Space } from "antd"
import "./index.less"
import ColorPicker from "../../../../../components/ColorPicker"
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript'
import { html } from '@codemirror/lang-html'
import { githubLight, githubDark } from '@uiw/codemirror-theme-github';
import { useContext, useState } from "react"
import ProviderContext from "../../../../providerContext";
import wordList from "../../../../../config/wordList";
import tempPage from "./temp"
import { filterXSS } from "xss"
import BugsLinkButton from "../../../../../components/BugsLinkButton";
const CodeEditor: any = CodeMirror
const defaultColorA = [
    {
        bgColor: "#FF6666",
        textColor: "#FFFFFF"
    }, {
        bgColor: "#FF9966",
        textColor: "#FFFFFF"
    }, {
        bgColor: "#3A383F",
        textColor: "#FFFFFF"
    }, {
        bgColor: "#FFF2F2",
        textColor: "#FF4D4F"
    }, {
        bgColor: "#FFDFCC",
        textColor: "#FF6000"
    }, {
        bgColor: "#3B3B51",
        textColor: "#EF7754"
    },
]
const InterceptPage = (props: any) => {
    const Context=useContext(ProviderContext)
    const [colorA, setColorA] = useState({
        open: false,
        bgColor: "#ffffff",
        textColor: "#000000"
    })
    const [colorB, setColorB] = useState({
        open: false,
        bgColor: "#ffffff",
        textColor: "#000000"
    })
    const [editHTML, setEditHTML] = useState({
        code: ``,
        open: false,
        target: ""
    })
    const [htmlList, setHTMLList] = useState([
        {
            title: wordList["攻击检测与黑名单"][Context.lan],
            value: 'blackIP',
            code: 403
        }, {
            title: wordList["频率限制"][Context.lan],
            value: 'frequency',
            code: 429
        }, {
            title: wordList["网站不存在"][Context.lan],
            value: 'unExist',
            code: 404
        }, {
            title: wordList["网关错误"][Context.lan],
            value: 'netError',
            code: 502
        }, {
            title: wordList["站点维护"][Context.lan],
            value: 'processing',
            code: 503
        }, {
            title: wordList["网关超时"][Context.lan],
            value: 'netTimeout',
            code: 504
        }, {
            title: wordList["页面不存在"][Context.lan],
            value: 'pageUnExist',
            code: 404
        }, {
            title: wordList["身份认证"][Context.lan],
            value: 'auth',
            code: 467
        }, {
            title: wordList["人机验证"][Context.lan],
            value: 'bot',
            code: 468
        },
        {
            title: wordList["等候室"][Context.lan],
            value: 'waitingRoom',
            code: 465
        },
    ])
    const [nowViewHTML, setNowViewHTML] = useState("editor")
    return <>
        <Modal

            title={wordList["配色方案"][Context.lan]}
            open={
                colorA.open
            }
            className="colorModal"
            width={800}
            onCancel={() => {
                setColorA({
                    ...colorA,
                    open: false
                })
            }}
        >
            <div style={{
                width: '100%',
                height: '400px',
                display: 'flex',
                justifyContent: 'space-between'
            }}>
                {/**示意部分 */}
                <div style={{
                    background: colorA.bgColor,
                    width: '500px',
                    height: "400px",
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '10px',
                    boxShadow: '0 0 10px rgba(0,0,0,0.1)',
                    flexDirection: 'column'
                }}>
                    <div style={{
                        color: colorA.textColor,
                        fontSize: "20px",
                    }}>谋乐 WAF </div>

                    <div style={{
                        color: colorA.textColor,
                        fontSize: "20px",
                    }}>{wordList["提示文本演示内容"][Context.lan]} </div>
                </div>
                <div style={{
                    width: "200px",
                    height: '400px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <div style={{
                        marginTop: '20px',
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between'
                    }}>
                        <div>{wordList["背景颜色"][Context.lan]}</div>
                        <ColorPicker
                            color={
                                colorA.bgColor
                            }
                            onChange={(hex) => {
                                setColorA({
                                    ...colorA,
                                    bgColor: hex
                                })
                            }}></ColorPicker>
                    </div>


                    <div style={{
                        marginTop: '20px',
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between'
                    }}>
                        <div>{wordList["文本颜色"][Context.lan]}</div>
                        <ColorPicker
                            color={
                                colorA.textColor
                            }
                            onChange={(hex) => {
                                setColorA({
                                    ...colorA,
                                    textColor: hex
                                })
                            }}></ColorPicker>
                    </div>

                    <div style={{
                        marginTop: '20px',
                        width: '100%',
                        textAlign: 'left'
                    }}>{wordList["预设方案"][Context.lan]}</div>
                    <div style={{
                        height: '200px',
                        overflowY: 'auto'
                    }}>
                        {
                            defaultColorA.map((item) => {
                                return <div className="color-item" onClick={() => {
                                    setColorA({
                                        ...colorA,
                                        ...item
                                    })
                                }}>
                                    <div style={{
                                        width: "150px",
                                        height: '100px',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        color: item.textColor,
                                        background: item.bgColor
                                    }}>
                                        <div>{wordList["背景颜色"][Context.lan]} <span>{item.bgColor}</span></div>
                                        <div> {wordList["文字颜色"][Context.lan]}<span>{item.textColor}</span></div>
                                    </div>
                                </div>
                            })
                        }

                    </div>
                </div>
            </div>
        </Modal>
        <Modal

            title={wordList["配色方案"][Context.lan]}
            open={
                colorB.open
            }
            className="colorModal"
            width={600}
            onCancel={() => {
                setColorB({
                    ...colorB,
                    open: false
                })
            }}
        >
            <div style={{
                width: '100%',
                height: '400px',
                display: 'flex',
                justifyContent: 'space-between'
            }}>
                {/**示意部分 */}
                <div style={{
                    background: colorB.bgColor,
                    width: '500px',
                    height: "400px",
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '10px',
                    boxShadow: '0 0 10px rgba(0,0,0,0.1)',
                    flexDirection: 'column'
                }}>
                    <div style={{
                        color: colorB.textColor,
                        fontSize: "20px",
                    }}>谋乐 WAF </div>

                    <div style={{
                        color: colorB.textColor,
                        fontSize: "20px",
                    }}>{wordList["提示文本演示内容"][Context.lan]} </div>
                </div>
                <div style={{
                    width: "200px",
                    height: '400px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <div style={{
                        marginTop: '20px',
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between'
                    }}>
                        <div>{wordList["背景颜色"][Context.lan]} </div>
                        <ColorPicker
                            color={
                                colorB.bgColor
                            }
                            onChange={(hex) => {
                                setColorB({
                                    ...colorB,
                                    bgColor: hex
                                })
                            }}></ColorPicker>
                    </div>


                    <div style={{
                        marginTop: '20px',
                        display: 'flex',
                        width: '100%',
                        justifyContent: 'space-between'
                    }}>
                        <div>{wordList["文本颜色"][Context.lan]}</div>
                        <ColorPicker
                            color={
                                colorB.textColor
                            }
                            onChange={(hex) => {
                                setColorB({
                                    ...colorB,
                                    textColor: hex
                                })
                            }}></ColorPicker>
                    </div>

                    <div style={{
                        marginTop: '20px',
                        width: '100%',
                        textAlign: 'left'
                    }}>{wordList["预设方案"][Context.lan]}</div>
                    <div style={{
                        height: '200px',
                        overflowY: 'auto'
                    }}>
                        {
                            defaultColorA.map((item) => {
                                return <div className="color-item" onClick={() => {
                                    setColorB({
                                        ...colorB,
                                        ...item
                                    })
                                }}>
                                    <div style={{
                                        width: "150px",
                                        height: '100px',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        color: item.textColor,
                                        background: item.bgColor
                                    }}>
                                        <div> {wordList["背景颜色"][Context.lan]}<span>{item.bgColor}</span></div>
                                        <div>{wordList["文字颜色"][Context.lan]} <span>{item.textColor}</span></div>
                                    </div>
                                </div>
                            })
                        }

                    </div>
                </div>
            </div>
        </Modal>
        {/**拦截页面 */}
        <Card 
        title={
            <div style={{
                fontWeight: 'bold'
            }}>{wordList["拦截页面"][Context.lan]}</div>
        }

            bordered={false}
            style={{
                marginTop:'20px',
                border:'1px solid #ddd',
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: 'auto',
            }}
        >
            <div style={{
                width: '100%',
                height: '30px',
                display: 'flex',
                gap: '10px',
                alignItems: 'center'
            }}>
                <Select
                    style={{
                        width: '200px'
                    }}
                    defaultValue={"default"}
                    options={[
                        {
                            label: wordList["默认提示文字"][Context.lan],
                            value: 'default'
                        },
                        {
                            label: wordList["攻击检测与黑名单"][Context.lan],
                            value: 'blackIP'
                        },
                        {
                            label: wordList["频率限制"][Context.lan],
                            value: 'frequency'
                        },
                        {
                            label: wordList["网站不存在"][Context.lan],
                            value: 'unExist'
                        },
                        {
                            label: wordList["网关错误"][Context.lan],
                            value: 'netError'
                        },
                        {
                            label: wordList["站点维护"][Context.lan],
                            value: 'processing'
                        },
                        {
                            label: wordList["网关超时"][Context.lan],
                            value: "netTimeout"
                        },
                        {
                            label: wordList["页面不存在"][Context.lan],
                            value: "pageUnExist"
                        },
                        {
                            label: wordList["身份认证"][Context.lan],
                            value: "auth"
                        },
                        {
                            label: wordList["人机认证"][Context.lan],
                            value: 'bot'
                        },
                        {
                            label: wordList["等候室"][Context.lan],
                            value: 'waitingRoom'
                        }
                    ]}></Select>
                <Divider type="vertical"></Divider>
                <span>{wordList["提示文字"][Context.lan]}</span>
                <Input style={{
                    width: '300px'
                }}></Input>
            </div>
            <div style={{
                marginTop: '20px'
            }}>{wordList["页面配色"][Context.lan]}</div>
            <Space
                style={{
                    cursor: "pointer",
                    marginTop: '20px'

                }}
                onClick={() => {
                    setColorA({
                        ...colorA,
                        open: true
                    })
                }}>
                <div style={{
                    border: '2px solid #ddd',
                    borderRadius: '15px',
                    width: '30px',
                    height: '30px',
                    background: colorA.bgColor
                }}>

                </div>
                <div style={{
                    lineHeight: '30px',
                    width: '100px',
                    color: colorA.textColor,
                    fontSize: '20px',
                    textShadow: `0px 0px 3px #999999`,
                    letterSpacing: '5px'
                }}>
                    {wordList["这是文本"][Context.lan]}
                </div>
                <span style={{
                    lineHeight: '30px',
                    color: "#999999"
                }}>{wordList["主要用于人机验证、等候室、身份认证等场景"][Context.lan]}</span>
            </Space>
            <br />
            <Space
                style={{
                    cursor: "pointer",
                    marginTop: '20px'

                }}
                onClick={() => {
                    setColorB({
                        ...colorB,
                        open: true
                    })
                }}>
                <div style={{
                    border: '2px solid #ddd',
                    borderRadius: '15px',
                    width: '30px',
                    height: '30px',
                    background: colorB.bgColor
                }}>

                </div>
                <div style={{
                    lineHeight: '30px',
                    width: '100px',
                    color: colorB.textColor,
                    fontSize: '20px',
                    textShadow: `0px 0px 3px #999999`,
                    letterSpacing: '5px'
                }}>
                    {wordList["这是文本"][Context.lan]}
                </div>
                <span style={{
                    lineHeight: '30px',
                    color: "#999999"
                }}>{wordList["主要用于攻击拦截、频率限制、失败报错等场景"][Context.lan]}</span>
            </Space>
            <Modal
                title={
                    <div style={{
                        width: 'calc(100% - 30px)',
                        display: 'flex',
                        justifyContent: 'space-between',

                    }}>
                        <div>{wordList["自定义HTML页面"][Context.lan]}</div>
                        <Radio.Group
                            style={{
                                flexShrink: 0,
                            }}
                            options={[
                                { label:  wordList["预览模式"][Context.lan], value: 'preview' },
                                { label: wordList["编辑模式"][Context.lan], value: 'editor' },
                            ]}
                            onChange={(e) => {
                                setNowViewHTML(e.target.value)
                            }}
                            value={nowViewHTML}
                            optionType="button"
                            buttonStyle="solid"
                        />
                    </div>
                }
                open={
                    editHTML.open
                }
                onCancel={(e) => {
                    setEditHTML({
                        ...editHTML,
                        open: false
                    })
                }}
                footer={
                    <Space>
                        <BugsLinkButton >{wordList["取消"][Context.lan]}</BugsLinkButton>
                        <Button type="primary" style={{ borderRadius: '10px', background: "#cc1212" }}>{wordList["保存"][Context.lan]}</Button>
                    </Space>
                }
                width={1000}
            >
                <div style={{
                    width: '100%',
                    height: '600px',
                    overflowY: 'auto'
                }}>
                    {
                        nowViewHTML == 'editor'
                            ? <CodeMirror
                                value={editHTML.code}
                                height='600px'
                                extensions={[javascript(), html()]} // 这里可以尝试替换为 Groovy 支持
                                theme={githubLight}
                                onChange={(value, viewUpdate) => {
                                    setEditHTML({
                                        ...editHTML,
                                        code: value
                                    })
                                }}
                            />
                            : <iframe srcDoc={editHTML.code} style={{
                                border: 'none',
                                outline: 'none',
                                width: '100%',
                                height: '100%',
                                overflowY: 'auto'
                            }}></iframe>
                    }

                </div>

            </Modal>
            <div style={{
                marginTop: '20px'
            }}>{wordList["自定义HTML"][Context.lan]}</div>
            <div style={{
                height: 'auto',
                marginTop: '10px',
                width: '100%',
                display: 'flex',
                flexWrap: 'wrap',
                gap: '10px',
                rowGap: '20px'
            }}>
                {
                    htmlList.map(item => {
                        return <div className="html-page-item" onClick={() => {
                            setEditHTML({
                                open: true,
                                code: tempPage[item.value],
                                target: item.value
                            })
                        }}>
                            <div>{wordList[item.title][Context.lan] }</div>
                            <div style={{ width: '5px', height: '2px', background: "#999999" }}></div>
                            <div className="html-page-item-code">{item.code}</div>
                        </div>
                    })
                }
            </div>
        </Card>
    </>
}
export default InterceptPage