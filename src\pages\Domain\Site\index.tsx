import { <PERSON><PERSON>, <PERSON>, Divider, Drawer, message, <PERSON><PERSON>, <PERSON><PERSON><PERSON>rm, <PERSON><PERSON>, Space, Spin, Switch, Table, Tabs, Tag } from "antd"
import "./index.less"
import { useContext, useEffect, useState } from "react"
import EditAndAdd from "./components/EditAndAdd";
import CCProtection from "./components/CCProtection";
import BOTProtection from "./components/BOTProtection";
import AuthProtection from "./components/AuthProtection";
import wordList from "../../../config/wordList";
import ProviderContext from "../../providerContext";
import type { ColumnsType } from 'antd/es/table';
import { Link } from "react-router-dom";
import ViewPageList from "../../../modules/ViewPageList";
import VisitedLineChart from "./components/VistedLineChart";
import ResponsePieChart from "./components/ResponsePieChart";
import TimeRangeSelector from "../../../components/TimeRangeSelector";
import api from "../../../api";
import config from "../../../config";
import moment from "moment";
import SiteDetail from "./SiteDetail";
import CustomTable from "../../../components/CustomTable";
import CustomCard from "../../../components/CustomCard";
import BugsLinkButton from "../../../components/BugsLinkButton";
interface DataType {
    rank: number;
    ip: string;
    times: number;
    placement: string;
}

const Domain = (props: any) => {
    const Context = useContext(ProviderContext)
    const [openEdit, setOpenEdit] = useState({
        open: false,
        type: 'add',
        data: {}
    })
    const [openDrawer, setOpenDrawer] = useState({
        show: false,
        id: ""
    })
    const [openCCEdit, setOpenCCEdit] = useState({
        open: false,
        type: 'add'
    })
    const [openBotEdit, setOpenBotEdit] = useState({
        open: false
    })
    const [openAuthEdit, setOpenAuthEdit] = useState({
        open: false
    })
    const [data, setData] = useState<any>([])
    const [getting, setGetting] = useState(false)
    const editSite = (id, newdata, index) => {
        setGetting(true)
        api.site.editSite(id, newdata)
            .then(res => {
                setGetting(false)
                if (res?.data?.data) {
                    message.success(wordList["修改成功"][Context.lan])
                    setData([
                        ...data.slice(0, index),
                        {
                            ...data[index],
                            ...newdata
                        },
                        ...data.slice(index + 1)
                    ])
                }
            })
    }
    /**站点列表 */
    const columns: ColumnsType<any> = [
        {
            title: wordList["站点"][Context.lan],
            key: 'domain',
            dataIndex: 'domain',
            width: 650,
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '50px',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                }}>
                    <div style={{
                        display: 'flex',
                        width: '100%',
                        height: '30px',
                        fontWeight: 200,
                        lineHeight: '30px',
                        alignItems: 'center',
                        gap: '10px'
                    }}>{
                            //config.siteInfo.typeIcon[record.type]
                        }

                        <a
                            style={{
                                color: "#cc1212",
                                fontWeight:"bold"
                            }}
                            onClick={() => {
                                setOpenDrawer({
                                    show: true,
                                    id: record.id
                                })
                            }}>
                            {/*to={"/domain/siteDetail/" + record.id}*/}
                            {
                                record?.title || wordList["未命名"][Context.lan]
                            }
                        </a>
                    </div>
                    <div
                        className="domain-column"
                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {
                            (record?.server_names || []).length == 0 ?
                                wordList["未配置匹配方式"][Context.lan]
                                : (record.server_names.length == 0 && record.server_names[0] == '*'
                                    ? wordList["通配所有域名"][Context.lan] : (<Space>
                                        {
                                            record?.server_names.map((item, index) => {
                                                return <>
                                                    {
                                                        index > 0
                                                            ? <Divider type="vertical"></Divider>
                                                            : null
                                                    }
                                                    {item}

                                                </>
                                            })
                                        }
                                    </Space>))
                        }
                    </div>
                </div>
            }
        }, {
            title: wordList["站点类型"][Context.lan],
            key: 'type',
            fixed: 'right',
            dataIndex: 'type',
            align: 'center',
            width: 100,
            render: (type: any, record: any, index: number) => {
                return config.siteInfo.type[type]
            }
        }, {
            title: wordList["监听端口"][Context.lan],
            key: 'ports',
            dataIndex: 'ports',
            fixed: 'right',
            width: 100,
            align: 'center',
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    textAlign: 'center',
                    justifyContent: 'center'
                }}>
                    {record.ports.map(item => {
                        return <div >
                            {item}
                        </div>
                    })}
                </div>
            }
        },
        /*{
            title: wordList["后端端口"][Context.lan],
            key: 'backend',
            dataIndex: 'backend',
            width: 100,
            render: (_: any, record: any, index: number) => {
                if(!record.backend) return "-"
                return record.backend+":"+record.backendPort
            }
        },*/
        {
            title: wordList["上游节点"][Context.lan],
            key: 'upstreams',
            fixed: 'right',
            dataIndex: 'upstreams',
            align: 'center',
            width: 100,
            render: (_: any, record: any, index: number) => {
                return (record?.upstreams || []).length + "节点"
            }
        }, {
            title: wordList["运行模式"][Context.lan],
            key: 'mode',
            fixed: 'right',
            dataIndex: 'mode',
            align: 'center',
            width: 100,
            render: (_: any, record: any, index: number) => {
                const tagStyle: any = {
                    border: 'none', width: 'auto', minWidth: '50px', height: '25px', fontSize: '14px', borderRadius: '5px', textAlign: 'center', lineHeight: '25px'
                }
                return [
                    <Tag style={tagStyle} color="#0fc6c2">
                        {wordList["防护"][Context.lan]}
                    </Tag>,//防护
                    <Tag style={tagStyle} color="#feab12">
                        {wordList["观察"][Context.lan]}
                    </Tag>,//观察
                    <Tag style={tagStyle} color="#f5222d">
                        {wordList["维护"][Context.lan]}
                    </Tag>//维护
                ][record.mode]
            }
        }, /*{
            title: wordList["高级防护"][Context.lan],
            key: 'advancedProtection',
            dataIndex: 'advancedProtection',
            width: 115,
            render: (_: any, record: any, index: number) => {
                return <Space>

                    <CheckableTag
                        key={"CC"}
                        style={{
                            width:'auto'
                        }}
                        checked={record.advancedProtection['CC'].open}
                        onChange={checked => {
                            console.log(checked)
                            setOpenCCEdit({
                                type:"edit",
                                open:true
                            })
                        }}
                    >
                        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="61446" data-spm-anchor-id="a313x.search_index.0.i51.75303a81B5suLu" width="18" height="18"><path d="M358.4 153.6l92.16 327.68c5.12 10.24-5.12 25.6-15.36 30.72H291.84c-15.36 0-25.6-10.24-25.6-25.6v-5.12L358.4 153.6zM665.6 870.4l-92.16-327.68v-5.12c0-15.36 10.24-25.6 25.6-25.6H742.4c15.36 5.12 20.48 20.48 15.36 30.72L665.6 870.4z" fill="#ffffff" p-id="61447" data-spm-anchor-id="a313x.search_index.0.i48.75303a81B5suLu" ></path><path d="M25.6 537.6c-15.36 0-25.6-10.24-25.6-25.6s10.24-25.6 25.6-25.6h143.36c10.24 0 20.48-5.12 25.6-20.48L307.2 71.68c10.24-25.6 35.84-40.96 61.44-35.84 20.48 10.24 35.84 20.48 40.96 35.84l256 860.16 117.76-394.24c10.24-30.72 40.96-56.32 71.68-56.32h143.36c15.36 0 25.6 10.24 25.6 25.6s-10.24 25.6-25.6 25.6h-143.36c-10.24 0-20.48 5.12-25.6 20.48L716.8 947.2c-5.12 15.36-15.36 30.72-35.84 35.84-25.6 10.24-56.32-5.12-61.44-35.84L358.4 87.04 240.64 481.28c-10.24 30.72-40.96 56.32-71.68 56.32H25.6z" fill="" p-id="61448"></path></svg>

                        {
                             wordList["CC防护"][Context.lan]
                        }
                    </CheckableTag>

                    <CheckableTag
                        key={"BOT"}
                        style={{
                            width:'auto'
                        }}
                        checked={record.advancedProtection['BOT'].open}
                        onChange={checked => {
                            setOpenBotEdit({
                                open: true
                            })
                        }}
                    >
                        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="62651" width="18" height="18"><path d="M757.461333 597.333333a96 96 0 0 1 96 96v38.613334A160 160 0 0 1 797.696 853.333333C730.88 910.677333 635.306667 938.709333 512 938.709333c-123.306667 0-218.794667-28.032-285.482667-85.333333a160 160 0 0 1-55.722666-121.344v-38.698667A96 96 0 0 1 266.794667 597.333333h490.666666z m0 64h-490.666666a32 32 0 0 0-32 32v38.698667c0 27.989333 12.202667 54.570667 33.450666 72.789333C321.92 850.986667 402.773333 874.709333 512 874.709333c109.226667 0 190.208-23.722667 244.010667-69.930666a96 96 0 0 0 33.450666-72.832v-38.613334a32 32 0 0 0-32-32zM507.648 85.632L512 85.333333a32 32 0 0 1 31.701333 27.690667l0.298667 4.309333v32h149.333333a96 96 0 0 1 96 96v192.213334a96 96 0 0 1-96 96h-362.666666a96 96 0 0 1-96-96V245.333333A96 96 0 0 1 330.666667 149.333333h149.333333v-32a32 32 0 0 1 27.648-31.701333L512 85.333333l-4.352 0.298667zM693.333333 213.333333h-362.666666a32 32 0 0 0-32 32v192.213334c0 17.664 14.336 32 32 32h362.666666a32 32 0 0 0 32-32V245.333333a32 32 0 0 0-32-32z m-277.333333 64a53.333333 53.333333 0 1 1 0 106.624 53.333333 53.333333 0 0 1 0-106.624z m191.658667 0a53.333333 53.333333 0 1 1 0 106.624 53.333333 53.333333 0 0 1 0-106.624z" fill="#212121" p-id="62652"></path></svg>
                        {
                             wordList["BOT防护"][Context.lan]
                        }
                    </CheckableTag>

                    <CheckableTag
                        style={{
                            width:'auto'
                        }}
                        key={"AUTH"}
                        checked={record.advancedProtection['AUTH'].open}
                        onChange={checked => {
                            setOpenAuthEdit({
                                open: true
                            })
                        }}
                    >
                        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="63766" width="18" height="18"><path d="M839.682194 205.238857v318.464c0 96.256-53.906286 192.731429-160.182857 286.866286-36.352 31.378286-74.898286 59.830857-114.541714 84.626286-8.484571 5.339429-16.969143 9.947429-25.161143 14.409142l-10.971429 5.997715c-50.907429-28.818286-102.4-65.097143-148.918857-105.033143C273.55648 716.434286 219.577051 619.958857 219.577051 523.702857V205.238857l310.857143-77.312L839.682194 205.238857z m45.129143-63.926857L538.479909 54.710857a27.062857 27.062857 0 0 0-17.334858-0.073143L174.521051 141.238857A36.205714 36.205714 0 0 0 146.287909 177.005714v346.697143c0 119.003429 62.756571 233.910857 186.514285 341.577143a1134.226286 1134.226286 0 0 0 165.814857 115.565714c2.413714 1.170286 4.608 2.194286 6.729143 3.072 3.510857 1.609143 6.582857 2.925714 9.654857 5.046857 4.827429 2.925714 10.605714 4.388571 16.384 4.388572a31.451429 31.451429 0 0 0 16.091429-4.242286 880.859429 880.859429 0 0 0 179.126857-123.904c123.684571-107.593143 186.441143-222.500571 186.441143-341.577143v-346.697143a36.205714 36.205714 0 0 0-28.233143-35.620571z" fill="#000000" p-id="63767"></path><path d="M586.315337 375.296a51.785143 51.785143 0 0 1-52.516571 50.907429c-27.428571 0-52.516571-24.283429-52.516572-50.907429 0-28.013714 23.552-50.834286 52.516572-50.834286 31.451429 0 52.516571 20.48 52.516571 50.834286m-179.931428-1.682286c0 49.883429 31.305143 94.866286 78.262857 112.932572v272.091428c0 20.845714 15.067429 35.84 35.84 35.84 20.772571 0 35.84-14.994286 35.84-35.84v-27.428571h52.516571a27.867429 27.867429 0 0 0 29.110857-29.184 27.867429 27.867429 0 0 0-29.110857-29.184h-52.516571v-21.430857l55.881143 3.072c17.846857 0 30.72-12.214857 30.72-29.110857a27.867429 27.867429 0 0 0-29.110858-29.184h-52.516571V492.105143a121.344 121.344 0 0 0 96.694857-118.491429c0-67.510857-56.466286-122.441143-125.805714-122.441143-68.242286 0-125.805714 56.100571-125.805714 122.441143" fill="#000000" p-id="63768"></path></svg>
                        {
                             wordList["身份防护"][Context.lan]
                        }
                    </CheckableTag>
                </Space>
            }
        },*/ /*{
            title: wordList["今日防护情况"][Context.lan],
            key: 'status',
            dataIndex: 'status',
            width: 100,
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100px',
                    height: '100%',
                    display: 'flex',
                    flexDirection: "column",
                    gap: '5px',

                }}>
                    <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        height: '30px',
                        lineHeight: '30px',
                    }}>
                        <span style={{ color: "#999999" }}>{wordList["拦截"][Context.lan]}</span>
                        <span>{record.statistic.attack || 0}</span>
                    </div>

                    <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        height: '30px',
                        lineHeight: '30px',
                    }}>
                        <span style={{ color: "#999999" }}>{wordList["请求"][Context.lan]}</span>
                        <span>{record.statistic.requestion || 0}</span>
                    </div>
                </div>
            }
        },*/
        /*{
            title: wordList["运行情况"][Context.lan],
            width: 80,
            dataIndex: "enabled",
            key: 'enabled',
            render: (enabled, record, index) => {
                return <Popconfirm title={wordList[`确认${enabled ? '关闭' : '开启'}该站点防护`][Context.lan]}
                    onConfirm={() => {
                        editSite(record.id, { enabled: !enabled }, index)

                    }}
                >
                    <Switch checked={enabled} checkedChildren={wordList["已启用"][Context.lan]} unCheckedChildren={wordList["未启用"][Context.lan]}></Switch>
                </Popconfirm>
            }
        },*/
        {
            title: wordList["创建时间"][Context.lan],
            key: 'created_at',
            dataIndex: 'created_at',
            fixed: 'right',
            align: 'center',
            width: 200,
            render: (created_at: string, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: "column",
                    gap: '5px',

                }}>
                    {moment(created_at).format('YYYY/MM/DD HH:mm:ss')}
                </div>
            }
        },
        {
            title: wordList["备注"][Context.lan],
            key: 'description',
            fixed: 'right',
            dataIndex: 'description',
            align: 'center',
            width: 100,
            render: (remark: any, record: any, index: number) => {
                return record?.description || '-'
            }
        },
        {
            title: '',
            dataIndex: 'opetation',
            fixed: 'right',
            width: 30,
            render: (_, record, index) => {
                return <Popover trigger={"hover"}
                    content={
                        <div style={{
                            width: 'auto',
                            display:'flex',
                            flexDirection:'column'
                        }}>
                            <BugsLinkButton
                                onClick={() => {
                                    setOpenEdit({
                                        open: true,
                                        data: record,
                                        type: 'edit'
                                    })
                                }}
                                style={{
                                    color: "#cc1212",
                                }}>{wordList["编辑"][Context.lan]}</BugsLinkButton>
                            <Divider style={{ marginTop: '10px', marginBottom: '10px' }}></Divider>
                            {
                                ['防护模式', '观察模式', '维护模式'].map((item, modeValue) => {
                                    return <Popconfirm
                                        disabled={record.mode == modeValue}
                                        title={wordList["确定将此站点模式更改为"][Context.lan] + wordList[item][Context.lan]}
                                        onConfirm={() => {
                                            editSite(record.id, { mode: modeValue }, index)
                                        }}
                                    >
                                        <BugsLinkButton
                                            disabled={record.mode == modeValue}
                                            style={{
                                                color: "#cc1212",
                                            }}>
                                            {wordList[item][Context.lan]}
                                        </BugsLinkButton>
                                    </Popconfirm>
                                })
                            }

                        </div>
                    }>
                    <Button
                        style={{
                            border: 'none',
                            background: 'transparent',
                            boxShadow: 'none'
                        }}
                        icon={
                            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="64884" width="24" height="24"><path d="M512 42.666667a469.333333 469.333333 0 1 0 469.333333 469.333333A469.333333 469.333333 0 0 0 512 42.666667z m0 864a394.666667 394.666667 0 1 1 394.666667-394.666667 395.146667 395.146667 0 0 1-394.666667 394.666667z" p-id="64885" fill="#cc1212"></path><path d="M304.906667 512m-66.666667 0a66.666667 66.666667 0 1 0 133.333333 0 66.666667 66.666667 0 1 0-133.333333 0Z" p-id="64886" fill="#cc1212"></path><path d="M512 512m-66.666667 0a66.666667 66.666667 0 1 0 133.333334 0 66.666667 66.666667 0 1 0-133.333334 0Z" p-id="64887" fill="#cc1212"></path><path d="M719.093333 512m-66.666666 0a66.666667 66.666667 0 1 0 133.333333 0 66.666667 66.666667 0 1 0-133.333333 0Z" p-id="64888" fill="#cc1212"></path></svg>
                        }></Button>
                </Popover>
            }
        }
    ]
    const [nowView, setNowView] = useState("domainList")
    const [pageInfo, setPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const getSite = (page = 1, pageSize = 10) => {
        setGetting(true)
        api.site.getSiteList({
            page: page,
            page_size: pageSize,
            sort: 'id'
        })
            .then(res => {
                setGetting(false)
                if (res?.data?.data) {
                    setData(res.data.data.rows)
                    setPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    useEffect(() => {
        getSite()
    }, [])
    const DomainList = (
        <CustomCard title={wordList["站点列表"][Context.lan]}
            extra={
                <Button
                    onClick={() => {
                        setOpenEdit({
                            type: 'add',
                            data: {},
                            open: true
                        })
                    }}
                    type="primary"
                    style={{
                        borderRadius: '5px',
                        background: "#cc1212",
                        border: 'none'
                    }}> {wordList["添加站点"][Context.lan]}</Button>
                    
            }
            style={{
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: 'calc(100% - 60px)'
            }}>
            <div
                className="domain-list-container"
                style={{
                    width: '100%',
                    height: "100%",
                    paddingLeft: '20px',
                    paddingRight: '20px',
                    boxSizing: 'border-box'
                }}>
                <Spin spinning={getting}>
                    <CustomTable
                        useBorder={true}
                        columns={columns}
                        dataSource={data}
                        loading={false}
                        onChange={() => { }}
                        scroll={{ x: 'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
                        size={'middle'}
                    ></CustomTable>
                </Spin>

            </div>

        </CustomCard>
    )

    const [viewIp, setViewIp] = useState<DataType[]>([{
        rank: 1,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 2,
        ip: "*********",
        placement: "美国",
        times: 30
    }, {
        rank: 3,
        ip: "*********",
        placement: "德国",
        times: 30
    }, {
        rank: 4,
        ip: "*********",
        placement: "日本",
        times: 30
    }, {
        rank: 5,
        ip: "*********",
        placement: "俄罗斯",
        times: 30
    }, {
        rank: 6,
        ip: "*********",
        placement: "韩国",
        times: 30
    }, {
        rank: 7,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 8,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 9,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }, {
        rank: 10,
        ip: "*********",
        placement: "中国-上海",
        times: 30
    }])
    const IpColumns_1: ColumnsType<DataType> = [
        {
            title: wordList['排名'][Context.lan],
            dataIndex: 'rank',
            align: 'center',
            key: 'rank',
            render: (rank: number) => {
                if (rank == 1) {
                    return <span style={{ color: "#f4424d" }}>1</span>
                }
                if (rank == 2) {
                    return <span style={{ color: "#feab12" }}>2</span>
                }
                if (rank == 3) {
                    return <span style={{ color: "#cc1212" }}>3</span>
                }
                return <span style={{ color: "#999" }}>{rank}</span>
            },
        },
        {
            title: wordList['访问IP'][Context.lan],
            dataIndex: 'ip',
            align: 'center',
            key: 'ip',
        },
        {
            title: wordList['归属地'][Context.lan],
            dataIndex: 'placement',
            key: 'placement',
            align: 'center',
        },
        {
            title: wordList['访问次数'][Context.lan],
            key: 'times',
            align: 'center',
            dataIndex: 'times',
        },
    ];
    const Analysi = (
        <div
            className="analysi-card-container"
        >
            <div
                className="analysi-card-container-layout"
            >
                <div className="page-ip-list">
                    <div className="page-card-title">{wordList['访问IP 前十'][Context.lan]}</div>
                    <div style={{
                        width: '100%',
                        height: 'calc(100% - 40px)',
                        display: 'flex',
                    }}>
                        <div
                            className="analysi-table-container"
                        >
                            <CustomTable

                                size="small"

                                rowClassName={() => "home-page-table-row"}
                                style={{
                                    border: 'none',
                                    width: '100%'
                                }}
                                dataSource={viewIp}
                                columns={IpColumns_1}
                                pagination={
                                    {
                                        hideOnSinglePage: true
                                    }
                                }

                            ></CustomTable>
                        </div>


                    </div>
                </div>
                <VisitedLineChart></VisitedLineChart>

            </div>
            <div
                className="analysi-overview-container"
                style={{

                }}>


                <ViewPageList></ViewPageList>



                <ResponsePieChart></ResponsePieChart>

            </div>
        </div>
    )
    return <div className="domain-container">
        <Drawer
            onClose={() => {
                setOpenDrawer({
                    show: false,
                    id: ""
                })
            }}
            destroyOnClose
            width={"90%"}
            open={
                openDrawer.show
            }
            title={wordList["站点详情"][Context.lan]}
            extra={
                <a style={{ color: "#cc1212" }} onClick={() => {
                    window.open("/domain/siteDetail/" + openDrawer.id)
                }}>{wordList['新页面打开'][Context.lan]}</a>
            }
        >
            <SiteDetail onUpdate={() => {
                setOpenDrawer({
                    show: false,
                    id: ""
                })
                getSite(pageInfo.page, pageInfo.page_size)
            }} id={openDrawer.id}></SiteDetail>
        </Drawer>
        {
            openEdit?.open ? <EditAndAdd
                data={openEdit.data || {}}
                type={openEdit.type}
                updateSite={getSite}
                close={() => {
                    setOpenEdit({
                        ...openEdit,
                        open: false
                    })
                }}></EditAndAdd> : null
        }
        {
            openCCEdit?.open ? <CCProtection type={openCCEdit.type} close={() => {
                setOpenCCEdit({
                    ...openCCEdit,
                    open: false
                })
            }}></CCProtection> : null
        }
        {
            openBotEdit?.open ? <BOTProtection close={() => {
                setOpenBotEdit({
                    open: false
                })
            }}></BOTProtection> : null
        }
        {
            openAuthEdit?.open ? <AuthProtection close={() => {
                setOpenAuthEdit({
                    open: false
                })
            }}></AuthProtection> : null
        }
        {

            /*
            <div
            className="domain-type-selector">
            <Tabs size="small" onChange={(e) => {
               
                setNowView(e)
            }}>
                <Tabs.TabPane tab={wordList['站点分析'][Context.lan]} key={"analysi"}></Tabs.TabPane>
                <Tabs.TabPane tab={wordList['站点列表'][Context.lan]} key={"domainList"}></Tabs.TabPane>
            </Tabs>

        </div>
            */
        }
        {
            nowView == 'analysi'
                ? <div style={{ width: "300px", marginTop: '20px' }} className="analysi-time-selector"><TimeRangeSelector></TimeRangeSelector></div>
                : null
        }
        {
            nowView == 'analysi'
                ? Analysi
                : DomainList
        }

    </div>
}
export default Domain