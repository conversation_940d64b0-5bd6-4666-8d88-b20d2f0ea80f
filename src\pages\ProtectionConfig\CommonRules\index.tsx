import { <PERSON><PERSON>, Card, Space } from "antd"
import "./index.less"
import IPGroup from "./components/IPGroup"
import InterceptPage from "./components/InterceptPage"
import SharePlan from "./components/SharePlan"
import Sensitive from "./components/Sensitive"
import ProviderContext from "../../providerContext"
import wordList from "../../../config/wordList"
import { useContext } from "react"
import CustomCard from "../../../components/CustomCard"
const CommonRules = (props: any) => {
    const Context=useContext(ProviderContext)
    return <CustomCard
        title={wordList["通用配置"][Context.lan]}
        bordered
        className="common-rules"
        style={{
            borderRadius: '10px',
            width: '100%',
            paddingLeft: 0,
            paddingRight: 0,
            height: '100%',
            overflowY: 'auto'
        }}>
        <div style={{
            width: '100%',
            height: '100%',
            overflowY: 'auto'
        }}>
            <IPGroup></IPGroup>
            <InterceptPage></InterceptPage>
            <Sensitive></Sensitive>
            <SharePlan></SharePlan>
        </div>


    </CustomCard>
}
export default CommonRules