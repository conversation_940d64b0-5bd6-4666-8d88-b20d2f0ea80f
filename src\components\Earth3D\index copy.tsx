import world from '../../assets/world.json';
import * as echarts from 'echarts'; //全局引入 ，可按需引入
import { useEffect, useRef, useState } from 'react';
import "./index.less"
import linesJson from "../../assets/lines.json"
import React from 'react';
import ReactDOM from 'react-dom';
import { CaretUpFilled } from '@ant-design/icons';
const WorldMap = (props) => {
    if (!props?.data) {
        props = {
            ...(props || {}),
            data: []
        }
    }
    const chartRef = useRef<any>(null);
    const topNumber = props.data[0]?.value || 0;
    const bottomNumber = props.data[props.data.length - 1]?.value || 0;
    const [rootNode, setRootNodes] = useState<any>([])

    const mapOption = (mapName, data) => {
        const myChart = echarts.init(chartRef.current);

        echarts.registerMap(mapName, data);
        // 定义蓝紫色渐变
        var bluePurpleGradient = {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
                {
                    offset: 0, color: '#8675ff' // 蓝色
                },
                {
                    offset: 1, color: '#ff2156' // 紫色
                }
            ],
            global: false
        };
        let option = {
            animationDuration: 1000,
            tooltip: {
                backgroundColor: 'rgba(21, 24, 45, 0.9)', // 提示框浮层的背景颜色。
                textStyle: {
                    // 提示框浮层的文本样式。
                    color: '#fff',
                    fontSize: 14,
                },
                extraCssText: 'border-color: rgba(21, 24, 45, 0.9);',
                formatter: function (params) {
                    //数据格式化
                    const val = params.value ? params.value : 0;
                    if (params.value) {
                        return (
                            params.name + '<br />' + params.seriesName + '：' + val
                        );
                    } else {
                        return '暂无数据';
                    }
                },
            },
            visualMap: {
                min: 0,
                max: topNumber,
                left: 'right',
                top: 'bottom',
                text: [topNumber, bottomNumber], //取值范围
                inRange: {
                    color: ['#fff', '#6E92FF'], //取值范围的颜色
                },
                show: false, //图注
            },
            geo: [{
                map: 'world',
                roam: false, //不开启缩放和平移
                zoom: 1.23, //视角缩放比例
                itemStyle: {
                    normal: {
                        areaColor: {
                            type: 'pattern',
                            image: createHexagonPattern(3, '#fff'), // 创建白色点状图案
                            repeat: 'repeat'
                        },
                        borderColor: 'rgba(0, 0, 0, 0)',
                    },
                    emphasis: {
                        areaColor: {
                            type: 'pattern',
                            image: createHexagonPattern(3, '#fff'),
                            repeat: 'repeat'
                        },
                        //areaColor: '#4BD6C7', //鼠标选择区域颜色
                        shadowOffsetX: 0,
                        shadowOffsetY: 0,
                        shadowBlur: 20,
                        borderWidth: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                    },
                },
            }],
            series: [
                {
                    type: 'lines',
                    coordinateSystem: "geo", // 对应上方配置
                    zlevel: 2,
                    effect: {
                        show: true,
                        delay: 0,
                        period: 1, //箭头指向速度，值越小速度越快
                        trailLength: 0.75, //特效尾迹长度[0,1]值越大，尾迹越长重
                        symbol: 'none', //箭头图标
                        symbolSize: 3, //图标大小
                        smooth: true
                    },
                    data: [],
                },
            ],
        };
        function createHexagonPattern(size, color) {
            var canvas = document.createElement('canvas');
            // 调整画布尺寸为单个六边形单元尺寸（重要修改）
            var hexWidth = size * 2;
            var hexHeight = Math.sqrt(3) * size;
            canvas.width = hexWidth;
            canvas.height = hexHeight;

            var ctx: any = canvas.getContext('2d');
            ctx.fillStyle = color;

            // 绘制单个六边形（去除了位置偏移）
            function drawHexagon(centerX, centerY, radius) {
                ctx.beginPath();
                for (var i = 0; i < 6; i++) {
                    var angle = Math.PI / 3 * i;
                    var x = centerX + radius * Math.cos(angle);
                    var y = centerY + radius * Math.sin(angle);
                    if (i === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.closePath();
                ctx.fill();
            }

            // 计算精确的六边形参数（关键修改）
            var radius = size; // 六边形外接圆半径
            var hexCenterX = radius;
            var hexCenterY = Math.sqrt(3) * radius / 2;

            // 绘制中心六边形
            drawHexagon(hexCenterX, hexCenterY, radius);

            // 绘制右侧相邻六边形（确保边线重合）
            drawHexagon(hexCenterX + 1.5 * radius, hexCenterY, radius);

            // 绘制下方相邻六边形（形成蜂窝结构）
            drawHexagon(hexCenterX + 0.75 * radius, hexCenterY + Math.sqrt(3) / 2 * radius, radius);

            return canvas;
        }
        let lineData: any = linesJson.map(item => {
            return {
                ...item,
                lineStyle: {
                    ...item.lineStyle,
                    color: bluePurpleGradient
                }
            }
        })

        // 提取起点和终点数据
        var startPoints = [];
        var endPoints = [];

        let last = -1
        const drawLine = (index: number) => {

            if (index < last) return
            if (index >= lineData.length) {
                setRootNodes([])
                myChart.setOption({
                    series: [
                        {
                            ...option.series[0],
                            data: []
                        }
                    ]
                }); //绘图
                return
            }
            myChart.clear()
            let nums = Math.floor(Math.random() * 5) + 1;
            last = index
            let itemData: any = [...lineData.slice(index, index + nums)]

            //myChart.convertToPixel('geo', [128.3324, 89.5344]); // 参数 'geo' 等同于 {geoIndex: 0}
            option = {
                ...option,
                series: [
                    {
                        ...option.series[0],
                        data: itemData.map(item => {

                            return item
                        })
                    }
                ]
            }
            myChart.setOption(option); //绘图
            let nodes: any = [

            ]
            const divStyle: React.CSSProperties = {

                zIndex: 2,
                position: "absolute",
                transform: 'translateX(-50%) translateY(20px)',
                width: "200px",
                height: '40px',
                background: 'white',
                borderRadius: '10px'
            }
            itemData.map((item) => {
                let startPoint = myChart.convertToPixel({ geoIndex: 0 }, item.coords[0])
                let endPoint = myChart.convertToPixel({ geoIndex: 0 }, item.coords[1])
                let id = Math.random().toString(32).slice(2, 10)
                nodes = [
                    ...nodes,
                    //创建起点
                    ReactDOM.createPortal(
                        <>
                            <div
                                className='line-point-container'
                                id={id + "_start"}
                                style={{
                                    ...divStyle,
                                    left: startPoint[0] + 'px',
                                    top: startPoint[1] + 'px',
                                }}>
                                <div style={{
                                    width: "100%",
                                    height: "100%",
                                    position: 'relative',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '10px',
                                    paddingLeft: '10px',
                                    paddingRight: '10px'
                                }}>
                                    <CaretUpFilled style={{ position: 'absolute', transform: 'translateY(-20px) translateX(-50%)', marginLeft: '50%', fontSize: "30px", color: "white", backdropFilter: "shadow(0,-3px,3px,rgba(0,0,0,0.25))" }} />
                                    <div style={{
                                        width: "auto",
                                        height: '25px',
                                        lineHeight: "25px",
                                        fontSize: "16px",
                                        paddingLeft: '10px',
                                        paddingRight: "10px",
                                        fontWeight: 700,
                                        background: "#cc1212",
                                        borderRadius: '5px'
                                    }}>美国</div>
                                    <div style={{
                                        lineHeight: "30px",
                                        fontFamily: 'AlimamaShuHeiTi',
                                        fontSize: '16px'
                                    }}>
                                        192.168.0.1
                                    </div>
                                </div>


                            </div>
                        </>,
                        chartRef.current,),
                    //创建终点
                    ReactDOM.createPortal(
                        <>
                            <div
                                className='line-point-container'
                                id={id + "_end"}
                                style={{
                                    ...divStyle,
                                    left: endPoint[0] + 'px',
                                    top: endPoint[1] + 'px',
                                }}>
                                <div style={{
                                    width: "100%",
                                    height: "100%",
                                    position: 'relative',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '10px',
                                    paddingLeft: '10px',
                                    paddingRight: '10px'
                                }}>
                                    <CaretUpFilled style={{ position: 'absolute', transform: 'translateY(-20px) translateX(-50%)', marginLeft: '50%', fontSize: "30px", color: "white", backdropFilter: "shadow(0,-3px,3px,rgba(0,0,0,0.25))" }} />
                                    <div style={{
                                        width: "auto",
                                        height: '25px',
                                        lineHeight: "25px",
                                        fontSize: "16px",
                                        paddingLeft: '10px',
                                        paddingRight: "10px",
                                        fontWeight: 700,
                                        background: "#ff8f69",
                                        borderRadius: '5px'
                                    }}>美国</div>
                                    <div style={{
                                        lineHeight: "30px",
                                        fontFamily: 'AlimamaShuHeiTi',
                                        fontSize: '16px'
                                    }}>
                                        192.168.0.1
                                    </div>
                                </div>

                            </div>
                        </>,
                        chartRef.current,)
                ]

                console.log("创建完成", id + "_start")
                setTimeout(() => {
                    //document.getElementById(id + "_end")?.remove()
                    //document.getElementById(id + "_start")?.remove()
                }, 1000)
            })
            setRootNodes(nodes)
            setTimeout(() => {

                drawLine(index + nums + 1)
            }, 1000)
        }
        //点击画布内还是画布外
        drawLine(0)

    };
    const loadingWorld = () => {
        mapOption('world', world); //初始化-创建中国地图
    };

    useEffect(() => {
        loadingWorld();
    }, []);

    return <div style={{ width: '100%', minHeight: '500px', height: 'calc(100vh - 200px)' }} ref={chartRef} >
        {
            rootNode.map(item => {
                return item
            })
        }</div>;
};
export default WorldMap;
