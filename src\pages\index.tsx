
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Divider, <PERSON>u, message, Modal, Radio, Space, Tooltip, notification } from "antd"
import { MenuProps } from "antd";
import { lazy, Suspense, useEffect, useMemo, useState } from "react";
import { Link, Route, Routes, useLocation, useNavigate, useParams } from "react-router-dom";
import { Content } from "antd/es/layout/layout";
import logo from "../assets/logo.png"
import "./index.less"
import { breadcrumbNameMap } from "../config/routerRefer";
import { FileTextOutlined, LogoutOutlined, MenuFoldOutlined, MenuUnfoldOutlined } from "@ant-design/icons";
import ProviderContext from "./providerContext";
import wordList from "../config/wordList";
import api from "../api";
import Active from "../components/Active";
const Login = lazy(() => import("./Login"));
const Domain = lazy(() => import("./Domain/Site"));
const DomainLicense = lazy(() => import("./Domain/License"));
const DomainGlobal = lazy(() => import("./Domain/GLobalConfigure"));
const LogAttack = lazy(() => import("./Log/Attack"));
const LogFrequency = lazy(() => import("./Log/Frequency"));
const LogBot = lazy(() => import("./Log/Bot"));
const LogAuth = lazy(() => import("./Log/Auth"));
const LogAttacker = lazy(() => import("./Log/Attacker"));
const LogBan = lazy(() => import("./Log/Ban"));
const LogWaitingRoom = lazy(() => import("./Log/WaitingRoom"));
const ConfigureFrequency = lazy(() => import("./ProtectionConfig/Frequency"));
const ConfigureModules = lazy(() => import("./ProtectionConfig/Modules"));
const ConfigureCommon = lazy(() => import("./ProtectionConfig/CommonRules"));
const ConfigureCustom = lazy(() => import("./ProtectionConfig/CustomRules"));
const HomePage = lazy(() => import("./HomePage"))
const SiteDetail = lazy(() => import("./Domain/Site/SiteDetail"))
const Account = lazy(() => import("./Setting/Account"))
const Timeout = lazy(() => import("./Timeout"))
//const Setting = lazy(() => import("../Setting"));
//const Dev = lazy(() => import("../Dev"));
type MenuItem = Required<MenuProps>['items'][number];
const PageEntrance = () => {

    const pathRefer: any = {
        "homePage": "/homePage",
        "homePage-screen": "/homePage",
        "domain": "/domain/domainList",
        "domain-list": "/domain/domainList",
        "domain-license": '/domain/domainLicense',
        "domain-global": '/domain/domainGlobal',
        'log': "/log/logAttack",
        'log-attack': "/log/logAttack",
        'log-attacker': "/log/logAttacker",
        'log-ban': "/log/logBan",
        "log-frequency": "/log/logFrequency",
        "log-bot": "/log/logBot",
        "log-auth": '/log/logAuth',
        "log-waitingRoom": '/log/logWaitingRoom',

        "configure": '/configure/configureFrequency',
        "configure-frequency": '/configure/configureFrequency',
        "configure-modules": '/configure/configureModules',
        "configure-common": '/configure/configureCommon',
        'configure-custom': '/configure/configureCustom',
        'setting-account': '/setting/settingAccount'
    }

    /**对/domain /log /configure 一级路由配置默认指向 */
    let indexPath = {
        homePage: 'homePage-screen',
        'domain': 'domain-list',
        log: 'log-attack',
        setting: 'setting-account',
        configure: 'configure-custom'
    }
    let parentRefer = {
        "homePage": pathRefer[indexPath['homePage']],
        "domain": pathRefer[indexPath['domain']],
        "log": pathRefer[indexPath['log']],
        "setting": pathRefer[indexPath['setting']],
        "configure": pathRefer[indexPath['configure']],
    }
    const navigate = useNavigate()
    const locations = useLocation()
    const [nowTab, setNowTab] = useState("homePage")
    const [openKeys, setOpenKeys] = useState<any>([])
    const [provideConfig, setProviderConfig] = useState<any>({
        lan: "cn",
        timeOutDay: 0
    })
    const params = useParams()
    useEffect(() => {
        if (params["*"] == '') {
            setNowTab("homePage-screen")
        }
        let pathName = locations.pathname.split("/")
        if (!pathName[1] || !parentRefer[pathName[1]]) {
            navigate("/homePage")
            return
        }
        if (!pathName[2]) {
            //如果没有二级路由，则转入该一级路由下的第一个子路由
            setNowTab(indexPath[pathName[1]])
            setOpenKeys([pathName[1]])
            return
        }
        const regex = /\B([A-Z])/g;
        // 使用 $1 引用匹配到的大写字母，并将其转换为小写，在前面添加一个连字符
        const nowTab = pathName[2].replace(regex, '-$1').toLowerCase();
        setNowTab(nowTab)
        setOpenKeys([pathName[1]])
    }, [locations.pathname])
    const getTimeout = () => {
        api.license.getLicense()
            .then(res => {
                let subscription_end = res?.data?.data?.subscription_end
                let trial_end = res?.data?.data?.trial_end
                let subscription_start = res?.data?.data?.subscription_end
                let trial_start = res?.data?.data?.trial_end

                if (!trial_end && !subscription_end && !window.location.href.includes('/timeout')) {
                    navigate("/timeout")
                } else {

                    let timeOut_trial = 0
                    if (trial_end) {
                        timeOut_trial = new Date(trial_end).getTime()
                    }

                    let timeOut_subs = 0
                    if (subscription_end) {
                        timeOut_subs = new Date(subscription_end).getTime()
                    }

                    //取最大的过期时间
                    let nowTime = Date.now()
                    if (nowTime > Math.max(timeOut_subs, timeOut_trial)) {
                        message.error("您的系统使用权限已过期")
                        navigate("/timeout")
                    } else {
                        let max = Math.max(timeOut_subs, timeOut_trial)
                        let timeDis = max - nowTime
                        let showText = ""
                        const millisecondsPerDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
                        const millisecondsPerHour = 60 * 60 * 1000; // 一小时的毫秒数
                        const millisecondsPerMinute = 60 * 1000; // 一分钟的毫秒数
                        let timeNumber, timeOutUnit = 0 //0天 1小时 2分钟
                        const updateHour = (isStart = false) => {
                            let inter = setInterval(() => {
                                isStart = false
                                nowTime = Date.now()
                                timeDis = max - nowTime

                                timeNumber = Math.floor(timeDis / millisecondsPerHour);
                                showText = `${timeNumber} 小时`;
                                if (timeDis < millisecondsPerHour || timeNumber == 1) {
                                    //已经进入分钟,到分钟里面去处理显示内容
                                    clearInterval(inter)
                                    updateMinute(true)
                                    return

                                }
                                timeOutUnit = 1
                                setProviderConfig({
                                    ...provideConfig,
                                    timeOutText: showText,
                                    unit: timeOutUnit,
                                    timeOut: timeNumber
                                })
                            }, isStart ? 0 : (timeNumber == 1 ? (60 * 1000) : (60 * 60 * 1000)))//每小时循环一次
                        }
                        const sumMinute = (inter) => {
                            nowTime = Date.now()
                            timeDis = max - nowTime
                            // 如果不到一小时，显示为分钟数
                            timeNumber = Math.floor(timeDis / millisecondsPerMinute);
                            if (timeNumber <= 0) {
                                clearInterval(inter)
                                let tempInter = setInterval(() => {
                                    nowTime = Date.now()
                                    if (max < nowTime) {
                                        clearInterval(tempInter)
                                        Modal.warning({
                                            title: "到期提醒",
                                            content: "您的系统使用权限已到期",
                                            onOk: () => {
                                                window.location.href = "/timeout"
                                            }
                                        })
                                    }
                                }, 2000)
                                showText = `0分钟`;
                                timeOutUnit = 2
                            } else {
                                if (timeNumber >= 60) {
                                    showText = `1小时`;
                                    timeOutUnit = 1
                                    timeNumber = 1
                                } else {
                                    showText = `${timeNumber} 分钟`;
                                    timeOutUnit = 2
                                }
                            }



                            setProviderConfig({
                                ...provideConfig,
                                timeOutText: showText,
                                unit: timeOutUnit,
                                timeOut: timeNumber
                            })
                        }
                        const updateMinute = (isStart = false) => {
                            let start = isStart
                            let inter = setInterval(() => {
                                start = false
                                sumMinute(inter)
                            }, 60 * 1000)//每分钟循环一次
                            sumMinute(inter)
                        }
                        if (timeDis >= millisecondsPerDay) {
                            // 如果超过一天，显示为天数
                            timeNumber = Math.floor(timeDis / millisecondsPerDay);
                            showText = `${timeNumber} 天`;
                            timeOutUnit = 0
                            setProviderConfig({
                                ...provideConfig,
                                timeOutText: showText,
                                unit: timeOutUnit,
                                timeOut: timeNumber
                            })
                        } else if (timeDis >= millisecondsPerHour) {
                            // 如果不到一天但超过一小时，显示为小时数
                            updateHour(true)

                        } else {
                            updateMinute(true)
                        }

                        if ((timeOutUnit == 0 && timeNumber <= 15) || timeOutUnit > 0) {
                            notification.warning({
                                duration: 100000,
                                placement: 'bottomRight',
                                message: `系统到期通知`,
                                description: "尊敬的客户，您好！您的系统使用期限即将到期，为确保您能继续享受我们提供的优质服务，请及时完成续费操作。如未在到期前完成续费，系统将暂停服务，可能影响您的正常使用。为避免不必要的麻烦，建议您尽快处理。如有任何疑问，欢迎联系我们的客服团队，我们将竭诚为您服务。感谢您的支持与理解！"
                            })
                        }
                    }
                }

            })

    }
    useEffect(() => {
        //查询一次到期时间
        getTimeout()
    }, [])
    const items: MenuItem[] = [
        {
            key: "homePage",
            label:
                <div
                    key={nowTab}
                    style={{ marginLeft: '0px', width: '100%', display: 'flex', alignItems: 'center', gap: '10px' }}
                    className="menu-button-text">
                    <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3767" width="18" height="18">
                        <path d="M994.816 577.024l-434.688 192.512c-41.472 17.92-58.368 17.92-96.256 0L29.184 577.024C6.144 564.736 0 520.192 0 495.616c29.184 16.384 69.632 34.304 68.096 33.792l443.904 201.728 443.904-201.728c1.024 0 40.448-19.968 68.096-33.792 0 25.088-7.68 71.68-29.184 81.408z m0-201.728l-434.688 192.512c-41.472 17.92-58.368 17.92-96.256 0L29.184 375.296c-34.304-17.92-33.28-74.24 0-95.232l434.688-226.304c38.4-18.944 62.464-19.968 96.256 0l434.688 226.304c32.768 17.92 31.744 80.384 0 95.232zM512 92.16L68.096 327.68l443.904 184.32 443.904-184.32L512 92.16z m0 840.704l443.904-201.728c1.024 0 40.448-19.968 68.096-33.792 0 25.088-7.68 71.168-29.184 81.408l-434.688 192.512c-41.472 17.92-58.368 17.92-96.256 0L29.184 778.752C6.144 766.464 0 721.92 0 697.344c29.184 16.384 69.632 34.304 68.096 33.792l443.904 201.728z" fill={nowTab.startsWith('homePage') ? "#fff" : "#000"} p-id="3768"></path>
                    </svg>
                    <span style={{ overflow: "hidden", textOverflow: 'ellipsis' }}>{wordList['数据看板'][provideConfig.lan]}</span></div>
            ,
            children: [
                {
                    key: 'homePage-screen',
                    label: wordList['总览'][provideConfig.lan]
                },
            ]
        },
        {
            key: 'domain',
            label: <div style={{ marginLeft: '0px', width: '100%', display: 'flex', alignItems: 'center', gap: '10px' }}
                className="menu-button-text">
                <svg style={{ flexShrink: 0 }} viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="22927" width="18" height="18"><path d="M272.156444 383.772444a760.035556 760.035556 0 0 1 76.8-235.121777 396.060444 396.060444 0 0 0-187.164444 173.568c34.702222 25.144889 71.736889 45.681778 110.364444 61.553777zM337.92 681.528889a533.390222 533.390222 0 0 0 355.328-2.048c13.368889-81.351111 12.231111-164.010667-3.470222-244.508445a585.272889 585.272889 0 0 1-373.248 18.488889 719.473778 719.473778 0 0 0 21.390222 228.124445z m387.527111-319.431111a540.444444 540.444444 0 0 0 112.412445-79.075556 396.743111 396.743111 0 0 0-227.612445-156.899555 760.035556 760.035556 0 0 1 115.2 235.975111z m20.878222 47.388444c-2.673778 1.479111-5.461333 2.616889-8.135111 3.982222 16.782222 79.644444 20.48 161.450667 10.695111 242.403556a536.689778 536.689778 0 0 0 160.768-120.945778 396.856889 396.856889 0 0 0-44.487111-207.018666A586.808889 586.808889 0 0 1 746.268444 409.6z m-424.391111-8.248889a535.665778 535.665778 0 0 0 356.010667-17.009777 705.251556 705.251556 0 0 0-142.279111-269.767112 398.051556 398.051556 0 0 0-112.64 9.216 706.275556 706.275556 0 0 0-101.148445 277.617778z m359.025778 336.782223a584.704 584.704 0 0 1-324.551111 3.527111 694.044444 694.044444 0 0 0 77.539555 154.112l5.518223 7.850666a397.482667 397.482667 0 0 0 180.280889-7.964444c27.136-50.631111 47.445333-103.537778 61.212444-157.525333z m-401.635556-80.668445a772.152889 772.152889 0 0 1-13.312-220.558222A586.695111 586.695111 0 0 1 139.946667 370.005333a399.928889 399.928889 0 0 0-25.656889 162.304 530.432 530.432 0 0 0 164.977778 125.098667z m520.647112 30.151111c-20.024889 11.264-40.448 21.105778-61.155556 29.809778-10.581333 51.2-26.737778 101.774222-48.355556 150.755556a396.743111 396.743111 0 0 0 204.629334-247.182223c-29.297778 25.258667-61.041778 47.559111-95.118222 66.673778z m-504.718223 33.507556A584.192 584.192 0 0 1 128 618.382222a399.303111 399.303111 0 0 0 236.657778 263.68c-29.639111-51.2-52.792889-105.358222-69.404445-160.995555z"
                    fill={nowTab.startsWith('domain') ? "#fff" : "#000"} p-id="22928"></path></svg>
                <span style={{ overflow: "hidden", textOverflow: 'ellipsis' }}>{wordList['防监察站点'][provideConfig.lan]}</span></div>,
            children: [
                {
                    key: 'domain-list',
                    label: wordList["站点列表"][provideConfig.lan]
                },
                {
                    key: 'domain-license',
                    label: wordList["证书管理"][provideConfig.lan]
                }/*,
                {
                    key: 'domain-global',
                    label: wordList["全局配置"][provideConfig.lan]
                }*/
            ]
        },
        {
            key: 'log',
            label: <div style={{ marginLeft: '0px', width: '100%', display: 'flex', alignItems: 'center', gap: '10px' }}
                className="menu-button-text" key="configure">
                <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13896" width="18" height="18">
                    <path d="M512 1024C229.6832 1024 0 794.29632 0 512S229.6832 0 512 0s512 229.70368 512 512-229.66272 512-512 512z m0-921.6C286.14656 102.4 102.4 286.14656 102.4 512s183.74656 409.6 409.6 409.6 409.6-183.74656 409.6-409.6S737.85344 102.4 512 102.4z" fill={nowTab.startsWith('log') ? "#fff" : "#000"} p-id="13897"></path>
                    <path d="M704.36864 612.84352l-204.8-51.2A51.2 51.2 0 0 1 460.8 512V256a51.2 51.2 0 0 1 102.4 0v216.00256l166.03136 41.55392a51.2 51.2 0 1 1-24.84224 99.30752z" fill={nowTab.startsWith('log') ? "#fff" : "#000"} p-id="13898"></path>
                </svg>

                <span style={{ overflow: "hidden", textOverflow: 'ellipsis' }}>{wordList['防监察日志'][provideConfig.lan]}</span></div>
            ,
            children: [
                {
                    key: 'log-attack',
                    label: wordList['攻击日志'][provideConfig.lan]
                },
                {
                    key: 'log-attacker',
                    label: wordList['监察者日志'][provideConfig.lan]
                }, {
                    key: 'log-ban',
                    label: wordList['监察阻断'][provideConfig.lan]
                },
                /*{
                    key: 'log-frequency',
                    label: wordList["频率限制"][provideConfig.lan]
                },
                {
                    key: 'log-bot',
                    label: wordList["人机验证"][provideConfig.lan]
                },
                {
                    key: 'log-auth',
                    label: wordList["身份验证"][provideConfig.lan]
                },
                {
                    key: 'log-waitingRoom',
                    label: wordList["等候室"][provideConfig.lan]
                }*/
            ]
        },
        {
            key: "configure",
            label:
                <div style={{ marginLeft: '0px', width: '100%', display: 'flex', alignItems: 'center', gap: '10px', overflow: "hidden", textOverflow: 'ellipsis' }}
                    className="menu-button-text" key="configure">
                    <svg style={{ flexShrink: 0 }} viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6763" width="18" height="18">
                        <path d="M566.144 184.867l324.277 324.277-35.354 35.354L530.79 220.222zM774.599 757.557a24.921 24.921 0 0 1-17.678-7.322c-9.763-9.764-9.763-25.592 0-35.355l98.147-98.146c9.765-9.762 25.593-9.762 35.355 0 9.764 9.764 9.763 25.593 0 35.355l-98.147 98.146a24.921 24.921 0 0 1-17.677 7.322zM238.94 751.743a24.921 24.921 0 0 1-17.678-7.322l-92.333-92.333c-9.763-9.763-9.763-25.593 0-35.355 9.764-9.764 25.592-9.764 35.355 0l92.333 92.333c9.763 9.763 9.763 25.593 0 35.355a24.917 24.917 0 0 1-17.677 7.322zM453.209 184.87l35.355 35.354-324.277 324.277-35.355-35.355z" fill={nowTab.startsWith('configure') ? "#fff" : "#000"} p-id="6764"></path>
                        <path d="M508.277 259.585c-48.022 0-87.091-39.069-87.091-87.091s39.069-87.091 87.091-87.091c48.021 0 87.09 39.069 87.09 87.091s-39.068 87.091-87.09 87.091z m0-134.181c-25.966 0-47.091 21.125-47.091 47.091s21.125 47.091 47.091 47.091 47.09-21.125 47.09-47.091-21.124-47.091-47.09-47.091zM120.222 651.312c-48.022 0-87.091-39.068-87.091-87.091 0-48.021 39.069-87.09 87.091-87.09s87.091 39.069 87.091 87.09c0 48.022-39.069 87.091-87.091 87.091z m0-134.182c-25.966 0-47.091 21.125-47.091 47.091s21.125 47.091 47.091 47.091 47.091-21.125 47.091-47.091-21.125-47.091-47.091-47.091zM901.402 651.313c-48.022 0-87.091-39.069-87.091-87.092 0-48.021 39.068-87.09 87.091-87.09s87.091 39.069 87.091 87.09c0 48.022-39.068 87.092-87.091 87.092z m0-134.183c-25.966 0-47.091 21.125-47.091 47.091 0 25.967 21.125 47.092 47.091 47.092s47.091-21.125 47.091-47.092c0-25.966-21.125-47.091-47.091-47.091z" fill={nowTab.startsWith('configure') ? "#fff" : "#000"} p-id="6765"></path>
                        <path d="M773.676 632.146l-49.931-6.127a197.682 197.682 0 0 0-15.675-38.998l30.996-39.664c7.491-9.58 5.508-24.666-4.025-34.2l-32.28-32.283c-9.536-9.535-24.625-11.513-34.204-4.025l-38.535 30.115a197.61 197.61 0 0 0-39.94-17.08l-5.952-48.516c-1.477-12.067-13.547-21.335-27.028-21.335h-45.653c-13.484 0-25.554 9.269-27.034 21.335l-5.813 47.397a197.474 197.474 0 0 0-41.176 16.752l-37.797-29.536c-9.581-7.486-24.667-5.511-34.202 4.024l-32.282 32.28c-9.533 9.535-11.513 24.626-4.026 34.205l29.315 37.508a197.521 197.521 0 0 0-17.036 41.063l-47.757 5.854c-12.069 1.481-21.339 13.549-21.339 27.031v45.656c-0.002 13.479 9.27 25.552 21.337 27.03l48.137 5.904a197.657 197.657 0 0 0 16.813 40.063l-30.337 38.821c-7.487 9.58-5.509 24.666 4.026 34.201l32.281 32.279c9.535 9.54 24.623 11.515 34.203 4.03l39.35-30.754a197.313 197.313 0 0 0 38.909 15.926l6.168 50.303c1.479 12.07 13.548 21.34 27.031 21.34h45.654c13.48 0 25.548-9.27 27.031-21.34l6.216-50.684a197.377 197.377 0 0 0 37.917-15.696l40.65 31.772c9.585 7.487 24.672 5.507 34.202-4.029l32.283-32.278c9.536-9.539 11.514-24.626 4.025-34.207l-31.542-40.36a197.593 197.593 0 0 0 15.959-37.8l51.062-6.262c12.071-1.485 21.342-13.55 21.337-27.035l0.003-45.65c0.001-13.484-9.27-25.55-21.341-27.03zM533.659 788.14c-58.958 0-106.751-47.794-106.751-106.75 0-58.957 47.794-106.752 106.751-106.752 58.956 0 106.753 47.795 106.753 106.752 0 58.956-47.797 106.75-106.753 106.75z" fill={nowTab.startsWith('configure') ? "#fff" : "#000"} opacity=".4" p-id="6766"></path>
                        <path d="M748.361 594.173l-49.932-6.126a197.666 197.666 0 0 0-15.675-38.0fc6c2l30.995-39.664c7.491-9.579 5.509-24.666-4.024-34.2l-32.281-32.283c-9.535-9.534-24.624-11.513-34.203-4.025l-38.535 30.114a197.623 197.623 0 0 0-39.939-17.079l-5.953-48.517c-1.478-12.067-13.547-21.335-27.028-21.335h-45.653c-13.483 0-25.554 9.268-27.035 21.335l-5.813 47.397a197.557 197.557 0 0 0-41.176 16.751l-37.797-29.535c-9.581-7.486-24.667-5.511-34.202 4.025l-32.282 32.28c-9.533 9.535-11.513 24.625-4.026 34.207l29.316 37.506a197.47 197.47 0 0 0-17.037 41.063l-47.757 5.854c-12.069 1.481-21.339 13.55-21.339 27.031v45.657c-0.001 13.48 9.27 25.552 21.338 27.029l48.137 5.904a197.687 197.687 0 0 0 16.812 40.063l-30.336 38.821c-7.487 9.581-5.509 24.667 4.026 34.201l32.28 32.279c9.535 9.541 24.624 11.515 34.204 4.03l39.35-30.754a197.338 197.338 0 0 0 38.909 15.926l6.168 50.303c1.479 12.07 13.547 21.34 27.03 21.34h45.654c13.48 0 25.547-9.27 27.031-21.34l6.216-50.684a197.464 197.464 0 0 0 37.917-15.695l40.65 31.771c9.585 7.487 24.672 5.507 34.201-4.027l32.283-32.279c9.537-9.54 11.514-24.626 4.026-34.208l-31.542-40.36a197.724 197.724 0 0 0 15.96-37.8l51.062-6.262c12.069-1.485 21.341-13.55 21.335-27.035l0.004-45.65c0.002-13.484-9.269-25.551-21.339-27.03zM508.343 750.166c-58.958 0-106.751-47.793-106.751-106.749 0-58.958 47.794-106.753 106.751-106.753 58.957 0 106.752 47.795 106.752 106.753 0.001 58.956-47.795 106.749-106.752 106.749z" fill={nowTab.startsWith('configure') ? "#fff" : "#000"} p-id="6767"></path></svg>
                    <span style={{ overflow: "hidden", textOverflow: 'ellipsis' }}>{wordList['防监察配置'][provideConfig.lan]}</span></div>,
            children: [
                /*{
                    key: "configure-frequency",
                    label: "频次限制"
                }, */{
                    key: "configure-custom",
                    label: wordList["自定义规则"][provideConfig.lan]
                }/*, {
                    key: "configure-modules",
                    label: wordList["防护模块"][provideConfig.lan]
                },{
                    key: "configure-common",
                    label: wordList["通用配置"][provideConfig.lan]
                }*/
            ]

        },
        {
            key: "setting",
            label: <div style={{ marginLeft: '0px', width: '100%', display: 'flex', alignItems: 'center', gap: '10px' }}
                className="menu-button-text" key="account">
                <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="27755" width="18" height="18">
                    <path d="M960 512c0 65.7-13.9 128.4-41.8 188.2s-65.7 110.5-113.5 152.3l-41.8 29.9c-75.7 51.8-159.3 77.7-250.9 77.7s-175.2-25.9-250.9-77.7l-38.8-29.9c-49.8-41.8-88.6-92.6-116.5-152.3C77.9 640.5 64 577.7 64 512s19.9-156.8 59.7-225.5c39.8-68.7 94.1-123 162.8-162.8C355.2 83.9 430.4 64 512 64s156.8 19.9 225.5 59.7c68.7 39.8 123 94.1 162.8 162.8C940.1 355.2 960 430.4 960 512zM512 903.3c83.6 0 159.3-23.9 227-71.7-4-17.9-9-34.8-14.9-50.8-11.9-27.9-28.4-52.3-49.3-73.2-20.9-20.9-45.8-36.8-74.7-47.8s-58.2-16.4-88.1-16.4-40.3 2.5-61.2 7.5c-20.9 5-39.3 12.4-55.3 22.4-15.9 10-30.9 21.9-44.8 35.8-13.9 13.9-25.9 29.4-35.8 46.3-10 16.9-17.9 35.3-23.9 55.3l-3 20.9c67.7 47.8 142.4 71.7 224 71.7zM374.6 488.1c8 17.9 18.9 33.8 32.9 47.8 13.9 13.9 28.9 24.9 44.8 32.9 39.8 15.9 78.6 15.9 116.5 0 17.9-8 33.8-18.9 47.8-32.9 13.9-13.9 24.9-29.4 32.9-46.3s11.9-35.8 11.9-56.7-4-40.3-11.9-58.2c-8-17.9-18.9-33.8-32.9-47.8-13.9-13.9-29.4-24.9-46.3-32.9s-36.3-11.9-58.2-11.9-41.8 4.5-59.7 13.4c-17.9 9-33.4 19.9-46.3 32.9s-23.4 28.4-31.4 46.3-11.9 36.8-11.9 56.7 4 38.8 11.9 56.7z m415.2 298.7c37.8-35.8 66.2-77.7 85.1-125.4 18.9-47.8 28.4-97.6 28.4-149.3S885.9 374.7 851 315c-34.8-59.7-82.1-107-141.9-141.9-59.7-34.8-125.4-52.3-197.1-52.3s-137.4 17.4-197.1 52.3c-59.7 34.8-107 82.1-141.9 141.9-34.8 59.7-52.3 125.4-52.3 197.1s10 101.5 29.9 149.3c19.9 47.8 47.8 89.6 83.6 125.4v-3c10-29.9 23.4-56.7 40.3-80.6s37.3-44.8 61.2-62.7c17.9-13.9 36.8-24.9 56.7-32.9-10-8-18.9-15.9-26.9-23.9-15.9-15.9-27.9-30.9-35.8-44.8-8-13.9-14.9-30.4-20.9-49.3s-9-38.3-9-58.2 5.5-54.8 16.4-80.6c11-25.9 25.9-48.8 44.8-68.7 18.9-19.9 41.3-34.8 67.2-44.8 25.9-10 53.3-14.9 82.1-14.9s56.2 5 82.1 14.9c25.9 10 48.8 24.9 68.7 44.8 19.9 19.9 35.3 42.8 46.3 68.7 11 25.9 15.4 52.8 13.4 80.6 2 39.8-7 75.7-26.9 107.5-10 15.9-21.9 30.9-35.8 44.8s-16.9 14.9-26.9 20.9c21.9 10 41.8 21.9 59.7 35.8 23.9 17.9 44.3 38.8 61.2 62.7s29.4 49.8 37.3 77.7v6z"
                        p-id="27756" fill={nowTab.startsWith('setting') ? "#fff" : "#000"}></path></svg>
                <span style={{ overflow: "hidden", textOverflow: 'ellipsis' }}>{wordList['系统设置'][provideConfig.lan]}</span></div>,
            children: [{
                key: "setting-account",
                label: wordList['账号管理'][provideConfig.lan]
            }]
        },
    ];
    const location = useLocation();
    const pathSnippets = location.pathname.split('/').filter(i => i);

    const extraBreadcrumbItems: any = pathSnippets.map((_, index) => {

        const url = `/${pathSnippets.slice(0, index + 1).join('/')}`;
        let name = ""
        for (let key in breadcrumbNameMap) {
            if (key.includes("/*") && key.startsWith(url)) {
                name = breadcrumbNameMap[key]
                break
            } else if (url == key) {
                name = breadcrumbNameMap[key]
                break
            }
        }
        if (!name) {
            return ""
        }
        return (
            <Breadcrumb.Item key={url}>
                <Link to={url}>{wordList[name][provideConfig.lan]}</Link>
            </Breadcrumb.Item>
        );
    });

    const breadcrumbItems = [
        <Breadcrumb.Item key="home">
            <Link to="/">{wordList['主页'][provideConfig.lan]}</Link>
        </Breadcrumb.Item>,
    ].concat(extraBreadcrumbItems);
    const [showActive,setShowActive]=useState(false)
    const [folded, setFolded] = useState(false)
    return <ProviderContext.Provider
        value={provideConfig}
    >
        {
            showActive
            ?<Active close={()=>{
                setShowActive(false)
            }}></Active>
            :null
        }
        <div style={{
        width: '100vw',
        background: '#f7f8fa',//'#f7f8fa',
        overflow: 'hidden',
        height: '100vh'
    }}>
            <div
                className={folded ? "close-menu" : ""}
                style={{
                    width: '100vw',
                    height: '100vh',
                    display: 'flex',
                }}>
                <div
                    className="menu-container"
                >
                    <div className="bugsguard">
                        <img src={logo} style={{ width: "30px", height: "30px", borderRadius: '15px' }} />
                        <span style={{ fontFamily: 'AlimamaShuHeiTi' }}>漏洞卫士平台</span>
                    </div>
                    <div className="bugsguard-icon">
                        <img src={logo} style={{ width: 'auto', height: '100%' }} />
                    </div>
                    <div style={{
                        height: "calc(100vh - 150px)",
                        overflowY: 'auto'
                    }}>
                        <Menu
                            multiple={true}
                            onChange={(e) => {

                            }}
                            onSelect={(e) => {
                                if (e.key == nowTab) return
                                setNowTab(e.key)
                            }}
                            onOpenChange={(e) => {
                                let key: any
                                for (let i = 0; i < items.length; i++) {
                                    key = items[i]?.key
                                    if (!e.includes(key)) {
                                        //哪个不存在就是点击哪个
                                        setNowTab(indexPath[key])
                                        navigate(pathRefer[indexPath[key]])
                                        break
                                    }
                                }
                                return
                                /*if (e.length > 0) {
                                    let nowOpen = e.at(-1)
                                    if (nowOpen) {
                                        let tempRefer = parentRefer[nowOpen]
                                        navigate(tempRefer)
                                        for (let i = 0; i < items.length; i++) {
                                            if (items[i]?.key == nowOpen) {
                                                console.log(nowTab,nowOpen)
                                                if (nowTab==nowOpen) {
                                                    return
                                                }
                                                console.log("setTabs",)
                                                setNowTab((items[i] as any)?.children[0].key)
                                                break
                                            }
                                        }
                                        //setOpenKeys([nowOpen])
                                    }
                                } */
                            }}
                            onClick={(e) => {
                                if (e.key == nowTab) return
                                setNowTab(e.key)
                                if (pathRefer[e.key]) {
                                    navigate(pathRefer[e.key])
                                }
                                if (openKeys.length > 0) {
                                    if (openKeys[0].includes(e.key.split("-")[0])) {
                                        return
                                    }
                                }
                                //setOpenKeys([])
                                /*switch(e.key){
                                    case "center":
                                        if(window.location.href.includes("/device/controller/index/")) return
                                        window.location.href="/device/controller/index/"+(window as any).nowViewDeviceId
                                        break
                                    case "report":
                                        window.location.href="/device/controller/reportList/"+(window as any).nowViewDeviceId
                                        break
                                }*/
                            }}
                            style={{ fontSize: '18px', gap: '50px' }}
                            selectedKeys={[nowTab]}
                            openKeys={items.map((item: any) => item.key)}
                            items={items}
                            mode="inline">

                        </Menu>
                    </div>
                    <div style={{
                        height: "100px",
                        display: 'flex',
                        flexDirection: "column",
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '10px'
                    }}>
                        <div style={{
                            width: "60%",
                            background: 'white',
                            borderRadius: '10px',
                            height: '25px',
                            lineHeight: '25px',
                            textAlign: 'center',
                            fontSize: '14px',
                        }}>
                            版本号 V1.0.6
                        </div>
                        {/*<div style={{
                            width: "60%",
                            display: 'flex',
                            gap: '20px',
                            height: '30px',
                            lineHeight: '30px',
                            fontWeight: "400",
                            cursor: "pointer"
                        }}>
                            <FileTextOutlined />
                            <span>帮助文档</span>
                        </div>*/}
                    </div>
                </div>

                <Content className="page-content">
                    <div
                        className="header-container"
                    >
                        {/**左上角 
                         * <div
                                onClick={() => {
                                    setFolded(!folded)
                                }}
                                className="close-menu-button"
                                style={{
                                    width: '30px',
                                    cursor: "pointer",
                                    height: '30px'
                                }}>
                                {
                                    folded
                                        ? <MenuUnfoldOutlined style={{ fontSize: "24px" }}></MenuUnfoldOutlined>
                                        : <MenuFoldOutlined style={{ fontSize: "24px" }} />
                                }
                            </div>
                        */}
                        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>

                            <Breadcrumb style={{ height: '40px', marginTop: "15px", alignItems: 'center', display: 'flex' }}>{breadcrumbItems}</Breadcrumb>
                        </div>

                        {/**右上角 */}
                        <div style={{
                            width: 'auto',
                            display: 'flex',
                            height: '50px',
                            alignItems: 'center',
                            gap: '20px'
                        }}>
                            <Tooltip title="点击续期" placement="bottom">
                                <div 
                                onClick={()=>{
                                    setShowActive(true)
                                }}
                                style={{
                                    background: "white",
                                    borderRadius: "5px",
                                    paddingLeft: '10px',
                                    paddingRight: "10px",
                                    height: '30px',
                                    lineHeight: '30px',
                                    color: "#999",
                                    gap: '10px',
                                    fontSize: '12px',
                                    cursor: "pointer"
                                }}>
                                    <span
                                        style={{
                                            fontSize: '16px',
                                            fontWeight: "bolder",
                                            fontFamily: "AlimamaShuHeiTi",
                                            color: (provideConfig.unit > 0 || (provideConfig.unit == 0 && provideConfig.timeOut <= 30)) ? '#cc1212' : "black",
                                            marginRight: '10px'
                                        }}>
                                        {provideConfig.timeOutText}
                                    </span>
                                    <span>后到期</span>
                                </div>
                            </Tooltip>

                            <Radio.Group
                                style={{ height: '30px', lineHeight: '30px' }}
                                options={[{
                                    label: "中文",
                                    value: "cn"
                                }, {
                                    label: "English",
                                    value: 'en'
                                }]}
                                className="lan-trans"

                                onChange={(e) => {
                                    setProviderConfig({
                                        ...provideConfig,
                                        lan: e.target.value
                                    })
                                }}
                                defaultValue={"cn"}
                                optionType="button"
                                buttonStyle="solid"
                            />
                            <Tooltip title="退出登录">
                                <Button type="primary"
                                    style={{
                                        border: "none",
                                        background: "white",
                                        boxShadow: "none",
                                        borderRadius: '10px'
                                    }}
                                    onClick={() => {
                                        Modal.confirm({
                                            title: "操作确认",
                                            content: "确认退出登录",
                                            onOk: () => {
                                                return new Promise<boolean>((resolve, reject) => {
                                                    localStorage.removeItem("AUTH")
                                                    message.success("用户已退出")
                                                    setTimeout(() => {
                                                        window.location.href = "/login"
                                                        resolve(true)
                                                    }, 1000)
                                                })
                                            }
                                        })
                                    }}>
                                    <LogoutOutlined style={{ color: "#cc1212" }} />
                                </Button>
                            </Tooltip>

                        </div>
                    </div>
                    <div style={{
                        width: '100%',
                        zIndex: 1,
                        position: 'relative',
                        height: "calc(100% - 80px)",
                        overflowY: 'auto'
                    }}>

                        <div style={{
                            width: 'calc(100% - 20px)',
                            height: 'auto',
                            marginLeft: '10px',
                            overflow: "hidden"
                        }}>
                            <Suspense fallback={<div>loading...</div>}>
                                <Routes>
                                    <Route path="/" element={<HomePage></HomePage>}></Route>
                                    <Route path="/homePage" element={<HomePage></HomePage>}></Route>
                                    <Route path="/setting" element={<Account></Account>}></Route>
                                    <Route path="/setting/settingAccount" element={<Account></Account>}></Route>

                                    <Route path="/domain" element={<Domain></Domain>}></Route>
                                    <Route path="/domain/domainList" element={<Domain></Domain>}></Route>
                                    <Route path="/domain/domainLicense" element={<DomainLicense></DomainLicense>}></Route>
                                    <Route path="/domain/domainGlobal" element={<DomainGlobal></DomainGlobal>}></Route>
                                    <Route path="/domain/siteDetail/:id" element={<SiteDetail></SiteDetail>}></Route>

                                    <Route path="/log" element={<LogAttack></LogAttack>}></Route>
                                    <Route path="/log/logAttack" element={<LogAttack></LogAttack>}></Route>
                                    <Route path="/log/logAttacker" element={<LogAttacker></LogAttacker>}></Route>
                                    <Route path="/log/logBan" element={<LogBan></LogBan>}></Route>
                                    <Route path="/log/logFrequency" element={<LogFrequency></LogFrequency>}></Route>
                                    <Route path="/log/logBot" element={<LogBot></LogBot>}></Route>
                                    <Route path="/log/logAuth" element={<LogAuth></LogAuth>}></Route>
                                    <Route path="/log/logWaitingRoom" element={<LogWaitingRoom></LogWaitingRoom>}></Route>


                                    <Route path="/configure" element={<ConfigureFrequency></ConfigureFrequency>}></Route>
                                    <Route path="/configure/configureFrequency" element={<ConfigureFrequency></ConfigureFrequency>}></Route>
                                    <Route path="/configure/configureModules" element={<ConfigureModules></ConfigureModules>}></Route>
                                    <Route path="/configure/configureCommon" element={<ConfigureCommon></ConfigureCommon>}></Route>
                                    <Route path="/configure/configureCustom" element={<ConfigureCustom></ConfigureCustom>}></Route>
                                </Routes>
                            </Suspense>
                        </div>
                    </div>
                </Content>
            </div>

        </div>
    </ProviderContext.Provider>
}
export default PageEntrance