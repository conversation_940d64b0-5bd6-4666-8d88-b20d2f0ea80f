.home-page-domain-list{
    width: 50%;
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 10px;
    background-color: white;
    .progress-list-container{
        overflow: hidden;
    }
    .progress-list-item{
        height: 20%;
    }
}

.page-card-title{
    width: auto;
    height: 30px;
    line-height: 30px;
    position: relative;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 10px;
}
.page-card-title::before{
    content: '';
    position: absolute;
    left: -10px;
    z-index: 2;
    height: 20px;
    background-color: #cc1212;
    top: 5px;
    width: 5px;
    border-radius: 10px;
}
.progress-list-container{
    height: calc(100% - 40px);
    padding-right: 10px;
    margin-top: 15px;
    box-sizing: border-box;
    overflow-y: auto;
    width: 100%;
}

.progress-list-item{
    width: 100%;
    height: 30px;
    display: flex;
    flex-direction: column;
    .ant-progress{
        margin-top: -8px;
    }
}
.progress-list-item-text-conainer{
    width: 100%;
    height: 20px;
    display: flex;
    justify-content:space-between;
}
.progress-list-item-text-title{
    width: auto;
    font-size: 12px;
    color: #999;
    text-align: left;
}
.progress-list-item-text-data{
    width: auto;
    font-size: 12px;
    font-weight: bold;
    text-align: right;
}

