import ajax from "../tools/ajax";

//账号密码登录
const getAccountList = (data) => ajax("/api/v1/user", data, "GET");//不带token
const login=(data)=>ajax('/api/v1/user/login',data,'POST')//用户登录
const register=(data)=>ajax(`/api/v1/user/register`,data,'POST')//注册
const editUser=(id,data)=>ajax(`/api/v1/user/${id}`,data,'PATCH')//修改用户
const resetPassword=(data)=>ajax(`/api/v1/user/reset-password`,data,'PATCH')//重置密码
const deleteUser=(id)=>ajax(`/api/v1/user/${id}`,{},"DELETE")//删除用户
export default {
    getAccountList,
    login,
    register,
    editUser,
    resetPassword,
    deleteUser
}