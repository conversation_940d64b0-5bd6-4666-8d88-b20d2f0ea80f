.site-detail-modules{
    width: calc(100% - 30px);
    max-height: 500px;
    min-height: 200px;
    overflow-y:auto ;
    padding: 20px;
    margin-left: 15px;
    box-sizing: border-box;
    border-radius: 10px;
    display: flex;
    border:1px solid #ddd;
    flex-wrap: wrap;
    gap: 50px;
    row-gap: 30px;
    box-sizing: border-box;
}
.site-baseinfo-container{
    .ant-tag-checkable:hover{
        background-color: #cc1212;
        color: white;
        path{
            fill: white;
        }
    }
    .ant-tag-checkable{
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        width: 100px;
        line-height: 25px;
        text-align: center;
        height: 25px;
        border-radius: 5px;
        background-color: #f7f8fa;
        transition: .2s;
        border:1px solid transparent;
        box-sizing: border-box;
        font-size: 14px;
    }
    .ant-tag-checkable-checked{
        color: #cc1212;
        border: 1px solid #ddd;
        background-color: #c8d9ff60;
    }
    
}
.upstream-node{
    position: relative;
    width: 150px;
    cursor: pointer;
    height: 200px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.06);
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    justify-content: center;
    gap: 10px;
}
.upstream-node:hover{
    .edit-upstream-node{
        right: 0;
        top: 0;
    }
}
.edit-upstream-node{
    position: absolute;
    right: inherit;
    transition: .25s;
    right: -30px;
    top: -30px;
    background-color: #cc1212;
    display: flex;
    color: white;
    font-size: 18px;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-bottom-left-radius: 30px;
}
.site-detail-today{
    width: 100%;
    height: 40%;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.06);

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.site-detail-modal{
    .ant-form-item-label{
        width: 80px;
        flex-shrink: 0 !important;
    }
}
.defend-item-container{
    position: relative;
    width: 300px;
    cursor: pointer;
    height: 170px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.06);
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    overflow: hidden;
    justify-content: center;
    gap: 10px;
    .ant-radio-group {
        width: 100%;
        border: none;
        height:100%;
        justify-content: center;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        
    }
    .ant-radio-wrapper{
        font-size: 16px;
        margin-bottom: 10px;
    }
    .ant-radio-inner{
        width: 16px;
        height: 16px;
    }
}