@font-face {
    font-family: "Mono";
    src: url('./assets/Mono.ttf') format('ttf');
    font-weight: normal;
    font-style: normal;
  }

  @font-face {
    font-family: "AlimamaShuHeiTi";
    src: url("./assets//AlimamaShuHeiTi-Bold.woff2") format('woff2');
  }
.App {
    .page-main {
        display: flex;

        .page-main-container {
            flex: 1;
            box-shadow: 0 0.9px 4px -1px rgb(0 0 0 / 8%), 0 2.6px 8px -1px rgb(0 0 0 / 6%), 0 5.7px 12px -1px rgb(0 0 0 / 5%), 0 15px 15px -1px rgb(0 0 0 / 4%);
            padding: 30px 24px;
            min-height: calc(100vh - 96px);
            transition: margin 150ms cubic-bezier(0.694, 0.0482, 0.335, 1);
        }
    }
}

.loading-container {
    text-align: center;
    margin-top: 50px;
}

//富文本相关
.quill {
    .ql-toolbar.sticky {
        background-color: #fff;
        z-index: 99;
        position: fixed;
        top: 64px;
    }

    .ql-toolbar.ql-snow {
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }

    .ql-container.ql-snow {
        height: 300px;
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
    }
}

.quill-submit {
    text-align: right;
}

@media screen and (max-width: 992px) {
    .App {
        .page-main {
            display: block;

            .page-main-container {
                margin-left: 0 !important;
                width: 100% !important;
            }
        }
    }
}

.ant-modal-content {
    border-radius: 15px !important;
    overflow: hidden !important;
}

.ant-input {
    border-radius: 10px !important;
    border: 1px solid #dddddd;
    overflow: hidden;
}

.ant-radio-group {
    border-radius: 5px !important;
    overflow: hidden;
    border: 1px solid #d9d9d9;
}

.ant-radio-button-wrapper {
    border: none;
}

.ant-radio-button-checked {
    background-color: #cc1212;
}


.ant-input-number-group {
    border-radius: 10px !important;
    border: 1px solid #dddddd;
    overflow: hidden;

    .ant-input-number {
        background-color: #fafafa !important;
        border: none;
    }

    .ant-input-number-group-addon {
        border: none;
    }
}

.ant-input {
    border-radius: 5px !important;
}

.ant-select-selector {
    border-radius: 5px !important;
}

.ant-table-cell {
    background-color: transparent !important;
    color: #999
}

.ant-table-thead {
    .ant-table-cell {
        color: #999 !important
    }
}
.ant-input-number{
    border-radius: 5px !important;
}
.ant-table {
    border: 1px solid #ddd;
    box-sizing: border-box;
    padding: 5px;
    border-radius: 10px !important;
    overflow: hidden;
}

.ant-radio-checked .ant-radio-inner::after {
    transform: scale(0.8);
    background-color: #cc1212;
}
.ant-picker-ok{
    .ant-btn-primary{
        background-color: #cc1212 !important;
        color: white !important;
    }
}

.ant-picker-cell-selected{
    .ant-picker-cell-inner{
        background-color: #cc1212 !important;
    
    }
}
.ant-picker-cell-today{
    .ant-picker-cell-inner::before{
        border-color: #cc1212 !important;
    
    }
}
.ant-picker-now-btn{
    color: #cc1212 !important;
}

/**开发模式下处理渲染报错*/
#webpack-dev-server-client-overlay {
    display: none !important;
}

.ant-switch-checked {
    background-color: #cc1212 !important;
}

.ant-radio-group-solid {
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    overflow: hidden;

    label {
        border: none !important;
    }
}
.ant-modal-confirm-btns{
    .ant-btn{
        border-radius: 10px;

    }
    .ant-btn-primary{
        background-color: #cc1212 !important;
    }
}
.ant-popover-inner-content{
    .ant-btn{
        border-radius: 5px;
    }
    .ant-btn-primary{
        background-color: #cc1212 !important;
    }
}
/*多选框中间蓝线*/
.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before{
    background-color: transparent !important;
}