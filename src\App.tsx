import { lazy, Suspense, useEffect, useState } from 'react';
import {
    Routes,
    Route,
    
    useLocation,
    useNavigate,
    Link,
} from "react-router-dom";
import { Spin,Breadcrumb, message  } from "antd";

import './App.less';
import { breadcrumbNameMap } from './config/routerRefer';

import Login from './pages/Login';
import api from './api';


const Homepage = lazy(() => import('./pages'));

const Timeout = lazy(() => import("./pages/Timeout"))
function App() {
    const [loading,setLoading]=useState(true)
    useEffect(()=>{
        if(window.location.href.includes("/timeout")){
            setLoading(false)
            return
        }
        api.license.getLicense()
            .then(res => {
                let subscription_end = res?.data?.data?.subscription_end
                let trial_end = res?.data?.data?.trial_end
                let subscription_start = res?.data?.data?.subscription_end
                let trial_start = res?.data?.data?.trial_end

                if (!trial_end && !subscription_end && !window.location.href.includes('/timeout')) {
                    window.location.href="/timeout"
                } else {

                    let timeOut_trial = 0
                    if (trial_end) {
                        timeOut_trial = new Date(trial_end).getTime()
                    }

                    let timeOut_subs = 0
                    if (subscription_end) {
                        timeOut_subs =new Date(subscription_end).getTime()
                    }

                    //取最大的过期时间
                    let nowTime = Date.now()
                    if (nowTime > Math.max(timeOut_subs, timeOut_trial)) {
                        window.location.href="/timeout"
                    } else{
                        setLoading(false)
                    }
            }})
            .catch(err=>{
                console.log(err)
                message.error("无法获取设备License状态")
                setTimeout(()=>{
                    window.location.href="/timeout"
                },2000)
            })
    },[])
    return (
        <div className="App">
            {
                loading
                ?<Spin spinning></Spin>
                :<Suspense fallback={(<div className="loading-container"><Spin size="large" /></div>)}>
                    <Routes>
                        <Route path="/timeout" element={<Timeout></Timeout>}></Route>
                        <Route path="/login" element={<Login></Login>}></Route>
                        <Route path="/*" element={<Homepage />} />
                    </Routes>
                </Suspense>
            }
            
            
        </div>
    );
}

export default App;
