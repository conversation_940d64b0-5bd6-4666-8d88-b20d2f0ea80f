import React from 'react';
import ReactDOM from 'react-dom/client';
import zhCN from 'antd/es/locale/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { BrowserRouter } from "react-router-dom";
import { ConfigProvider } from 'antd';

import './index.css';
import App from './App';

moment.locale('zh-cn');

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <ConfigProvider locale={zhCN}>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </ConfigProvider>
);
