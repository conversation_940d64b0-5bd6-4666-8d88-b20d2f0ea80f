
import ajax from "../tools/ajax";
const getSiteList=(data)=>ajax(`/api/v1/site`,data,'GET')//获取站点分页
const getSiteById=(id)=>ajax(`/api/v1/site/${id}`,{},'GET',{},true)//id获取站点
const deleteSite=(id)=>ajax(`/api/v1/site/${id}`,{},'DELETE')//删除站点
const editSite=(id,data)=>ajax(`/api/v1/site/${id}`,data,'PATCH')//编辑站点
const createSite=(data)=>ajax(`/api/v1/site`,data,'POST')//创建站点
export default {
    getSiteList,
    getSiteById,
    deleteSite,
    editSite,
    createSite
}