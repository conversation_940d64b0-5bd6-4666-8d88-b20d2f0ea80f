import { Button, Checkbox, Input, InputNumber, message, Modal, Radio, Space, Switch, Table, Upload } from "antd"
import { useContext, useState } from "react"
import "./index.less"
import { InboxOutlined } from "@ant-design/icons"
import type { UploadProps } from 'antd';
import ProviderContext from "../../../../providerContext";
import wordList from "../../../../../config/wordList";
import CustomTable from "../../../../../components/CustomTable";
import BugsLinkButton from "../../../../../components/BugsLinkButton";
const BOTProtection = (props: any) => {
    const Context=useContext(ProviderContext)
    const [open, setOpen] = useState(true)
    const [data, setData] = useState({
        'bot': {
            use: false,
            type: 'slider',
            time: 1
        },
        dynamics: {
            use: false
        },
        html: {
            use: false,
            useQuick: false
        },
        dynamicsJs: {
            use: false,
            path: []
        },
        waterMark: {
            use: false,
            type: 'domain'
        },
        replay: {
            use: false
        }
    })
    //临时选择
    const [tempSelectedPath, setTempSelectedPath] = useState([])
    //已选择
    const [selectedPath, setSelectedPath] = useState([])
    const uploadProps: UploadProps = {
        name: 'file',
        multiple: true,
        action: 'https://www.mocky.io/v2/5cc8019d300000980a055e76',
        onChange(info) {
            const { status } = info.file;
            if (status !== 'uploading') {
            }
            if (status === 'done') {
                message.success(`${info.file.name} file uploaded successfully.`);
            } else if (status === 'error') {
                message.error(`${info.file.name} file upload failed.`);
            }
        },
        onDrop(e) {
        },
    };
    /**人机防护部分的内容 */
    const botNode = (<div className="cc-protection-custom">
        <div className="ccprotection-custom-item">
            <Radio.Group
                style={{
                    flexShrink: 0
                }}
                options={[
                    { label: wordList["无交互验证"][Context.lan], value: 'free' },
                    { label: wordList["滑动验证"][Context.lan], value: 'slider' },
                ]}
                onChange={(e) => {
                    setData({
                        ...data,
                        bot: {
                            ...data.bot,
                            type: e.target.value
                        }
                    })
                }}
                value={data.bot.type}
                optionType="button"
                buttonStyle="solid"
            />
            <div style={{ marginLeft: '10px', whiteSpace: 'nowrap' }}>
            {wordList["验证有效期"][Context.lan]}</div>
            <InputNumber style={{
                width: '100%'
            }} addonAfter="小时"></InputNumber>
        </div>
    </div>)
    /**动态防护部分的内容 */
    const dynamicsNode = (
        <div className="cc-protection-custom">
            <Space>
                <Checkbox
                    checked={
                        data.html.use
                    }
                    onChange={(e) => {
                        setData({
                            ...data,
                            html: {
                                ...data.html,
                                use: e.target.checked
                            }
                        })
                    }}>
                    
                    {wordList["HTML动态加密"][Context.lan]}
                </Checkbox>
                {
                    data.html.use
                        ? <Checkbox
                            checked={data.html.useQuick}
                            onChange={(e) => {
                                setData({
                                    ...data,
                                    html: {
                                        ...data.html,
                                        useQuick: e.target.checked
                                    }
                                })
                            }}>
                           {wordList["快速加解密（跳过解密页面）"][Context.lan]}
                        </Checkbox>
                        : null
                }

            </Space>

            <Space>
                <Checkbox
                    onChange={(e) => {
                        setData({
                            ...data,
                            dynamicsJs: {
                                ...data.dynamicsJs,
                                use: e.target.checked
                            }
                        })
                    }}>
                    {wordList["JS 动态混淆"][Context.lan]}
                </Checkbox>
                <span style={{ color: "#999999", fontSize: '12px' }}>{wordList["性能消耗较大，请充分测试再上线"][Context.lan]}</span>
            </Space>
            {
                data.dynamicsJs.use
                    ? <div style={{
                        position: 'relative',
                        width: 'calc(100% - 40px)',
                        marginLeft: '20px',
                        height: 'auto',
                        minHeight: '300px',
                        overflowY: 'auto'
                    }}>
                        <BugsLinkButton
                            onClick={() => {
                                setOpenPathBord(true)
                            }}
                            style={{
                                position: 'absolute',
                                right: '0px',
                                top: '0px',
                                zIndex: 2,
                                color: "#cc1212"
                            }}>
                                {wordList["选择防护资源"][Context.lan]}</BugsLinkButton>
                        <CustomTable 
                            pagination={
                                {
                                    hideOnSinglePage: true,
                                    pageSize: 999
                                }
                            }
                            scroll={{
                                x: 'mac-content',
                                y: 350
                            }}
                            dataSource={
                                selectedPath
                            }
                            columns={[
                                {
                                    title: '路径',
                                    dataIndex: "path",
                                    key: 'path',
                                    width: 300
                                }
                            ]}
                        ></CustomTable>
                    </div>
                    : null
            }

            <Space>
                <Checkbox
                    onChange={(e) => {
                        setData({
                            ...data,
                            waterMark: {
                                ...data.waterMark,
                                use: e.target.checked
                            }
                        })
                    }}>
                    {wordList["图片动态水印"][Context.lan]}
                </Checkbox>
                <span style={{ color: "#999999", fontSize: '12px' }}>{wordList["性能消耗较大，请充分测试再上线"][Context.lan]}</span>
            </Space>
            {
                data.waterMark.use
                    ? <>
                        <Radio.Group
                            style={{
                                outline: "none",
                                border: 'none',
                                justifyContent: 'space-between',
                                display: 'flex'
                            }}
                            options={[
                                { label: wordList["网站域名"][Context.lan], value: 'host' },
                                { label: wordList["自定义文本"][Context.lan], value: 'text' },
                                { label: wordList["自定义图片"][Context.lan], value: 'image' },
                            ]} onChange={(e) => {
                               
                                setData({
                                    ...data,
                                    waterMark: {
                                        ...data.waterMark,
                                        type: e.target.value
                                    }
                                })
                            }} value={data.waterMark.type} />
                        {
                            data.waterMark.type == 'text'
                                ? <div style={{
                                    width: '100%',
                                    display: 'flex',
                                    gap: '10px',
                                    alignItems: 'center'
                                }}>
                                    <div style={{
                                        width: '100px',
                                        flexShrink: 0,
                                        color: '#999999'
                                    }}>{wordList["水印文本内容"][Context.lan]}</div>
                                    <Input style={{
                                        borderRadius: '10px',
                                        width: '100%'
                                    }}></Input>
                                </div>
                                : null
                        }
                        {
                            data.waterMark.type == 'image'
                                ? <Upload.Dragger {...uploadProps}>
                                    <p className="ant-upload-drag-icon">
                                        <InboxOutlined />
                                    </p>
                                    <p className="ant-upload-text">{wordList["点击或拖入此处上传"][Context.lan]}</p>
                                </Upload.Dragger >
                                : null
                        }
                    </>
                    : null
            }

        </div>
    )
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);


    const onSelectChange = (newSelectedRowKeys: React.Key[], record) => {
        setTempSelectedPath(record)
        setSelectedRowKeys(newSelectedRowKeys);
    };
    /**所有的JS资源路径 */
    const [JSPathData, setJSPathData] = useState<any>([{
        path: '/test.js',
        key: "path1"
    }, {
        key: "path2",
        path: '/test2.js'
    }])
    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
    };
    const [openPathBord, setOpenPathBord] = useState(false)
     /*<Modal
        open={open}
        onCancel={(e) => {
            setOpen(false)
        }}
        width={1000}
        afterClose={() => [
            props.close && props.close()
        ]}
        title={wordList["BOT防护"][Context.lan]}
    >*/
    return <>
        {/**JS资源选择框 */}
    
        <Modal
            open={
                openPathBord
            }
            onCancel={(e) => {
                setOpenPathBord(false)
            }}
            title={wordList["JS防护资源"][Context.lan]}
            onOk={() => {
                //更新已经选择的JS资源
                setOpenPathBord(false)
                setSelectedPath([...tempSelectedPath.slice()])
            }}
            width={800}
        >
            <CustomTable  rowSelection={rowSelection}
                pagination={
                    {
                        hideOnSinglePage: true,
                        pageSize: 999
                    }
                }
                columns={[
                    {
                        title: '路径',
                        dataIndex: 'path',
                    },
                ]}
                dataSource={JSPathData} />
        </Modal>
        <div className="botprotection-container">
            <div className="botprotection-container-item">
                <div className="botprotection-container-item-title">{wordList["人机验证"][Context.lan]}</div>
                <Switch onChange={(e) => {
                    let replay=data.replay
                    if(!e){
                        replay.use=false
                    }
                    setData({
                        ...data,
                        bot: {
                            ...data.bot,
                            use: e
                        },
                        replay
                    })
                }}></Switch>
                <div style={{ color: "#999999" }}>{wordList["开启人机验证后，被自动化程序控制的浏览器将会被阻止"][Context.lan]}</div>
            </div>
            {
                data.bot.use
                    ? botNode
                    : null

            }
            <div className="botprotection-container-item" style={{ marginTop: '20px' }}>
                <div className="botprotection-container-item-title">{wordList["动态防护"][Context.lan]}</div>
                <Switch onChange={(e) => {
                    setData({
                        ...data,
                        dynamics: {
                            ...data.dynamics,
                            use: e
                        }
                    })
                }}></Switch>
                <div style={{ color: "#999999" }}>{wordList["开启动态防护后，网页内容将会被动态加密保护"][Context.lan]}</div>
            </div>
            {
                data.dynamics.use
                    ? dynamicsNode
                    : null
            }
            <div className="botprotection-container-item" style={{ marginTop: '20px' }}>
                <div className="botprotection-container-item-title">{wordList["请求防重放"][Context.lan]}</div>
                <Switch
                    checked={
                        data.bot.use && data.replay.use
                    }
                    onChange={(e) => {
                        if(!data.bot.use) {
                            message.error("需开启人机验证")
                            return
                        }
                        setData({
                            ...data,
                            replay: {
                                ...data.replay,
                                use: e
                            }
                        })
                    }}></Switch>
                <div style={{ color: "#999999" }}>{wordList["开启请求防重放后，通过抓包获得的请求将无法重复提交"][Context.lan]}</div>
            </div>
        </div>
        </>
    /*</Modal>*/
}
export default BOTProtection