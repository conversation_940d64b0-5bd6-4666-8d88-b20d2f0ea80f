import { FC, useState } from 'react';
import reactCSS from 'reactcss';
import { SketchPicker } from 'react-color';

interface ColorPickerProps {
  onChange?: (value: any) => void;
    color?:string
}

export const ColorPicker: FC<ColorPickerProps> = ({ onChange,color }) => {
  const [selectColor, setSelectColor] = useState({
    displayColorPicker: false,
    color: color || '#fff',
  });

  const handleClick = () => {
    setSelectColor({...selectColor, displayColorPicker: !selectColor.displayColorPicker });
  };

  const handleClose = () => {
    setSelectColor({...selectColor, displayColorPicker: false });
  };

  const handleChange = ({ hex }: any) => {
    setSelectColor({...selectColor, color: hex });
    onChange && onChange(hex);
  };

  const styles = reactCSS({
    default: {
      color: {
        width: '36px',
        height: '14px',
        borderRadius: '2px',
        background: selectColor.color,
      },
      swatch: {
        padding: '5px',
        background: '#fff',
        borderRadius: '1px',
        boxShadow: '0 0 0 1px rgba(0,0,0,.1)',
        display: 'inline-block',
        cursor: 'pointer',
      },
      popover: {
        position: 'absolute',
        zIndex: '2',
      },
      cover: {
        position: 'fixed',
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      },
    },
  });

  return (
    <div>
      <div style={styles.swatch} onClick={handleClick}>
        <div style={styles.color} />
      </div>
      {selectColor.displayColorPicker ? (
        <div style={styles.popover}>
          <div style={styles.cover} onClick={handleClose} />
          <SketchPicker color={selectColor.color} onChange={handleChange} />
        </div>
      ) : null}
    </div>
  );
};

export default ColorPicker;