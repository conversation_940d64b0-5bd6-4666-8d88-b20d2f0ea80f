import { Tag, <PERSON>ton, Card, DatePicker, Divider, Input, InputNumber, Popover, Radio, Space, Table, TimePicker, Spin } from "antd"
import "./index.less"
import { useContext, useEffect, useState } from "react"
import moment from "moment";
import LogDetail from "./components/LogDetail";
import SearchBar from "./components/SearchBar";
import config from "../../../config";
import wordList from "../../../config/wordList";
import ProviderContext from "../../providerContext";
import api from "../../../api";
import CustomTable from "../../../components/CustomTable";
import type { ColumnsType } from 'antd/es/table';
import CustomCard from "../../../components/CustomCard";
import BugsLinkButton from "../../../components/BugsLinkButton";
import { SyncOutlined } from "@ant-design/icons";
import BugsButton from "../../../components/BugsButton";
import ExportModal from "./components/ExportModal";
const { CheckableTag } = Tag;
function convertSeconds(seconds: number) {
    const hours = Math.floor(seconds / 3600);
    const remainingSeconds = seconds % 3600;
    const minutes = Math.floor(remainingSeconds / 60);
    const finalSeconds = remainingSeconds % 60;
    let result = "";
    if (hours > 0) {
        result += `${hours}小时`;
    }
    if (hours > 0 || minutes > 0) {
        result += `${minutes}分钟`;
    }
    result += `${finalSeconds}秒`;
    return result.trimStart();
}
const { AttackTypeRefer } = config
const LogAttack = (props: any) => {
    const Context = useContext(ProviderContext)
    const [findTarget, setFindTarget] = useState("log")//event
    const [searchData, setSearchData] = useState({})
    const [attackData, setAttackData] = useState([
       
    ])
    const [logData, setLogData] = useState([
 
    ])
    const [loadingLog,setLoadingLog]=useState(false)
    const columns = [
        {
            title: wordList["源IP"][Context.lan],
            key: 'ip',
            dataIndex: 'ip',
            width: 150,
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '50px',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                }}>
                    <div style={{
                        display: 'flex',
                        width: '100%',
                        height: '30px',
                        fontWeight: 200,
                        lineHeight: '30px',
                        alignItems: 'center',
                        gap: '10px'
                    }}>
                        <div>
                            {
                                record?.ip?.number || '-'
                            }
                        </div>
                    </div>
                    <div

                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {record?.ip?.placement || '未知'}
                    </div>
                </div>
            }
        }, {
            title: wordList["网站"][Context.lan],
            key: 'url',
            dataIndex: 'url',
            width: 600,
            render: (_: any, record: any, index: number) => {
                return record.url
            }
        }, {
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            title: wordList["攻击次数"][Context.lan],
            key: 'attackTimes',
            dataIndex: 'attackTimes',
            width: 100,
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    display: 'flex',
                    justifyContent: 'center'
                }}>
                    <Tag color="#f4424d">{record.attackTimes}</Tag>
                </div>
            }
        }, {
            title: wordList["持续时间"][Context.lan],
            key: 'keepTime',
            dataIndex: 'keepTime',
            width: 115,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    {
                        convertSeconds(record.keepTime)
                    }
                </div>
            }
        }, {
            title: wordList["开始时间"][Context.lan],
            key: 'startTime',
            dataIndex: 'startTime',
            width: 100,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    gap: '5px',

                }}>
                    {
                        moment(record.startTime).format('YYYY/MM/DD HH:mm:ss')
                    }
                </div>
            }
        }
    ]
    const [detail, setDetail] = useState<{ open: boolean, data: any }>({
        open: false,
        data: {}
    })
    const [detailLoading, setDetailLoading] = useState(false)
    const logColumns:ColumnsType<any> & any = [
        {
            title: wordList["动作"][Context.lan],
            dataIndex: 'action',
            key: 'action',
            width: 100,
            render: (_: any, record: any, index: number) => {
                return record.action == 1 ? <Tag color="#f4424d">{wordList["拦截"][Context.lan]}</Tag> : <Tag color="#0fc6c2">{wordList["放行"][Context.lan]}</Tag>
            }
        },
        {
            title: wordList["攻击地址"][Context.lan],
            dataIndex: "url",
            key: 'url',
            width: 300,
            render: (_: any, record: any, index: number) => {
                return record.url
            }
        },
        {
            title: "PATH",
            dataIndex: "path",
            key: 'path',
            
            width: 500,
            render: (_: any, record: any, index: number) => {
                return <div style={{whiteSpace:"wrap",maxWidth:'300px'}}>{record.path}</div>
            }
        },
        {
            title: wordList["状态码"][Context.lan],
            dataIndex: 'res_status',
            align:'center',
            key: 'res_status',
            width: 100,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (res_status: string) => {
                return res_status
            }

        },
        {
            title: wordList["攻击类型"][Context.lan],
            dataIndex: 'attack_type',
            key: 'attack_type',
            width: 100,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (type: string) => {
                return <div style={{
                    width: '100%',
                    textAlign: 'center'
                }}>{AttackTypeRefer[type]}</div>
            }

        },
        {
            title: wordList["源IP"][Context.lan],
            key: 'src_ip',
            dataIndex: 'src_ip',
            width: 150,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '30px',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <div >
                        {
                            record?.src_ip || '-'
                        }
                    </div>
                    {<div

                        style={{
                            fontSize: '12px',
                            height: '20px',
                            lineHeight: '20px',
                            color: "#999999"
                        }}>
                        {record?.src_country || '内网IP'}

                        
                        
                        {record?.src_subdivision?('-'+(record?.src_subdivision || '内网IP')):""}
                    </div>}
                </div>
            }
        },
        {
            title: wordList["目标IP"][Context.lan],
            key: 'dst_ip',
            dataIndex: 'dst_ip',
            width: 150,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '30px',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <div>
                        {
                            record?.dst_ip || '-'
                        }
                    </div>
                </div>
            }
        },
        {
            title: wordList["目标端口"][Context.lan],
            key: 'dst_port',
            dataIndex: 'dst_port',
            width: 150,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '30px',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <div>
                        {
                            record?.dst_port || '-'
                        }
                    </div>
                </div>
            }
        },
        {

            title: wordList["时间"][Context.lan],
            key: 'time',
            dataIndex: 'time',
            width: 100,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    gap: '5px',

                }}>
                    {
                        moment(record.time || '').format('YYYY/MM/DD HH:mm:ss')
                    }
                </div>
            }
        },
        {
            title: "",
            width: 50,
            dataIndex: 'operation',
            key: "operation",
            render: (_: any, record: any, index: number) => {
                return <BugsLinkButton onClick={() => {
                    setDetailLoading(false)
                    setDetail({
                        data: record,
                        open: true
                    })
                }}>{wordList["详情"][Context.lan]}</BugsLinkButton>
            }
        }
    ]
    const [logpPageInfo, setLogPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const getLogRecord = (page = 1, pageSize = 10, options = {}) => {
        setLoadingLog(true)
        api.record.getRecordList({
            page,
            page_size: pageSize,
            sort:"-time",
            ...options
        })
            .then(res => {
                setLoadingLog(false)
                if (res?.data?.data) {
                    setLogData(res.data.data.rows)
                    setLogPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    useEffect(() => {
        if (findTarget == 'event') {

        } else {
            getLogRecord()
        }
    }, [findTarget])
    const searchTraceId = (id) => {
        id = id.replace(/\s/g, "")
        setDetail({
            data: { trace_id: id },
            open: true
        })
    }
    const [showExport,setShowExport]=useState(false)
    return <>
        {
            detail.open
                ? <LogDetail
                    key={detail?.data?.id || '-'}
                    loading={detailLoading}
                    data={detail.data}
                    close={() => {
                        setDetail({
                            ...detail,
                            open: false
                        })
                    }}></LogDetail>
                : null
        }
        {
            showExport
            ?<ExportModal
            current={logpPageInfo.page}
            pageSize={logpPageInfo.page_size}
            total={logpPageInfo.total}
            nowPageSize={logData.length || 0}
            query={searchData}
             close={()=>{
                setShowExport(false)
            }}></ExportModal>
            :null
        }
        <CustomCard title={
            <div style={{
                width:'100%',
                display:'flex',
                justifyContent:'space-between'
            }}>
                <div>{wordList["攻击日志"][Context.lan]}</div>
                <BugsButton type="primary" onClick={()=>{
                    setShowExport(true)
                }}>导出日志</BugsButton>
            </div>
            
            //wordList["攻击检测"][Context.lan]
        }
            extra={
                null
            }
            style={{
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: '100%',
            }}>
            <div
                className="search-bar"
                key={findTarget}
                style={{
                    alignItems: 'center',
                    display: 'flex',
                    flexWrap: 'wrap',
                    width: '100%',
                    height: 'auto',
                    gap: '10px'
                }}>
                {/*<Radio.Group
                    style={{
                        flexShrink: 0,
                    }}
                    options={[
                        { label: wordList["攻击事件"][Context.lan], value: 'event' },
                        { label: wordList["攻击日志"][Context.lan], value: 'log' },
                    ]}
                    onChange={(e) => {
                        setFindTarget(e.target.value)
                    }}
                    value={findTarget}
                    optionType="button"
                    buttonStyle="solid"
                />
                <Divider type="vertical"></Divider>*/}
                <SearchBar searchQuery={(searchData) => {
                    getLogRecord(1, logpPageInfo.page_size, searchData)
                    setSearchData(searchData)
                }} type={findTarget} searchTrace={(traceId) => searchTraceId(traceId)}></SearchBar>
                <BugsLinkButton icon={<SyncOutlined></SyncOutlined>} onClick={()=>{
                    getLogRecord(logpPageInfo.page,logpPageInfo.page_size,searchData || {})
                }}></BugsLinkButton>
            </div>
            <Spin spinning={loadingLog}>
                <div
                    className="log-table-container">
                    {
                        findTarget == "event"
                            ? <CustomTable columns={columns}
                                useBorder={true}
                                dataSource={attackData}
                                loading={false}
                                onChange={() => { }}
                                scroll={{ x: 'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
                                size={'middle'}
                            ></CustomTable>
                            : <CustomTable columns={logColumns}
                                dataSource={logData}
                                useBorder={true}
                                pagination={{
                                    pageSize: logpPageInfo.page_size,
                                    current: logpPageInfo.page,
                                    total: logpPageInfo.total
                                }}

                                loading={false}
                                onChange={(e) => {
                                   
                                    getLogRecord(e.current, e.pageSize, searchData)
                                }}
                                scroll={{ x: 'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
                                size={'small'}
                            ></CustomTable>
                    }

                </div>
            </Spin>

        </CustomCard>
    </>
}
export default LogAttack
