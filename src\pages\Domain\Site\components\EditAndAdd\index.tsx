//新增站点
import { <PERSON><PERSON>, Checkbox, Divider, Drawer, Input, InputNumber, message, Modal, Popconfirm, Radio, Select, Space, Switch, Tooltip } from "antd"
import { useContext, useEffect, useState } from "react"
import "./index.less"
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons"
import wordList from "../../../../../config/wordList"
import ProviderContext from "../../../../providerContext"
import api from "../../../../../api"
import { Link } from "react-router-dom"
import <PERSON><PERSON>inkButton from "../../../../../components/BugsLinkButton"
const EditAndAdd = (props: any) => {
    const Context = useContext(ProviderContext)
    const [data, setData] = useState<any>({
        title: "",
        listeningPort: [{
            port: "",
            useSSL: false
        }],
        upstreams: [{
            ip: ""
        }],
        source_ip_config: {
            headers: "",
            index: 0,
            trust_proxies: []
        },
        description: "",
        mode: 0,
        type: 0,
        enabled: true,
        server_names: [],
        file_upload_module: {
            enabled: false,
            disallow_exts: ["exe", "bat", "cmd", "sh", "php", "jsp", "asp", "aspx"],
            strict: false
        }
    })
    const [open, setOpen] = useState(true)
    const [saveing, setSaving] = useState(false)

    const onSave = () => {
        if (!data.title) {
            message.error("请填写站点名称")
            return
        }

        if (data.listeningPort.filter(item => !item.port).length > 0) {
            message.error("请填写端口")
            return
        }
        if (data.upstreams.length == 0) {
            message.error("请填写上游服务器信息")
            return
        }
        let saveData = {
            ports: data.listeningPort.map(item => {
                if (item.useSSL) return `${item.port}_ssl`
                return item.port
            }),
            description: data.description,
            title: data.title,
            source_ip_config: data.source_ip_config || {},
            type: data.type,
            mode: data.mode,
            upstreams: data.upstreams,
            enabled: data.enabled,
            server_names: data.server_names,
            file_upload_module: data.file_upload_module || {}
        }
        if (data.server_names.length == 0) {
            message.error(wordList['请输入域名匹配规则'][Context.lan])
            return
        }
        if (saveData.ports.length == 0) {
            message.error(wordList['请输入服务端口域名'][Context.lan])
            return
        }
        setSaving(true)
        if (props.type == 'add') {
            api.site.createSite(saveData)
                .then(res => {
                    if (res?.data?.data) {
                        message.success(wordList['创建成功'][Context.lan])
                        props.updateSite && props.updateSite()
                        setOpen(false)
                    }
                    setSaving(false)
                })
            return
        }
        api.site.editSite(data.id, saveData)
            .then(res => {
                if (res?.data?.data) {
                    message.success(wordList['修改成功'][Context.lan])
                    props.updateSite && props.updateSite()
                    setOpen(false)
                }
                setSaving(false)
            })

    }
    useEffect(() => {
        let upstreams = (props?.data || {})?.upstreams ?? []
        let listeningPort = (props?.data?.ports || []).map(item => {
            let ports = item.split('_')
            let useSSL = ports.includes("ssl")
            let port = parseInt(ports[0])
            return {
                port: port,
                useSSL,
            }
        })
        if (upstreams.length == 0) {
            upstreams = ['']
        }
        if (listeningPort.length == 0) {
            listeningPort = [{
                port: "",
                useSSL: false
            }]
        }
        let file_upload_module = props?.data?.file_upload_module
        if (!file_upload_module?.disallow_exts || file_upload_module?.disallow_exts == null) {
            file_upload_module = {
                ...file_upload_module || {},
                disallow_exts: ["exe", "bat", "cmd", "sh", "php", "jsp", "asp", "aspx"],
            }
        }

        setData({
            ...data,
            ...(props?.data || {}),
            upstreams: upstreams,
            listeningPort: listeningPort,
            file_upload_module
        })
    }, [])
    return <Drawer

        title={(props.type == "add" ? wordList["添加"][Context.lan] : wordList["编辑"][Context.lan]) + wordList["站点"][Context.lan]}
        open={open}
        onClose={() => {
            setOpen(false)
        }}
        afterOpenChange={(e) => {
            if (!e) {

                props.close && props.close()
            }
        }}
        width={640}
        footer={<div style={{
            marginTop: '20px',
            width: '100%',
            display: 'flex',
            justifyContent: "space-between"
        }}>
            {/*<Checkbox>{wordList["启用上游服务器健康检查"][Context.lan]}</Checkbox>
            <Checkbox checked={data.enabled} onChange={(e) => { setData({ ...data, enabled: e.target.checked }) }}>{wordList["启用此站点防护"][Context.lan]}</Checkbox>*/}
            <div></div>
            <Space>
                <BugsLinkButton
                    style={{
                        color: "#cc1212"
                    }}
                    onClick={() => {
                        setOpen(false)
                    }}>
                    {wordList["取消"][Context.lan]}
                </BugsLinkButton>
                <Button type="primary"

                    loading={saveing}
                    onClick={onSave}
                    style={{
                        background: '#cc1212',
                        borderRadius: '10px'
                    }}>
                    {wordList["提交"][Context.lan]}</Button>
            </Space>
        </div>}>
        <div className="domain-edit-container">
            <div className="add-item-container">
                <Divider orientation="left">{wordList["名称"][Context.lan]}</Divider>
                <Input
                    value={data.title}
                    onChange={(e) => {
                        setData({
                            ...data,
                            title: e.target.value
                        })
                    }}
                    style={{
                        width: '100%',
                        borderRadius: '10px'
                    }}></Input>
            </div>
            <div className="add-item-container">
                <Divider orientation="left">{wordList["域名"][Context.lan]}</Divider>
                <Select
                    size="large"
                    dropdownStyle={
                        {
                            display: 'none'
                        }
                    }
                    mode="tags"
                    value={data?.server_names || []}
                    style={{ width: '100%', borderRadius: '10px' }}
                    placeholder={`www.example.com，支持通配符*`/**wordList['输入服务域名匹配规则'][Context.lan] */}
                    onChange={(e) => {
                        setData({
                            ...data,
                            server_names: e
                        })
                    }}
                    options={[]}
                />
            </div>
            <div className="add-item-container">
                <Divider orientation="left">{wordList["端口"][Context.lan]}</Divider>
                {
                    data.listeningPort.map((item: { port: number, useSSL: boolean }, index) => {
                        return <>
                            <Space style={{
                                width: '350px',
                                gap: '20px',
                                marginBottom: '20px'
                            }}>
                                <InputNumber
                                    controls={false}
                                    onChange={(e) => {
                                        setData({
                                            ...data,
                                            listeningPort: [
                                                ...data.listeningPort.slice(0, index),
                                                {
                                                    ...data.listeningPort[index],
                                                    port: e
                                                },
                                                ...data.listeningPort.slice(index + 1)
                                            ]
                                        })
                                    }}
                                    value={item.port}
                                    style={{
                                        width: '450px',
                                        borderRadius: '10px'
                                    }}></InputNumber>
                                <Checkbox checked={item.useSSL} onChange={(e) => {
                                    setData({
                                        ...data,
                                        listeningPort: [
                                            ...data.listeningPort.slice(0, index),
                                            {
                                                ...data.listeningPort[index],
                                                useSSL: e.target.checked
                                            },
                                            ...data.listeningPort.slice(index + 1)
                                        ]
                                    })
                                }}>SSL</Checkbox>
                                <Popconfirm
                                    title={wordList["确认删除此端口"][Context.lan]}
                                    onConfirm={() => {
                                        let tempPort = JSON.parse(JSON.stringify([
                                            ...data.listeningPort.slice(0, index),
                                            ...data.listeningPort.slice(index + 1)
                                        ]))
                                        setData({
                                            ...data,
                                            listeningPort: tempPort
                                        })
                                    }}
                                >

                                    <DeleteOutlined style={{ cursor: "pointer", flexShrink: 0 }}></DeleteOutlined>
                                </Popconfirm>
                            </Space>
                            <br />
                        </>
                    })
                }
                <BugsLinkButton
                    onClick={() => {
                        for (let i = 0; i < data.listeningPort.length; i++) {
                            if (!data.listeningPort[i].port) {
                                message.error(wordList["存在未填写端口的项，无法新增"][Context.lan])
                                return
                            }
                        }
                        setData({
                            ...data,
                            listeningPort: [
                                ...data.listeningPort,
                                {
                                    port: "",
                                    useSSL: false
                                }
                            ]
                        })
                    }}
                    style={{
                        marginTop: '10px',
                        color: "#cc1212",
                        width: '100%',
                        backgroundColor: "#cc121230",
                        borderRadius: '10px'
                    }} icon={<PlusOutlined style={{ color: "#cc1212" }}></PlusOutlined>}>{wordList["添加监听端口"][Context.lan]}</BugsLinkButton>
            </div>
            {/*<div style={{

                width: 'calc(100% - 20px)',
                marginTop: '20px',
                marginLeft: '10px',
                padding: "10px",
                boxSizing: "border-box",
                borderRadius: '10px',
                boxShadow: '0 0 10px rgba(0,0,0,0.1)'
            }}>
                <Divider orientation="left">{wordList["证书"][Context.lan]}</Divider>
                <Select
                    defaultValue="license1"
                    style={{ width: '100%' }}
                    onChange={() => { }}
                    options={[
                        {
                            value: '证书1',
                            label: 'license1',
                        },
                        {
                            value: '证书2',
                            label: 'license2',
                        },
                    ]}
                />
            </div>*/}
            <div className="add-item-container">
                <Divider orientation="left">{wordList["负载均衡"][Context.lan]}</Divider>
                {
                    data.upstreams.map((item, index) => {
                        return <>
                            <Space style={{
                                width: '350px',
                                gap: '20px',
                                marginBottom: '20px'
                            }}>
                                <span style={{ whiteSpace: 'nowrap' }}>IP/{wordList["域名"][Context.lan]}:</span>
                                <Input
                                    placeholder="http://*************:8080，不支持路径"
                                    onChange={(e) => {
                                        setData({
                                            ...data,
                                            upstreams: [
                                                ...data.upstreams.slice(0, index),
                                                e.target.value,
                                                ...data.upstreams.slice(index + 1)
                                            ]
                                        })
                                    }}
                                    value={item}
                                    style={{
                                        width: '460px',
                                        borderRadius: '10px'
                                    }}
                                ></Input>
                                {/*<span style={{ whiteSpace: 'nowrap' }}>{wordList["服务端口"][Context.lan]}:</span>
                                <InputNumber
                                    controls={false}
                                    onChange={(e) => {
                                        setData({
                                            ...data,
                                            upstreams: [
                                                ...data.upstreams.slice(0, index),
                                                {
                                                    ...data.upstreams[index],
                                                    port: e
                                                },
                                                ...data.upstreams.slice(index + 1)
                                            ]
                                        })
                                    }}
                                    value={item.port}
                                    style={{
                                        width: '100px',
                                        borderRadius: '10px'
                                    }}></InputNumber>*/}
                                <Popconfirm
                                    title={wordList["确认删除此节点"][Context.lan]}
                                    onConfirm={() => {
                                        let tempNodes = JSON.parse(JSON.stringify([
                                            ...data.upstreams.slice(0, index),
                                            ...data.upstreams.slice(index + 1)
                                        ]))
                                        setData({
                                            ...data,
                                            upstreams: tempNodes
                                        })
                                    }}
                                >

                                    <DeleteOutlined style={{ cursor: "pointer", flexShrink: 0 }}></DeleteOutlined>
                                </Popconfirm>
                            </Space>
                            <br />
                        </>
                    })
                }
                <BugsLinkButton
                    onClick={() => {
                        for (let i = 0; i < data.upstreams.length; i++) {
                            if (!data.upstreams[i]) {
                                message.error(wordList["存在未填的项，无法新增"][Context.lan])
                                return
                            }
                        }
                        setData({
                            ...data,
                            upstreams: [
                                ...data.upstreams,
                                ""
                            ]
                        })
                    }}
                    style={{
                        marginTop: '10px',
                        width: '100%',
                        color: "#cc1212",
                        backgroundColor: "#cc121230",
                        borderRadius: '10px'
                    }} icon={<PlusOutlined style={{ color: "#cc1212" }}></PlusOutlined>}>
                    {wordList["添加上游服务器"][Context.lan]}
                </BugsLinkButton>

            </div>

            <div className="add-item-container">
                <Divider orientation="left">{wordList["类型"][Context.lan]}</Divider>
                <Radio.Group defaultValue={0} value={data.type} buttonStyle="solid">
                    <Radio.Button value={0}>{wordList["反向代理"][Context.lan]}</Radio.Button>
                </Radio.Group>
            </div>
            <div className="add-item-container">
                <Divider orientation="left">{wordList["运行模式"][Context.lan]}</Divider>
                <Radio.Group
                    defaultValue={0} value={data.mode} onChange={(e) => { setData({ ...data, mode: e.target.value }) }} buttonStyle="solid">
                    <Radio.Button value={0}>{wordList["防护"][Context.lan]}</Radio.Button>
                    <Radio.Button value={1}>{wordList["观察"][Context.lan]}</Radio.Button>
                    <Radio.Button value={2}>{wordList["维护"][Context.lan]}</Radio.Button>
                </Radio.Group>
            </div>
            <div className="add-item-container">
                <Divider orientation="left">{wordList["源IP配置"][Context.lan]}</Divider>

                <div style={{ display: 'flex', width: '100%', gap: '20px', alignItems: 'center' }}>
                    <div style={{ width: '150px' }}>Header</div>

                    <Select
                        size="large"
                        dropdownStyle={
                            {
                                display: 'none'
                            }
                        }

                        mode="tags"
                        value={data.source_ip_config.headers || []}
                        style={{ width: '100%', borderRadius: '5px' }}
                        placeholder={'获取源IP的headers'}
                        onChange={(e) => {
                            setData({
                                ...data,
                                source_ip_config: {
                                    ...(data.source_ip_config || {}),
                                    headers: e
                                }
                            })
                        }}
                        options={[]}
                    />
                </div>
                <div style={{ display: 'flex', width: '100%', gap: '20px', alignItems: 'center', marginTop: '20px' }}>
                    <div style={{ width: '150px' }}>Header的索引</div>
                    <InputNumber
                        placeholder="获取源IP的Header的索引，0为左边第一个，-1为右边第一个"
                        controls={false}
                        onChange={(e) => {
                            setData({
                                ...data,
                                source_ip_config: {
                                    ...(data.source_ip_config || {}),
                                    index: e
                                }
                            })
                        }}
                        value={data?.source_ip_config?.index ?? ''}
                        style={{
                            width: '100%',
                            borderRadius: '5px'
                        }}></InputNumber>
                </div>
                <div style={{ display: 'flex', width: '100%', gap: '20px', alignItems: 'center', marginTop: '20px' }}>
                    <div style={{ width: '150px' }}>信任的代理IP</div>
                    <Select
                        size="large"
                        dropdownStyle={
                            {
                                display: 'none'
                            }
                        }

                        mode="tags"
                        value={data.source_ip_config.trust_proxies || []}
                        style={{ width: '100%', borderRadius: '5px' }}
                        placeholder={wordList['信任的代理IP'][Context.lan]}
                        onChange={(e) => {
                            setData({
                                ...data,
                                source_ip_config: {
                                    ...(data.source_ip_config || {}),
                                    trust_proxies: e
                                }
                            })
                        }}
                        options={[]}
                    />
                </div>
            </div>

            <div className="add-item-container">
                <Divider orientation="left">文件上传防护</Divider>
                <div style={{ display: 'flex', width: '100%', gap: '20px', alignItems: 'center' }}>
                    <div style={{ width: '150px' }}>模块配置</div>
                    <div style={{
                        display: "flex",
                        width: '100%',
                        height: '40px',
                        gap: '10px',
                        alignItems: 'center'
                    }}>
                        <div>是否启用</div>
                        <Switch checked={data?.file_upload_module?.enabled}
                            onChange={(e) => {
                                setData({
                                    ...data,
                                    file_upload_module: {
                                        ...data.file_upload_module || {},
                                        enabled: e
                                    }
                                })
                            }}></Switch>
                        <Tooltip title="严格模式，启用后会对文件内容是否匹配扩展名进行检查">

                            <Checkbox style={{ marginLeft: '30px' }} checked={data?.file_upload_module?.strict}
                                onChange={(e) => {
                                    setData({
                                        ...data,
                                        file_upload_module: {
                                            ...data.file_upload_module || {},
                                            strict: e.target.checked
                                        }
                                    })
                                }}
                            >严格模式</Checkbox>
                        </Tooltip>
                    </div>
                </div>

                <div style={{ display: 'flex', width: '100%', gap: '20px', alignItems: 'center', marginTop: '10px' }}>
                    <div style={{ width: '150px' }}>文件类型</div>
                    <div style={{
                        height: 'auto',
                        display: "flex",
                        flexDirection: 'column',
                        width: '100%',
                        gap: '5px'
                    }}>
                        <Select
                            size="large"
                            dropdownStyle={
                                {
                                    display: 'none'
                                }
                            }
                            mode="tags"
                            value={data?.file_upload_module?.disallow_exts || []}
                            style={{ width: '100%', borderRadius: '10px' }}
                            placeholder={`输入文件类型 php/exe/jsp 等`/**wordList['输入服务域名匹配规则'][Context.lan] */}
                            onChange={(e) => {
                                setData({
                                    ...data,
                                    file_upload_module: {
                                        ...data.file_upload_module || {},
                                        disallow_exts: e
                                    }
                                })
                            }}
                            options={[]}
                        />
                        <div style={{color:"#999"}}>启用后将<span style={{color:"#f5222d"}}>禁止上传</span>上述文件类型</div>
                    </div>

                </div>

            </div>

            <div className="add-item-container">
                <Divider orientation="left">{wordList["备注"][Context.lan]}</Divider>
                <Input
                    value={data.description}
                    onChange={(e) => {
                        setData({
                            ...data,
                            description: e.target.value
                        })
                    }}
                    style={{
                        width: '100%',
                        borderRadius: '10px'
                    }}></Input>
            </div>
            {props.type == 'edit'
                ? <div style={{ marginTop: "20px" }}>{wordList["更多设置请前往"][Context.lan]}<span><Link style={{ color: "#cc1212" }} to={`/domain/siteDetail/${data.id}`}>{wordList["站点详情页"][Context.lan]}</Link></span>{wordList["进行设置"][Context.lan]}</div>
                : null}


        </div>
    </Drawer >
}
export default EditAndAdd