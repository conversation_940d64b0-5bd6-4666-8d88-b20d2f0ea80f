.home-page-container {
    width: 100%;
    height: 100vh;
    overflow-y: auto;
    background-color: #eef1fa;
}

.home-page-container-top {
    width: 100%;
    display: flex;
    height: 115px;
    gap: 20px;
}

.home-page-top-overview-container {
    width: 70%;
    height: 100%;
    border-radius: 10px;
    background-color: white;
    padding: 10px;
    box-sizing: border-box;
}

.home-page-top-overview-time {
    width: 30%;
    min-width: 480px;
    height: 100%;
    border-radius: 10px;
    background-color: #cc1212;
    padding: 10px;
    box-sizing: border-box;
    align-items: center;
    padding-left: 30px;
    display: flex;
}

.home-page-top-time-date {
    width: 100%;
    flex-shrink: 0;
    font-size: 18px;
    white-space: nowrap;
    display: flex;
    font-weight: 700;
    font-size: 14px;
    font-family: Mono;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.home-page-overview-item {
    width: 25%;
    height: 40px;
    display: flex;
    flex-direction: column;
}

.home-page-overview-item-title {
    font-weight: bold;
    font-size: 12px;
    white-space: nowrap;
    color: #999;
    text-align: center;
}

.home-page-overview-item-content {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
}

.home-page-container-center {
    margin-top: 20px;
    width: 100%;
    overflow: hidden;
    height: 480px;
    display: flex;
    gap: 20px;

    .ant-table {
        border: none;
    }
}

.home-page-table-container {
    width: calc(50% - 10px);
    height: 100%;
    overflow-y: auto;
    position: relative;

    .ant-table-thead {
        position: sticky;
        top: 0;
        z-index: 2;
        background-color: white;
    }

    .ant-table-content {
        overflow-y: hidden;
    }

    div {
        height: 100% !important;

        table {
            height: 100% !important;
        }
    }
}


.home-page-ip-list {
    width: 70%;
    height: 100%;
    border-radius: 10px;
    background-color: white;
    padding: 10px;
    box-sizing: border-box;
}

.home-page-attack-overview {
    width: 100%;
    height: 35%;
    border-radius: 10px;
    padding: 10px;
    background-color: white;
    box-sizing: border-box;

}

.attack-overview-container {
    width: calc(100% - 20px);
    margin-left: 10px;
    height: calc(100% - 50px);
    display: flex;
    border-radius: 10px;
    background-image: linear-gradient(rgba(15, 109, 200, 0.16) 0%,
            rgba(90, 158, 222, 0.16) 0%,
            rgba(96, 159, 220, 0.16) 0%,
            rgba(207, 228, 248, 0.16) 0%,
            rgba(200, 216, 232, 0.16) 0%,
            rgba(94, 171, 243, 0.16) 0%,
            rgba(102, 174, 242, 0.16) 15%,
            rgba(197, 210, 223, 0.16) 100%,
            rgba(179, 179, 179, 0.16) 100%);
}

.attack-overview-item {
    width: 33%;
    height: 100%;
    display: flex;
    gap: 10px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: left;
}

.home-page-ip-chart {
    width: 100%;
    height: 60%;
    background-color: white;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;
}

.home-page-container-bottom {
    width: 100%;
    height: 260px;
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.home-page-card-title {
    width: auto;
    height: 30px;
    line-height: 30px;
    position: relative;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 10px;
}

.home-page-card-title::before {
    content: '';
    position: absolute;
    left: -10px;
    z-index: 2;
    height: 20px;
    background-color: #cc1212;
    top: 5px;
    width: 5px;
    border-radius: 10px;
}


.progress-list-item {
    width: 100%;
    height: 10%;
    min-height: 40px;
    display: flex;
    flex-direction: column;
}

.home-page-container-bottom {
    .progress-list-container {
        overflow: hidden;
    }

    .progress-list-item {
        height: 20%;
    }
}

.progress-list-item-text-conainer {
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: space-between;
}

.progress-list-item-text-title {
    width: auto;
    font-size: 12px;
    color: #999;
    text-align: left;
}

.progress-list-item-text-data {
    width: auto;
    font-size: 12px;
    font-weight: bold;
    text-align: right;
}

.home-page-table-row {
    .ant-table-cell {
        padding-top: 5px;
        padding-bottom: 5px;
    }
}

.attack-overview {
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-width: 480px;
    gap: 20px
}

@media screen and (max-width: 1320px) {
    .home-page-container-center {
        flex-direction: column;
        height: auto
    }

    .home-page-ip-list {
        width: 100%;
    }

    .attack-overview {
        width: 100%;
        height: 450px;

        .home-page-attack-overview {
            height: 180px;
        }

        .home-page-ip-chart {
            height: 280px;
        }
    }

}

@media screen and (max-width: 1060px) {
    .attack-overview {
        min-width: 0;
    }

    .home-page-top-overview-time {
        min-width: 0;
    }

    .home-page-container-top {
        flex-direction: column-reverse;
        height: auto;

        .home-page-top-overview-container {
            width: 100%;
            height: 120px;
        }

        .home-page-top-overview-time {
            width: 100%;
        }

    }

    .home-page-container-bottom {
        flex-direction: column;

        .home-page-domain-list {
            width: 100%;
        }
    }
}

.home-page-top-overview-time {
    .top-time-selector {
        width: 60%;
    }
}

.home-page-ip-list-layout {
    width: 100%;
    height: calc(100% - 40px);
    display: flex
}

@media screen and (max-width: 830px) {
    .home-page-ip-list-layout {
        flex-direction: column;
        gap: 10px;

        .home-page-table-container {
            width: 100%;
        }
    }
}

@media screen and (max-width: 720px) {
    .home-page-top-overview-time {
        padding-left: 10px;
        flex-direction: column-reverse;

        .top-time-selector {
            width: 100%;
        }

        .home-page-top-time-date {
            flex-direction: row;
            margin-bottom: 5px;
            gap: 20px;
        }
    }

}

.left-bar-item-container {
    width: 100%;
    height: 70px;
    display: flex;
    align-items: center;
    gap: 30px;
    margin-top: 20px;
}

.left-bar-item-icon-contianer {
    height: 70px;
    width: 70px;
    border-radius: 10px;
    padding: 12px;
    box-sizing: border-box;

}

.left-bar-item-icon-inner {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

.left-bar-item-text-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 5px;
    justify-content: center;
}

.left-bar-attack-ip-container {
    position: absolute;
    left: 20px;
    bottom: 100px;
    width: 300px;
    height: 300px;
    border-radius: 10px;
    background-color: white;
    padding: 20px;
    box-sizing: border-box;
}

.left-bar-attack-ip-rank {
    width: 18px;
    height: 18px;
    position: relative;
    margin-right: 12px;
}

.left-bar-attack-ip-rank-1 {}

.left-bar-attack-ip-rank-1::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgb(245, 27, 82) 0%, rgba(255, 192, 176, 0.88) 100%);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.left-bar-attack-ip-rank-2 {}

.left-bar-attack-ip-rank-2::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgb(252, 134, 100) 0%, rgb(251, 173, 134) 100%);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.left-bar-attack-ip-rank-3 {}

.left-bar-attack-ip-rank-3::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgb(255, 184, 77) 0%, rgba(255, 184, 77, 0.88) 100%);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.left-bar-attack-ip-rank4 {}

.left-bar-attack-ip-rank-4::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgb(225, 225, 225) 0%, rgba(188, 188, 188, 0.88) 100%);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.left-bar-attack-ip-rank-other {}

.left-bar-attack-ip-rank-other::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(225, 225, 225, 0.2) 0%, rgba(188, 188, 188, 0.2) 100%);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.left-bar-attack-ip-rank-number {
    margin: 0px;
    font-family: AlimamaShuHeiTi;
    font-weight: 400;
    line-height: 1.5;
    color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
}
.left-bar-attack-ip{
    
    font-family: AlimamaShuHeiTi;
    font-weight: 400;
    line-height: 1.5;
    color: black;
    font-size: 14px;
}
