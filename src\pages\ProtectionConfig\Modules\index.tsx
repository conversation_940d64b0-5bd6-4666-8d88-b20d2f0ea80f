import { Tag, <PERSON><PERSON>, Card, DatePicker, Divider, Input, InputNumber, Popover, Radio, Space, Table, TimePicker, Switch, Select } from "antd"
import "./index.less"
import { useContext, useState } from "react"
import moment from "moment";
import { render } from "@testing-library/react";
import config from "../../../config";
import ProviderContext from "../../providerContext";
import wordList from "../../../config/wordList";
const { modeulesName } = config
const ProtectionModules = (props: any) => {
    const Context=useContext(ProviderContext)
    const [usedModules, setUsedModules] = useState({})



    return <>

        <Card
            title={wordList['防护模块配置'][Context.lan]}
            extra={
                null
            }
            style={{
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: '100%'
            }}>
            <div style={{
                height: 'calc(100vh - 230px)',
                width: '100%',
                overflowY: 'auto',
            }}>
                <div style={{
                    width: '100%',
                    height: "auto",
                    paddingLeft: '20px',
                    paddingRight: '20px',
                    boxSizing: 'border-box'
                }}>
                    {
                        Object.keys(modeulesName).map((moduleKey) => {
                            return <div style={{
                                width: '100%',
                                display: 'flex',
                                justifyContent: 'space-between',
                                height: '30px',
                                gap: '10px',
                                marginBottom: '30px'
                            }} className="modules-item">
                                <div style={{ width: '200px', lineHeight: '30px' }}>
                                    {wordList[modeulesName[moduleKey]][Context.lan]}
                                </div>
                                <Radio.Group
                                defaultValue={3}
                                onChange={(e) => { 
                                   
                                    setUsedModules({
                                        ...usedModules,
                                        [moduleKey]:e.target.value
                                    })
                                }} value={usedModules[moduleKey]}>
                                    <Radio value={0}>{wordList['禁用'][Context.lan]}</Radio>
                                    <Radio value={1}>{wordList['仅观察'][Context.lan]}</Radio>
                                    <Radio value={2}>{wordList['平衡防护'][Context.lan]}</Radio>
                                    <Radio value={3}>{wordList['高强度防护'][Context.lan]}</Radio>
                                </Radio.Group>
                            </div>
                        })
                    }
                </div>
            </div>
        </Card>
    </>
}
export default ProtectionModules
