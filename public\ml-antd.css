/* 按钮 */
.ml-button {
    font-size: 14px!important;
    color: #0061a6!important;
    background-color: rgba(0,119,204,.2)!important;
    min-width: 112px!important;
    height: 40px!important;
    border: none!important;
    border-radius: 6px!important;
}
.ml-button:hover span {
    text-decoration: underline!important;
}
.ml-button.blue {
    background-color: #07c!important;
    color: #fff!important;
}
.ml-button[disabled] {
    color: #00000040!important;
    border-color: #d9d9d9!important;
    background: #f5f5f5!important;
}
.ml-button.small {
    height: auto !important;
    min-width: auto !important;
}
/* 按钮组 */
.ml-button-group .ant-radio-button-wrapper {
    font-size: 14px!important;
    color: #0061a6!important;
    background-color: rgba(0,119,204,.2)!important;
    border: none!important;
}
.ml-button-group .ant-radio-button-wrapper.ant-radio-button-wrapper-checked {
    background-color: #07c!important;
    color: #fff!important;
}
.ml-button-group .ant-radio-button-wrapper.ant-radio-button-wrapper-disabled {
    color: #00000040!important;
    border-color: #d9d9d9!important;
    background: #f5f5f5!important;
}
.ml-button-group .ant-radio-button-wrapper:hover {
    text-decoration: underline!important;
}
.ml-button-group .ant-radio-button-wrapper:nth-child(1) {
    border-top-left-radius: 6px!important;
    border-bottom-left-radius: 6px!important;
}
.ml-button-group .ant-radio-button-wrapper:nth-last-child(1) {
    border-top-right-radius: 6px!important;
    border-bottom-right-radius: 6px!important;
}

/* 输入框 */
.ml-input {
    height: 40px!important;
    font-size: 14px!important;
    color: #343741!important;
    border-radius: 6px!important;
    padding: 8px 12px!important;
    line-height: 1.5!important;
}
.ml-input.small {
    height: 32px!important; 
}
.ml-textarea {
    border-radius: 6px!important;
    resize: none!important;
    font-size: 14px !important;
}

/* 数字输入框 */
.ml-input-number{
    width: 100% !important;
    height: 40px!important;
    font-size: 14px!important;
    color: #343741!important;
    border-radius: 6px!important;
    line-height: 1.5!important;
}

/* 下拉框 */
.ml-select {
    width: 100%;
}
.ml-select .ant-select-selector {
    border-radius: 6px!important;
}
.ml-select-large .ant-select-selection-item ,.ml-select-large .ant-select-selection-placeholder{
    font-size: 14px;
}

/* 单选框 */
.ant-radio-wrapper {
    align-items: center!important;
}
.ant-radio {
    top: 0!important;
}

/* 日期选择框 */
.ml-datepicker {
    width: 100%;
    border-radius: 6px!important;
    height: 40px!important;
}
.ant-picker-large .ant-picker-input > input{
    font-size: 14px !important;
}