import { Button } from "antd"

import type { ButtonProps } from "antd/es/button"
const BugsButton=(props:ButtonProps)=>{
    let propsStyle=props.style || {}
    let newProps={
        ...props
    }
    try{
        delete newProps.style
    }catch{}
    return <Button type="primary" style={{
        borderRadius: '10px', background: "#cc1212", border: 'none',
        ...propsStyle
    }} {...newProps}></Button>
}
export default BugsButton