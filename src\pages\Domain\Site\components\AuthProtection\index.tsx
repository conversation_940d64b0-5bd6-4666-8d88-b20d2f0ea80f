import { Divider, Modal, Select, Switch } from "antd"
import { useContext, useState } from "react"
import "./index.less"
import ProviderContext from "../../../../providerContext"
import wordList from "../../../../../config/wordList"
const AuthProtection = (props: any) => {
    const Context = useContext(ProviderContext)
    const [open, setOpen] = useState(true)
    /*<Modal
       open={open}
       title={wordList["身份验证"][Context.lan]}
       width={600}
       onCancel={(e) => {
           setOpen(false)
       }}
       afterClose={() => {
           props.close && props.close()
       }}
   >*/
    return <>
        <div className="botprotection-container-item" style={{ marginTop: '20px' }}>
            <div className="botprotection-container-item-title">{wordList["该功能开启后，当用户访问您的网站时，需要进行身份认证，不在下方身份源中的用户将被拒之门外。"][Context.lan]}</div>
            <Switch style={{ flexShrink: 0 }} checkedChildren={wordList["启用"][Context.lan]} unCheckedChildren={wordList["禁用"][Context.lan]}></Switch>
        </div>
        <div className="auth-protection-custom">
            {wordList["身份验证"][Context.lan]}

            <Divider orientation="left">{wordList["身份源"][Context.lan]}</Divider>
            <Select style={{
                width: '100%'
            }}>

            </Select>
        </div>
    </>
    /*</Modal>*/
}
export default AuthProtection