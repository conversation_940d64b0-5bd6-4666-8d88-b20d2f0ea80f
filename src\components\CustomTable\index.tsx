import "./index.less"
import { Table } from "antd"
import type { TableProps } from "antd"
import ProviderContext from "../../pages/providerContext"
import { useContext } from "react"
type CustomeTableType={
    useBorder?:Boolean
}
const CustomTable=(props:CustomeTableType & TableProps<any>)=>{
    const Context=useContext(ProviderContext)
    return <Table 
    className={props.useBorder===true?"use-border-custom-table":"unuse-border-custom-table"} 
    {...props}
    scroll={props.dataSource?.length==0?{}:(props.scroll || {})}
    pagination={
        {
            showSizeChanger:true,
            showTotal: total => `共 ${total} 条数据`,
            ...(props.pagination || {})
        }
    }
    ></Table>
}
export default CustomTable