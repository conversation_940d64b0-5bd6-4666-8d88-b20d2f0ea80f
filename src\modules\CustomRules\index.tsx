import { Button, Checkbox, Divider, Empty, Input, message, Modal, Radio, Space } from "antd"
import "./index.less"
import config from "../../config"
import { useContext, useEffect, useState } from "react"
import RuleItem from "./components/RuleItem"
import { PlusOutlined } from "@ant-design/icons"
import ProviderContext from "../../pages/providerContext"
import wordList from "../../config/wordList"
import BugsButton from "../../components/BugsButton"
import api from "../../api"
import BugsLinkButton from "../../components/BugsLinkButton"
let { ruleTypes, ruleTypeRefer } = config
const CustomeRulesModules = (props: any) => {
    const Context = useContext(ProviderContext)
    const [nowSelected, setNowSelected] = useState<any>(0)
    const [orConditions, setOrConditions] = useState<any>([])
    const [enabled,setEnabled]=useState(false)
    const [log,setLog]=useState(false)
    const [title, setTitle] = useState("")
    const removeCondition = (index) => {
        let preList = [...orConditions.slice()]
        let newList = [
            ...preList.slice(0, index),
            ...preList.slice(index + 1)
        ]
        setOrConditions(newList)
        /*setOrConditions([
            ...orConditions.slice(0, index),
            ...orConditions.slice(index + 1)
        ])*/
    }
    const addCondition = () => {
        setOrConditions([...orConditions, [
            /**AND CONDITIONS */
            {
                type: "IP",//源IP
                method: "eq",//匹配方式
                content: ""//内容
            }
        ]])
    }
    useEffect(() => {
        if (props.type == 'edit') {
            let data = props.data || {}
            setNowSelected(data?.action)
            setTitle(data.name)
            setOrConditions(data?.pattern || [])
            setEnabled(data.enabled ?? false)
            setLog(data?.log ?? false)
        } else {
            //新增
            setNowSelected(0)
            setTitle("")
            setOrConditions([])
            setEnabled( false)
            setLog(false)
        }

    }, [props])
    const [setting, setSetting] = useState(false)
    const confirmSave = (() => {
        if (title.length == 0) {
            message.error(wordList['保存失败，请填写规则名称'][Context.lan])
            return
        }
        if(orConditions.length==0){
            message.error(wordList['请添加规则'][Context.lan])
            return
        }
        console.log(orConditions)
        //遍历规则内容
        for (let i = 0; i < orConditions.length; i++) {
            for (let j = 0; j < orConditions[i].length; j++) {
                if (!orConditions[i][j].k || !orConditions[i][j].op) {
                    message.error(wordList['未选择匹配方式与匹配目标'][Context.lan])
                    return
                }
            }
        }
        setSetting(true)
        if (props.type == 'edit') {
            api.rules.editRules(props.data?.id,
                {
                    action: nowSelected,
                    pattern: orConditions,
                    enabled:enabled,
                    log:log,
                    name: title
                })
                .then(res => {
                    setSetting(false)
                    if (res?.data?.data) {

                        message.success(wordList['修改成功'][Context.lan])
                        props.onClose && props.onClose(true)
                    }
                })
        }else{
            api.rules.createRules({
                enabled:enabled,
                log:log,
                action: nowSelected,
                pattern: orConditions,
                name: title
            }).then(res => {
                setSetting(false)
                if (res?.data?.data) {
                    message.success(wordList['添加成功'][Context.lan])
                    props.onClose && props.onClose(true)
                }
            })
        }

    })
    return <div className="custom-rule-modules-container">

        <div style={{
            height: "40px",
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            width: '100%',
        }}>
            <div style={{
                whiteSpace: 'nowrap'
            }}><span style={{ color: 'red', lineHeight: '40px' }}>*</span> {wordList["规则名称"][Context.lan]}</div>
            <Input
                value={title}
                onChange={(e) => {
                    setTitle(e.target.value)
                }}
                style={{
                    width: '100%',
                }}></Input>
        </div>
        <div style={{
            width: '100%',
            display: 'flex',
            marginTop: '20px',
            gap: '10px',
            height: '40px',
            alignItems: 'center',
        }}>
            <div style={{
                whiteSpace: 'nowrap'
            }}><span style={{ color: 'red', lineHeight: '40px' }}>*</span> {wordList["规则类型"][Context.lan]}</div>
            <div style={{
                display: 'flex',
                gap: '16px',
                width: '100%'
            }}>
                {
                    ruleTypes.slice(1)
                        .map(item => {
                            return <div
                                onClick={() => {
                                    setNowSelected(item.value)
                                }}
                                className={`custom-rule-modules-type ${nowSelected == item.value ? 'custom-rule-modules-type-selected' : ''}`}>

                                <Radio checked={nowSelected == item.value}>

                                </Radio>
                                {ruleTypeRefer[item.value]?.icon}
                                {wordList[item.label][Context.lan]}
                            </div>
                        })
                }
            </div>


        </div>
        <Divider
            orientation="left"
            style={{
                marginBottom: '20px',
                marginTop: '20px',
                width: '100%'
            }}>{wordList["规则定义"][Context.lan]}</Divider>
        <div className="custom-rule-modules-list">
            {
                orConditions.length==0
                ?<Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={wordList["暂无自定义规则"][Context.lan]}/>
                :null
            }
            <div style={{
                width: '100%',
                display: 'flex',
                height: 'auto'
            }}>
                {/**左侧Or条件链接 */}
                {
                    orConditions.length > 1
                        ? <div style={{
                            display: 'flex',
                            flexDirection: 'column',
                            marginTop: '30px',
                            maxHeight: '100%'

                        }}>
                            <div style={{
                                zIndex: 2,
                                height: 'calc(100% - 30px)',
                                width: '30px',
                                flexShrink: 0,
                                display: 'flex',
                                marginLeft: '20px',
                                position: 'relative',
                                justifyItems: 'center',
                                borderTopLeftRadius: '5px',
                                borderBottomLeftRadius: '5px',
                                alignItems: 'center',
                                borderTop: '1px solid #e3e8ef',
                                borderBottom: '1px solid #e3e8ef',
                                borderLeft: '1px solid #e3e8ef'
                            }}>
                                <div style={{
                                    width: '40px',
                                    height: '22px',
                                    background: "white",
                                    border: '1px solid #ddd',
                                    marginLeft: '-20px',
                                    lineHeight: '20px',
                                    fontSize: '12px',
                                    borderRadius: '5px',
                                    textAlign: 'center'
                                }}>OR</div>
                            </div>
                            <div style={{
                                height: '30px',
                                flexShrink: 0,
                                width: '20px'
                            }}></div>
                        </div>
                        : null
                }
                <div style={{
                    width: '100%',
                    height: 'auto',

                }}>
                    {
                        orConditions.map((item, index) => {
                            return <RuleItem
                                style={
                                    {
                                        marginTop: '20px'
                                    }
                                }
                                preList={item}
                                updateAndConditions={(conditions) => {
                                    setOrConditions([
                                        ...orConditions.slice(0, index),
                                        conditions,
                                        ...orConditions.slice(index + 1)
                                    ])
                                }}
                                onRemove={(withoutAsk = false) => {
                                    if (withoutAsk) {
                                        removeCondition(index)
                                        return
                                    }
                                    Modal.confirm({
                                        title: wordList["操作确认"][Context.lan],
                                        content: wordList["确认删除此条件组"][Context.lan],
                                        onOk: () => {
                                            removeCondition(index)
                                        }
                                    })
                                }}
                                isFirst={index == 0} isEnd={index == orConditions.length - 1} ></RuleItem>
                        })
                    }

                </div>
            </div>
            <Button onClick={() => {
                addCondition()
            }} type="primary"
                style={{ marginTop: '20px', color: "#cc1212", width: '100%', borderRadius: '10px', background: "white", borderColor: '#cc1212' }}
                icon={<PlusOutlined></PlusOutlined>}>{wordList["添加一个OR条件"][Context.lan]}
            </Button>

        </div>
        <div style={{
            display: 'flex',
            marginTop: '20px',
            justifyContent: 'space-between'
        }}>
            <div style={{
                display:'flex',
            }}>
                <Checkbox checked={enabled} onChange={(e)=>setEnabled(e.target.checked)}>{wordList["启用此规则"][Context.lan]}</Checkbox>
                <Checkbox checked={log} onChange={(e)=>setLog(e.target.checked)}>{wordList["记录日志"][Context.lan]}</Checkbox>
            </div>
            <Space>
                <BugsLinkButton  onClick={() => {
                    props.onClose && props.onClose()
                }}>{wordList["取消"][Context.lan]}</BugsLinkButton>
                <BugsButton
                    style={{background:"#cc1212"}}
                    loading={setting}
                    onClick={() => {
                        confirmSave()
                    }}>{wordList["保存"][Context.lan]}</BugsButton>
            </Space>
        </div>
    </div>
}
export default CustomeRulesModules