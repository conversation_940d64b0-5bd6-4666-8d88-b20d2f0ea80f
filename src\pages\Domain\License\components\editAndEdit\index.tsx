import { Button, Divider, Input, message, Modal, Radio, Select, Space, Upload } from "antd"
import "./index.less"
import { useContext, useEffect, useState } from "react"
import { UploadOutlined } from "@ant-design/icons"
import type { UploadProps } from 'antd';
import ProviderContext from "../../../../providerContext";
import wordList from "../../../../../config/wordList";
import BugsLinkButton from "../../../../../components/BugsLinkButton";
import BugsButton from "../../../../../components/BugsButton";
import api from "../../../../../api";

const EditAndAdd = (props: any) => {
    const Context = useContext(ProviderContext)
    const [open, setOpen] = useState(true)
    const [license, setLicense] = useState({
        license: "",
        key: ""
    })
    const [licenseType, setLicenseType] = useState(0)
    const uploadProps: UploadProps = {
        name: 'file',
        action: '',
        headers: {
            authorization: 'authorization-text',
        },
        showUploadList: false,
        beforeUpload(file) {
            if (file) {
                // 创建FileReader对象
                const reader = new FileReader();

                // 监听文件读取完成事件
                reader.onload = function (e: any) {
                    // 将文件内容显示到页面上
                    if (file.name.endsWith(".key")) {
                        setLicense({
                            ...license,
                            key: e.target.result
                        })
                    } else if (file.name.endsWith(".crt") || file.name.endsWith(".pem")) {
                        setLicense({
                            ...license,
                            license: e.target.result
                        })
                    }
                };

                // 读取文件内容为文本
                reader.readAsText(file);
            }
        },
        onChange(info) {

        },
    };
    const [editing,setEditing]=useState(false)
    const confirmSaveLicense=()=>{
        
        let action
        if(!license.license){
            message.error("请输入证书")
            return
        }
        if(!license.key){
            message.error("请输入秘钥")
            return
        }
        setEditing(true)
        let options={
            type:licenseType,
            manual:{
                crt:btoa(license.license),
                key:btoa(license.key)
            }
        }
        if(props.type==='edit'){
            action=api.keyLicense.editLicense(props.target || '',
                options
            )
        }else{
            action=api.keyLicense.createLicense(options)
        }
        action
        .then(res=>{
            setEditing(false)
            if(res?.data?.data?.id){
                message.success("添加成功")
                setOpen(false)
                setLicense({
                    license:"",
                    key:""
                })
                props?.update && props.update()
            }
        })
    }
    const getDetail=(id)=>{
        api.keyLicense.getLicenseById(id)
        .then(res=>{
            if(res?.data?.data?.manual){
                setLicense({
                    license:res.data.data.manual.crt || '',
                    key:res.data.data.manual.key || ''
                })
            }
        })
    }
    useEffect(()=>{
        if(props?.type=='edit'){
            getDetail(props?.target || '-')
        }
    },[])
    return <Modal
        open={open}
        title={props.type == 'add' ? wordList["添加"][Context.lan] : wordList["编辑证书"][Context.lan]}
        onCancel={(e) => {
            setOpen(false)
        }}
        afterClose={() => {
            props.close && props.close()
        }}
        width={600}
        footer={
            <div style={{
                width:'100%',
                display:"flex",
                justifyContent:'space-between'
            }}>
                <div></div>
                <Space>
                    <BugsLinkButton onClick={()=>{
                        setLicense({
                            license:"",
                            key:""
                        })
                        setOpen(false)
                    }}>取消</BugsLinkButton>
                    <BugsButton
                        onClick={()=>{
                            confirmSaveLicense()
                        }}
                        loading={editing}
                        onChange={()=>{}}
                    >保存</BugsButton>
                </Space>
            </div>
        }
        className="license-edit-modal"
    >
        {/*<Radio.Group options={[
            { label: wordList["上传已有证书"][Context.lan] , value: 'upload' },
            //{ label:wordList["申请免费证书"][Context.lan] , value: 'free' },
        ]} onChange={(e) => {
            setLicenseType(e.target.value)
        }} value={licenseType} optionType="button" />*/}

        {
            licenseType == 1
                ? <div style={{
                    height: 'auto',
                    width: '100%'
                }}>
                    <div style={{

                        width: 'calc(100% - 20px)',
                        marginTop: '20px',
                        marginLeft: '10px',
                        padding: "10px",
                        boxSizing: "border-box",
                        borderRadius: '10px',
                        boxShadow: '0 0 10px rgba(0,0,0,0.1)'
                    }}>
                        <Divider orientation="left">{wordList["域名"][Context.lan]}</Divider>
                        <Select
                            size="large"
                            mode="tags"
                            style={{ width: '100%', borderRadius: '10px' }}
                            placeholder="Tags Mode"
                            onChange={(e) => {

                            }}
                            options={[]}
                        />
                    </div>

                    <div style={{

                        width: 'calc(100% - 20px)',
                        marginTop: '20px',
                        marginLeft: '10px',
                        padding: "10px",
                        boxSizing: "border-box",
                        borderRadius: '10px',
                        boxShadow: '0 0 10px rgba(0,0,0,0.1)'
                    }}>
                        <Divider orientation="left">{wordList["申请邮箱"][Context.lan]}</Divider>
                        <Input style={
                            {
                                width: '100%',
                                borderRadius: '10px'
                            }
                        }></Input>
                    </div>
                    <div className="license-edit-tip-item">
                        {wordList["需要在线申请，使用"][Context.lan]}<a>{wordList["Let's Encrypt 的 HTTP-01 验证方法"][Context.lan]}</a>。{wordList["过期前 30 天自动续期"][Context.lan]}
                    </div>
                </div>
                : null
        }

        {
            licenseType == 0
                ? <div style={{
                    height: 'auto',
                    width: '100%'
                }}>
                    <div style={{

                        width: 'calc(100% - 20px)',
                        marginTop: '20px',
                        marginLeft: '10px',
                        padding: "10px",
                        boxSizing: "border-box",
                        borderRadius: '10px',
                        boxShadow: '0 0 10px rgba(0,0,0,0.1)'
                    }}>
                        <Divider orientation="left">{wordList["证书"][Context.lan]}</Divider>
                        <Input.TextArea 
                            placeholder="请输入证书"
                            rows={5}
                            value={license.license}
                            onChange={(e) => {
                                setLicense({
                                    ...license,
                                    license: e.target.value
                                })
                            }}
                            style={{
                                overflowY:'auto',
                                resize: 'none',
                                borderRadius: '10px'
                            }}></Input.TextArea>
                        <Upload {...uploadProps} accept=".crt,.pem">
                            <Button icon={<UploadOutlined />} style={{
                                marginTop: '10px',
                                borderRadius: '10px'
                            }}>{wordList["上传本地证书"][Context.lan]}(.crt | .pem)</Button>
                        </Upload>
                    </div>

                    <div style={{

                        width: 'calc(100% - 20px)',
                        marginTop: '20px',
                        marginLeft: '10px',
                        padding: "10px",
                        boxSizing: "border-box",
                        borderRadius: '10px',
                        boxShadow: '0 0 10px rgba(0,0,0,0.1)'
                    }}>
                        <Divider orientation="left">{wordList["私钥"][Context.lan]}</Divider>
                        <Input.TextArea rows={5}
                            placeholder={"请输入秘钥"}
                            value={license.key}
                            onChange={(e) => {
                                setLicense({
                                    ...license,
                                    key: e.target.value
                                })
                            }}
                            style={{
                                overflowY:'auto',
                                resize: 'none',
                                borderRadius: '10px'
                            }}></Input.TextArea>
                        <Upload {...uploadProps} accept=".key">
                            <Button icon={<UploadOutlined />} style={{
                                marginTop: '10px',
                                borderRadius: '10px'
                            }}>{wordList["上传本地证书"][Context.lan]}(.key)</Button>
                        </Upload>
                    </div>
                </div>
                : null
        }
    </Modal>
}
export default EditAndAdd