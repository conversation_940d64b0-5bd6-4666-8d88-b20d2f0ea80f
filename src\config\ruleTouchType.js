
const ruleTouchType={
    "src_ip":{
        "eq":"等于",
        "not_eq":"不等于",
        'match':"模糊匹配",
        "cidr":"属于网段",
        'not_cidr':"不属于网段",
        /*"ipGroup":"属于IP组",
        "notIntGroup":"不属于IP组",
        "inPlace":"属于地理位置",
        "notInPlace":"不属于地理位置",*/
    },
    'website':{
        "eq":"等于",
        "not_eq":"不等于",
    },
    'uri':{
        "eq":"等于",
        "not_eq":"不等于",
        'has':'包含',
        'not_has':"不包含",
        'prefix':"前缀关键字",
        're':"正则表达式"
    },
    "uri_no_query":{
        "eq":"等于",
        "not_eq":"不等于",
        'has':'包含',
        'not_has':"不包含",
        'prefix':"前缀关键字",
        're':"正则表达式"
    },
    "host":{
        "eq":"等于",
        "not_eq":"不等于",
        "match":'模糊匹配'
    },
    "req_header":{
        "eq":"等于",
        "not_eq":"不等于",
        'has':'包含',
        'not_has':"不包含",
        're':"正则表达式",
        'exist':"存在",
        'not_exist':'不存在'
    },
    "get_param":{
        "eq":"等于",
        "not_eq":"不等于",
        'has':'包含',
        'not_has':"不包含",
        're':"正则表达式",
        'exist':"存在",
        'not_exist':'不存在'
    },
    "post_param":{
        "eq":"等于",
        "not_eq":"不等于",
        'has':'包含',
        'not_has':"不包含",
        're':"正则表达式",
        'exist':"存在",
        'not_exist':'不存在'
    },
    "req_body":{
        "has":"包含",
        "not_has":"不包含",
        "re":'正则表达式'
    },
    "method":{
        "eq":"等于",
        "not_eq":"不等于",
    }
}

/** */
const touchMethod=[
    "GET",
    "POST",
    "PUT",
    "DELETE",
    "OPTIONS",
    "CONNECT",
    "HEAD",
    "TRACE",
    "PATCH"
]
module.exports={
    ruleTouchType,
    touchMethod
}