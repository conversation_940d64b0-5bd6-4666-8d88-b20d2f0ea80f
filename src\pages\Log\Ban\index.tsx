import { Tag, <PERSON><PERSON>, <PERSON>, DatePicker, Divider, Input, InputNumber, Popover, Radio, Space, Table, TimePicker, Spin, Modal, Collapse, Pagination, message } from "antd"
import "./index.less"
import { useContext, useEffect, useState } from "react"
import moment from "moment";
import config from "../../../config";
import wordList from "../../../config/wordList";
import type { ColumnsType } from 'antd/es/table';
import ProviderContext from "../../providerContext";
import api from "../../../api";
import CustomTable from "../../../components/CustomTable";
import { CaretRightFilled } from "@ant-design/icons";
import CollapsePanel from "antd/lib/collapse/CollapsePanel";
import LogDetail from "../Attack/components/LogDetail";
import CustomCard from "../../../components/CustomCard";
import { title } from "process";
import BugsLinkButton from "../../../components/BugsLinkButton";
const LogBan = (props: any) => {
    const Context = useContext(ProviderContext)

    const [logData, setLogData] = useState([
        
    ])
    const [attackRecord, setAttackRecord] = useState<any>(
        {
            show: false,
            nowSite: "",
            data: [

            ]
        })
    const [loadingLog, setLoadingLog] = useState(false)
    const [detailLoading, setDetailLoading] = useState(false)
    const [detailPageInfo, setDetailPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const getDetail = (page = 1, pageSize = 10, options: any = {}) => {
        setDetailLoading(true)
        api.record.getRecordList({
            page,
            page_size: pageSize,
            sort: "-time",
            ...options
        })
            .then(res => {
                setDetailLoading(false)
                if (res?.data?.data) {
                    setAttackRecord({
                        show: true,
                        nowSite: options.site,
                        data: res.data.data.rows
                    })
                    setDetailPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    const logColumns:ColumnsType & any = [
        {
            title: wordList["源IP"][Context.lan],
            key: 'src_ip',
            dataIndex: 'src_ip',
            width: 150,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: any) => {
                return <div style={{
                    height: '30px',
                    display: 'flex',
                    width: '100%',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <div>
                        {
                            record?.src_ip || '-'
                        }
                    </div>

                </div>
            }
        },
        {
            title:wordList['封禁期间尝试攻击次数'][Context.lan],
            dataIndex: 'count',
            key: 'count',
            align:'center',
            width: 100,
           
        },
        {
            title: wordList['封禁时间'][Context.lan],
            key: 'ban_time',
            dataIndex: 'ban_time',
            width: 100,
            onHeaderCell: (cell) => {
                return {
                    ...cell,
                    style: {
                        textAlign: "center"
                    }
                }
            },
            render: (_: any, record: any, index: number) => {
                return <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    gap: '5px',

                }}>
                    {
                        moment(record.ban_time || '').format('YYYY/MM/DD HH:mm:ss')
                    }
                </div>
            }
        },
        {
            title: wordList['封禁时长'][Context.lan],
            key: 'ban_seconds',
            align:'center',
            dataIndex: 'ban_seconds',
            width: 100,
            render:(ban_seconds)=>{
                  // 计算分钟数，使用 Math.floor 向下取整
                const minutes = Math.floor(ban_seconds / 60);
                // 计算剩余的秒数
                const remainingSeconds = ban_seconds % 60;
                // 将剩余秒数格式化为至少两位数字
                const formattedSeconds = String(remainingSeconds).padStart(2, '0');
                // 返回格式化后的字符串
                return `${minutes} 分 ${formattedSeconds} 秒`;
            }

        },
        {
            title:"",
            dataIndex:"operation",
            key:"operation",
            align:"center",
            width:50,
            render:(_,record:any)=>{
                return <BugsLinkButton
                    onClick={()=>{
                        Modal.confirm({
                            title:wordList['操作确认'][Context.lan],
                            cancelButtonProps:{
                                style:{
                                    color:"#cc1212",
                                    border:"none"
                                }
                            },
                            content:wordList['确认解除该IP的封禁状态'][Context.lan],
                            onOk:(resolve)=>{
                                return new Promise<boolean>((resolve, reject) => {
                                    api.record.unBanIp({
                                        src_ip:record.src_ip
                                    })
                                    .then(res=>{
                                        if(res?.data?.data){
                                            message.success(wordList['操作成功'][Context.lan])
                                            let page=logpPageInfo.page
                                            let pageSize=logpPageInfo.page_size
                                            if(logData.length==1){
                                                page-=1
                                            }
                                            if(page<1){
                                                page=1
                                            }
                                            getLogRecord(page,pageSize)
                                            resolve(false)
                                        }else{
                                            reject(false)
                                        }
                                    })
                                })
                            }
                        })
                    }}
                >{ wordList['解除封禁'][Context.lan]}</BugsLinkButton>
            }
        }
    ]
    const [logpPageInfo, setLogPageInfo] = useState({
        "page": 1,
        "page_size": 10,
        "total": 5,
        "page_count": 1
    })
    const getLogRecord = (page = 1, pageSize = 10, options = {}) => {
        setLoadingLog(true)
        api.record.getBanIpList({
            page,
            page_size: pageSize,
        })
            .then(res => {
                setLoadingLog(false)
                if (res?.data?.data) {
                    setLogData(res.data.data.rows)
                    setLogPageInfo({
                        ...res.data.data.page_info
                    })
                }
            })
    }
    useEffect(() => {
        getLogRecord()
    }, [])
    return <>

        <CustomCard title={
            wordList['已禁用监察者IP'][Context.lan]
        }

            extra={
                null
            }
            style={{
                borderRadius: '10px',
                width: '100%',
                paddingLeft: 0,
                paddingRight: 0,
                height: '100%',
            }}>
            <div
                className="search-bar"
                style={{
                    alignItems: 'center',
                    display: 'flex',
                    flexWrap: 'wrap',
                    width: '100%',
                    height: 'auto',
                    gap: '10px'
                }}>

            </div>
            <Spin spinning={loadingLog}>
                <div
                    className="log-table-container">
                    <CustomTable columns={logColumns}
                        dataSource={logData}
                        useBorder={true}
                        pagination={{
                            pageSize: logpPageInfo.page_size,
                            current: logpPageInfo.page,
                            total: logpPageInfo.total
                        }}

                        loading={false}
                        onChange={(e) => {

                            getLogRecord(e.current, e.pageSize, {})
                        }}
                        scroll={{ x: 'max-content' }} // 加上这条  横向滚动  支持此属性的浏览器内容就不会换行了
                        size={'small'}
                    ></CustomTable>

                </div>
            </Spin>

        </CustomCard>
    </>
}

export default LogBan