const wordList = {
    "总览": {
        "cn": "总览",
        "en": "Overview"
    },

    "最近攻击时间": {
    "cn": "最近攻击时间",
    "en": "Recent attack time"
    },
    "系统内置规则": {
    "cn": "系统内置规则",
    "en": "System-built-in rules"
    },
        
    "数据看板": {
        "cn": "数据看板",
        "en": "Data Dashboard"
    },
    "防监察站点": {
        "cn": "防监察站点",
        "en": "Anti - Inspection Site"
    },
    "防监察日志": {
        "cn": "防监察日志",
        "en": "Anti - Inspection Log"
    },
    "监察者日志": {
        "cn": "监察者日志",
        "en": "Inspector's Log"
    },
    "监察阻断": {
        "cn": "监察阻断",
        "en": "Anti - Inspection Black Room"
    },
    "防监察配置": {
        "cn": "防监察配置",
        "en": "Anti - Inspection Configuration"
    },
    "主页": {
        "cn": "主页",
        "en": "Home"
    },
    "访问概况": {
        "cn": "访问概况",
        "en": "Visit Overview"
    },
    "独立访客": {
        "cn": "独立访客",
        "en": "Unique Visitors"
    },
    "独立IP": {
        "cn": "独立IP",
        "en": "Unique IPs"
    },
    "来源国家": {
        "cn": "来源国家",
        "en": "Source Country"
    },
    "访问次数": {
        "cn": "访问次数",
        "en": "Visit Count"
    },
    "访问&攻击 IP 前十": {
        "cn": "访问&攻击 IP 前十",
        "en": "Top 10 Visit & Attack IPs"
    },
    "拦截&访问分析": {
        "cn": "拦截&访问分析",
        "en": "Blocking & Visit Analysis"
    },
    "受访页面前五": {
        "cn": "受访页面前五",
        "en": "Top 5 Visited Pages"
    },
    "响应分析": {
        "cn": "响应分析",
        "en": "Response Analysis"
    },
    "受访域名": {
        "cn": "受访域名",
        "en": "Visited Domain"
    },
    "受访页面": {
        "cn": "受访页面",
        "en": "Visited Pages"
    },
    "受攻击站点": {
        "cn": "受攻击站点",
        "en": "Attacked Sites"
    },
    "站点类型": {
        "cn": "站点类型",
        "en": "Site Type"
    },
    "站点已删除": {
        "cn": "站点已删除",
        "en": "Site Deleted"
    },
    "确认删除此站点": {
        "cn": "确认删除此站点",
        "en": "Confirm Deletion of This Site"
    },
    "删除节点": {
        "cn": "删除节点",
        "en": "Delete Node"
    },
    "修改失败": {
        "cn": "修改失败",
        "en": "Modification Failed"
    },
    "反向代理": {
        "cn": "反向代理",
        "en": "Reverse Proxy"
    },
    "站点描述": {
        "cn": "站点描述",
        "en": "Site Description"
    },
    "请输入描述": {
        "cn": "请输入描述",
        "en": "Please Enter Description"
    },
    "匹配方式": {
        "cn": "匹配方式",
        "en": "Matching Method"
    },
    "确认关闭该站点防护": {
        "cn": "确认关闭该站点防护",
        "en": "Confirm to Disable Protection for This Site"
    },
    "确认开启该站点防护": {
        "cn": "确认开启该站点防护",
        "en": "Confirm to Enable Protection for This Site"
    },
    "搜索攻击URL": {
        "cn": "搜索攻击URL",
        "en": "Search Attack URL"
    },
    "搜索攻击PATH":{
        "cn":'搜索攻击PATH',
        'en':"Search Attack PATH"
    },
    "规则类型": {
        "cn": "规则类型",
        "en": "Rule Type"
    },
    "已启用": {
        "cn": "已启用",
        "en": "Enabled"
    },
    "未启用": {
        "cn": "未启用",
        "en": "Disabled"
    },
    "确定将此站点模式更改为": {
        "cn": "确定将此站点模式更改为",
        "en": "Are you sure you want to change this site's mode to "
    },
    "站点分析": {
        "cn": "站点分析",
        "en": "Site Analysis"
    },
    "站点列表": {
        "cn": "站点列表",
        "en": "Site List"
    },
    "非攻击": {
        cn: "非攻击",
        en: "Non-attack"
    },
    "扫描器": {
        "cn": "扫描器",
        "en": "Scanner"
    },
    "CIDR": {
        "en": "CIDR",
        "cn": "CIDR"
    },
    "匹配规则": {
        "cn": "匹配规则",
        "en": "Matching Rules"
    },
    "请输入站点名称": {
        "cn": "请输入站点名称",
        "en": "Please Enter Site Name"
    },
    "日志记录": {
        "cn": "日志记录",
        "en": "Log Recording"
    },
    "站点状态已修改": {
        "cn": "站点状态已修改",
        "en": "Site Status Modified"
    },
    "节点": {
        "cn": "节点",
        "en": "Nodes"
    },
    "运行情况": {
        "cn": "运行情况",
        "en": "Running Status"
    },
    "访问情况": {
        "cn": "访问情况",
        "en": "Visit Status"
    },
    "拦截情况": {
        "cn": "拦截情况",
        "en": "Blocking Status"
    },
    "今日": {
        "cn": "今日",
        "en": "Today"
    },
    "近7天": {
        "cn": "近7天",
        "en": "7 Days"
    },
    "近30天": {
        "cn": "近30天",
        "en": "30 Days"
    },
    "攻击概览": {
        "cn": "攻击概览",
        "en": "Attack Overview"
    },
    "星期一": {
        "cn": "星期一",
        "en": "Monday"
    },
    "星期二": {
        "cn": "星期二",
        "en": "Tuesday"
    },
    "星期三": {
        "cn": "星期三",
        "en": "Wednesday"
    },
    "星期四": {
        "cn": "星期四",
        "en": "Thursday"
    },
    "星期五": {
        "cn": "星期五",
        "en": "Friday"
    },
    "星期六": {
        "cn": "星期六",
        "en": "Saturday"
    },
    "星期天": {
        "cn": "星期天",
        "en": "Sunday"
    },
    "星期日": {
        "cn": "星期日",
        "en": "Sunday"
    },
    "防护站点": {
        "cn": "防护站点",
        "en": "Protection Site"
    },
    "站点列表": {
        "cn": "站点列表",
        "en": "Site Management"
    },
    "端口": {
        "cn": "端口",
        "en": "port"
    },
    "添加站点": {
        "cn": "添加站点",
        "en": "Add Site"
    },
    "添加监听端口": {
        "cn": "添加监听端口",
        "en": "Add Listening Port"
    },
    "添加": {
        "cn": "添加",
        "en": "Add "
    },
    "站点": {
        "cn": "站点",
        "en": "Site"
    },
    "监听端口": {
        "cn": "监听端口",
        "en": "Listening Port"
    },
    "运行模式": {
        "cn": "运行模式",
        "en": "Operating Mode"
    },
    "高级防护": {
        "cn": "高级防护",
        "en": "Advanced Protection"
    },
    "今日防护情况": {
        "cn": "今日防护情况",
        "en": "Today's Protection Status"
    },
    "防护": {
        "cn": "防护",
        "en": "Protection"
    },
    "观察": {
        "cn": "观察",
        "en": "Observation"
    },
    "更多设置请前往": {
        "cn": "更多设置请前往",
        "en": "For More Settings, Please Go to"
    },
    "站点详情页": {
        "cn": "站点详情页",
        "en": "Site Details Page"
    },
    "编辑上游服务器": {
        cn: '编辑上游服务器',
        en: 'Edit Upstream Server'
    },
    "确认": {
        "cn": "确认",
        "en": "Confirm"
    },
    '新页面打开': {
        cn: '新页面打开',
        en: 'Open As New Page'
    },
    "IP地址": {
        "cn": 'IP地址',
        "en": "IP"
    },
    "进行设置": {
        "cn": "进行设置",
        "en": "Proceed with Settings"
    },
    "启用此站点防护": {
        "cn": "启用此站点防护",
        "en": "Enable Protection for This Site"
    },
    "维护": {
        "cn": "维护",
        "en": "Maintenance"
    },
    "CC防护": {
        "cn": "CC防护",
        "en": "CC Protection"
    },
    "BOT防护": {
        "cn": "BOT防护",
        "en": "BOT Protection"
    },
    "身份防护": {
        "cn": "身份防护",
        "en": "Identity Protection"
    },
    "拦截": {
        "cn": "拦截",
        "en": "Intercepted"
    },
    "请求": {
        "cn": "请求",
        "en": "Request"
    },
    "编辑": {
        "cn": "编辑",
        "en": "Edit"
    },
    "修改模式": {
        "cn": "修改模式",
        "en": "Modify Mode"
    },
    "防护模式": {
        "cn": "防护模式",
        "en": "Protection Mode"
    },
    "观察模式": {
        "cn": "观察模式",
        "en": "Observation Mode"
    },
    "维护模式": {
        "cn": "维护模式",
        "en": "Maintenance Mode"
    },
    "启用上游服务器健康检查": {
        "cn": "启用上游服务器健康检查",
        "en": "Enable Upstream Server Health Check"
    },
    "提交": {
        "cn": "提交",
        "en": "Submit"
    },
    "取消": {
        "cn": "取消",
        "en": "Cancel"
    },
    "删除": {
        "cn": "删除",
        "en": "Delete"
    },
    "负载均衡": {
        "cn": "负载均衡",
        "en": " Load Balancing"
    },
    "添加上游服务器": {
        "cn": "添加上游服务器",
        "en": "Add Upstream Server"
    },
    "确认删除此上游服务器": {
        "cn": "确认删除此上游服务器",
        "en": "Confirm Deletion of This Upstream Server"
    },
    "备注": {
        "cn": "备注",
        "en": "Remark"
    },
    '信任的代理IP':{
        cn:'信任的代理IP',
        en:'Trusted Proxy IP'
    },
    '源IP配置':{
        'cn':'源IP配置',
        'en':'Source IP'
    },
    "攻击频率": {
        "cn": "攻击频率",
        "en": "Attack Frequency"
    },
    "修改成功": {
        "cn": "修改成功",
        "en": "Modification Successful"
    },
    "输入服务域名匹配规则": {
        "cn": "输入服务域名匹配规则",
        "en": "Enter Service Domain Matching Rules"
    },
    "存在未填写端口的项，无法新增": {
        "cn": "存在未填写端口的项，无法新增",
        "en": "There are items with missing ports, unable to add"
    },
    "添加一个监听端口": {
        "cn": "添加一个监听端口",
        "en": "Add a Listening Port"
    },
    "未命名": {
        "cn": "未命名",
        "en": "Unnamed"
    },
    "未配置匹配方式": {
        "cn": "未配置匹配方式",
        "en": "Matching Method Not Configured"
    },
    "通配所有域名": {
        "cn": "通配所有域名",
        "en": "Wildcard for All Domains"
    },
    "证书": {
        "cn": "证书",
        "en": "Certificate"
    },
    "证书管理": {
        "cn": "证书管理",
        "en": "Certificate Management"
    },
    "颁发机构": {
        "cn": "颁发机构",
        "en": "Issuer"
    },
    "有效期至": {
        "cn": "有效期至",
        "en": "Valid Until"
    },
    "证书列表": {
        "cn": "证书列表",
        "en": "Certificate List"
    },
    "类型": {
        "cn": "类型",
        "en": "Type"
    },
    "域名": {
        "cn": "域名",
        "en": "Domain Name"
    },
    "详情": {
        "cn": "详情",
        "en": "Details"
    },
    "续期": {
        "cn": "续期",
        "en": "Renew"
    },
    "添加证书": {
        "cn": "添加证书",
        "en": "Add Certificate"
    },
    "WAF控制台证书": {
        "cn": "WAF控制台证书",
        "en": "WAF Console Certificate"
    },
    "全局配置": {
        "cn": "全局配置",
        "en": "Global Configuration"
    },
    "源 IP 获取方式": {
        "cn": "源 IP 获取方式",
        "en": "Source IP Retrieval Method"
    },
    "从网络连接中获取": {
        "cn": "从网络连接中获取",
        "en": "Retrieve from Network Connection"
    },
    "取 X-Forwarded-For 中上一级代理的地址": {
        "cn": "取 X-Forwarded-For 中上一级代理的地址",
        "en": "Retrieve Address from X-Forwarded-For of Previous Proxy"
    },
    "取 X-Forwarded-For 中上上一级代理的地址": {
        "cn": "取 X-Forwarded-For 中上上一级代理的地址",
        "en": "Retrieve Address from X-Forwarded-For of Second-Previous Proxy"
    },
    "取 X-Forwarded-For 中上上上一级代理的地址": {
        "cn": "取 X-Forwarded-For 中上上上一级代理的地址",
        "en": "Retrieve Address from X-Forwarded-For of Third-Previous Proxy"
    },
    "从 HTTP Header 中获取": {
        "cn": "从 HTTP Header 中获取",
        "en": "Retrieve from HTTP Header"
    },
    "其他高级配置": {
        "cn": "其他高级配置",
        "en": "Other Advanced Configurations"
    },
    "监听 IPv6 地址": {
        "cn": "监听 IPv6 地址",
        "en": "Listen on IPv6 Address"
    },
    "启用 HTTP/1.0": {
        "cn": "启用 HTTP/1.0",
        "en": "Enable HTTP/1.0"
    },
    "启用 HTTP/2": {
        "cn": "启用 HTTP/2",
        "en": "Enable HTTP/2"
    },
    "HTTP 自动跳转到 HTTPS": {
        "cn": "HTTP 自动跳转到 HTTPS",
        "en": "Automatic HTTP to HTTPS Redirect"
    },
    "启用 HSTS": {
        "cn": "启用 HSTS",
        "en": "Enable HSTS"
    },
    "代理时修改请求中的 Host 头": {
        "cn": "代理时修改请求中的 Host 头",
        "en": "Modify Host Header in Proxy Requests"
    },
    "为上游服务器传递 X-Forwarded-Host、 X-Forwarded-Proto": {
        "cn": "为上游服务器传递 X-Forwarded-Host、 X-Forwarded-Proto",
        "en": "Pass X-Forwarded-Host and X-Forwarded-Proto to Upstream Server"
    },
    "清空并重写 X-Forwarded-For": {
        "cn": "清空并重写 X-Forwarded-For",
        "en": "Clear and Rewrite X-Forwarded-For"
    },
    "支持 Gzip 压缩": {
        "cn": "支持 Gzip 压缩",
        "en": "Support Gzip Compression"
    },
    "支持 Brotli 压缩": {
        "cn": "支持 Brotli 压缩",
        "en": "Support Brotli Compression"
    },
    "站点不存在时返回内置证书": {
        "cn": "站点不存在时返回内置证书",
        "en": "Return Built-in Certificate When Site Does Not Exist"
    },
    "SSL合规配置": {
        "cn": "SSL合规配置",
        "en": "SSL Compliance Configuration"
    },
    "SSL协议版本": {
        "cn": "SSL协议版本",
        "en": "SSL Protocol Version"
    },
    "SSL Chipers": {
        "cn": "SSL Chipers",
        "en": "SSL Ciphers"
    },
    "HTTP Header 操作": {
        "cn": "HTTP Header 操作",
        "en": "HTTP Header Operations"
    },
    "操作对象": {
        "cn": "操作对象",
        "en": "Target Object"
    },
    "请求头": {
        "cn": "请求头",
        "en": "Request Header"
    },
    "响应头": {
        "cn": "响应头",
        "en": "Response Header"
    },
    "操作": {
        "cn": "操作",
        "en": "Action"
    },
    "设置": {
        "cn": "设置",
        "en": "Settings"
    },
    "添加一项Header操作": {
        "cn": "添加一项Header操作",
        "en": "Add a Header Operation"
    },
    "防监察日志": {
        "cn": "防监察日志",
        "en": "Protection Logs"
    },
    "攻击检测": {
        "cn": "攻击检测",
        "en": "Attack Detection"
    },
    "攻击类型": {
        "cn": "攻击类型",
        "en": "Attack Type"
    },
    "攻击地址": {
        "cn": "攻击地址",
        "en": "Attack Address"
    },
    "已拦截": {
        "cn": "已拦截",
        "en": "Intercepted"
    },
    "未拦截": {
        "cn": "未拦截",
        "en": "Not Intercepted"
    },
    "放行": {
        "cn": "放行",
        "en": "PASSED"
    },
    "攻击事件": {
        "cn": "攻击事件",
        "en": "Attack Events"
    },
    "攻击日志": {
        "cn": "攻击日志",
        "en": "Attack Logs"
    },
    "封禁": {
        "cn": "封禁",
        "en": "Ban"
    },
    "直接封禁": {
        "cn": "直接封禁",
        "en": "Direct Ban"
    },
    "攻击IP": {
        "cn": "攻击IP",
        "en": "Attack IP"
    },
    "搜索攻击开始时间": {
        "cn": "搜索攻击开始时间",
        "en": "Search Attack Start Time"
    },
    "搜索攻击结束时间": {
        "cn": "搜索攻击结束时间",
        "en": "Search Attack End Time"
    },
    "搜索攻击类型": {
        "cn": "搜索攻击类型",
        "en": "Search Attack Type"
    },
    "结束时间": {
        "cn": "结束时间",
        "en": "End Time"
    },
    "搜索攻击IP": {
        "cn": "搜索攻击IP",
        "en": "Search Attack IP"
    },
    "路径": {
        "cn": "路径",
        "en": "Path"
    },
    "搜索日志ID": {
        "cn": "搜索日志ID",
        "en": "Search Log ID"
    },
    "搜索攻击路径": {
        "cn": "搜索攻击路径",
        "en": "Search Attack Path"
    },
    "搜索域名": {
        "cn": "搜索域名",
        "en": "Search Domain"
    },
    "搜索攻击端口": {
        "cn": "搜索攻击端口",
        "en": "Search Attack Port"
    },
    "筛选内容": {
        "cn": "筛选内容",
        "en": "Filter Content"
    },
    "更多": {
        "cn": "更多",
        "en": "More"
    },
    "攻击时间": {
        "cn": "攻击时间",
        "en": "Attack Time"
    },
    "添加成功": {
        "cn": "添加成功",
        "en": "Added Successfully"
    },
    "暂无自定义规则": {
        "cn": "暂无自定义规则",
        "en": "No Custom Rules Available"
    },
    "请求报文": {
        "cn": "请求报文",
        "en": "Request Message"
    },
    "保存失败，请填写规则名称": {
        "cn": "保存失败，请填写规则名称",
        "en": "Save Failed, Please Enter Rule Name"
    },
    "未选择匹配方式与匹配目标": {
        "cn": "未选择匹配方式与匹配目标",
        "en": "Matching Method and Target Not Selected"
    },
    "暂无记录内容": {
        "cn": "暂无记录内容",
        "en": "No Record Content"
    },
    "误报反馈": {
        "cn": "误报反馈",
        "en": "False Positive Feedback"
    },
    "复制cURL": {
        "cn": "复制cURL",
        "en": "Copy cURL"
    },
    "响应报文": {
        "cn": "响应报文",
        "en": "Response Message"
    },
    "无响应报文": {
        "cn": "无响应报文",
        "en": "No Response Message"
    },
    "开始日期": {
        "cn": "开始日期",
        "en": "Start Date"
    },
    "结束日期": {
        "cn": "结束日期",
        "en": "End Date"
    },
    "源IP": {
        "cn": "源IP",
        "en": "Source IP"
    },
    "目标IP": {
        "cn": "目标IP",
        "en": "Target IP"
    },
    "目标端口": {
        "cn": "目标端口",
        "en": "Target Port"
    },
    "网站": {
        "cn": "网站",
        "en": "Website"
    },
    "攻击次数": {
        "cn": "攻击次数",
        "en": "Attack Count"
    },
    "持续时间": {
        "cn": "持续时间",
        "en": "Duration"
    },
    "后端端口": {
        "cn": "后端端口",
        "en": "Backend Port"
    },
    "上游节点": {
        "cn": "上游节点",
        "en": "Upstream Node"
    },
    "创建时间": {
        "cn": "创建时间",
        "en": "Creation Time"
    },
    "站点名称": {
        "cn": "站点名称",
        "en": "Site Name"
    },
    "未知": {
        "cn": "未知",
        "en": "Unknown"
    },
    "攻击来源": {
        "cn": "攻击来源",
        "en": "Attack Source"
    },
    "命中规则": {
        "cn": "命中规则",
        "en": "Matched Rule"
    },
    "受击站点": {
        "cn": "受击站点",
        "en": "Hit Site"
    },
    "删除站点": {
        "cn": "删除站点",
        "en": "Delete Site"
    },
    "站点详情": {
        "cn": "站点详情",
        "en": "Site Details"
    },
    "编辑站点": {
        "cn": "编辑站点",
        "en": "Edit Site"
    },
    "访问IP 前十": {
        "cn": "访问IP 前十",
        "en": "Top 10 Visit IPs"
    },
    "开始时间": {
        "cn": "开始时间",
        "en": "Start Time"
    },
    "归属地": {
        "cn": "归属地",
        "en": "Location"
    },
    "排名": {
        "cn": "排名",
        "en": "Rank"
    },
    "访问IP": {
        "cn": "访问IP",
        "en": "Visitor IP"
    },
    "频率限制": {
        "cn": "频率限制",
        "en": "Rate Limiting"
    },
    "监察阻断": {
        "cn": "监察阻断",
        "en": "Rate Limiting"
    },
    "系统设置": {
        cn: "系统设置",
        en: "Setting"
    },
    "监察阻断设置": {
        "cn": "监察阻断设置",
        "en": "Rate Limiting Setting"
    },
    "拦截次数": {
        "cn": "拦截次数",
        "en": "Interception Count"
    },
    "触发时间": {
        "cn": "触发时间",
        "en": "Trigger Time"
    },
    "解封": {
        "cn": "解封",
        "en": "Unblock"
    },
    "原因": {
        "cn": "原因",
        "en": "Reason"
    },
    "动作": {
        "cn": "动作",
        "en": "Action"
    },
    "人机验证": {
        "cn": "人机验证",
        "en": "CAPTCHA"
    },
    "触发": {
        "cn": "触发",
        "en": "Trigger"
    },
    "通过": {
        "cn": "通过",
        "en": "Pass"
    },
    "身份验证": {
        "cn": "身份验证",
        "en": "Authentication"
    },
    "用户账号": {
        "cn": "用户账号",
        "en": "User Account"
    },
    "认证结果": {
        "cn": "认证结果",
        "en": "Authentication Result"
    },
    "等候室": {
        "cn": "等候室",
        "en": "Waiting Room"
    },
    "允许活跃用户": {
        "cn": "允许活跃用户",
        "en": "Allowed Active Users"
    },
    "总等待人数": {
        "cn": "总等待人数",
        "en": "Total Waiting Users"
    },
    "峰值等待人数": {
        "cn": "峰值等待人数",
        "en": "Peak Waiting Users"
    },
    "平均等待时间": {
        "cn": "平均等待时间",
        "en": "Average Wait Time"
    },
    "跳出率": {
        "cn": "跳出率",
        "en": "Bounce Rate"
    },
    "防监察配置": {
        "cn": "防监察配置",
        "en": "Protection Configuration"
    },
    "高频访问限制": {
        "cn": "高频访问限制",
        "en": "High-Frequency Access Limit"
    },
    "高频攻击限制": {
        "cn": "高频攻击限制",
        "en": "High-Frequency Attack Limit"
    },
    "高频错误限制": {
        "cn": "高频错误限制",
        "en": "High-Frequency Error Limit"
    },
    "时间": {
        "cn": "时间",
        "en": "Time"
    },
    "经过时间": {
        "cn": "经过时间",
        "en": "Elapsed Time"
    },
    "请求次数达到": {
        "cn": "请求次数达到",
        "en": "Request Count Reached"
    },
    "处理方式": {
        "cn": "处理方式",
        "en": "Handling Method"
    },
    "封禁": {
        "cn": "封禁",
        "en": "Account Ban"
    },
    "排除 Content-Type": {
        "cn": "排除 Content-Type",
        "en": "Exclude Content-Type"
    },
    "攻击阻断次数达到": {
        "cn": "攻击阻断次数达到",
        "en": "Attack Block Count Reached"
    },
    "错误次数达到": {
        "cn": "错误次数达到",
        "en": "Error Count Reached"
    },
    "状态码": {
        "cn": "状态码",
        "en": "Status Code"
    },
    "自定义规则": {
        "cn": "自定义规则",
        "en": "Custom Rules"
    },
    "全部": {
        "cn": "全部",
        "en": "All"
    },
    "白名单": {
        "cn": "白名单",
        "en": "Whitelist"
    },
    "黑名单": {
        "cn": "黑名单",
        "en": "Blacklist"
    },
    "启用": {
        "cn": "启用",
        "en": "Enable"
    },
    "禁用": {
        "cn": "禁用",
        "en": "Disable"
    },
    "今日命中": {
        "cn": "今日命中",
        "en": "Hits Today"
    },
    "命中": {
        "cn": "命中",
        "en": "Hits"
    },
    "在线规则": {
        "cn": "在线规则",
        "en": "Online Rules"
    },
    "更新时间": {
        "cn": "更新时间",
        "en": "Update Time"
    },
    "订阅在线规则": {
        "cn": "订阅在线规则",
        "en": "Subscribe to Online Rules"
    },
    "添加规则": {
        "cn": "添加规则",
        "en": "Add Rule"
    },
    "防护模块": {
        "cn": "防护模块",
        "en": "Protection Module"
    },
    "防护模块配置": {
        "cn": "防护模块配置",
        "en": "Protection Module Configuration"
    },
    "SQL注入检测": {
        "cn": "SQL注入检测",
        "en": "SQL Injection Detection"
    },

    "已禁用监察者IP": {
        "cn": "已禁用监察者IP",
        "en": "Disabled Inspector IP"
    },
    "用户ID": {
        "cn": "用户ID",
        "en": "User ID"
    },
    "用户名": {
        "cn": "用户名",
        "en": "Username"
    },
    "角色": {
        "cn": "角色",
        "en": "Role"
    },
    "重置密码": {
        "cn": "重置密码",
        "en": "Reset Password"
    },
    "请填写信息": {
        "cn": "请填写信息",
        "en": "Please fill in the information"
    },
    "两次输入的密码不一致": {
        "cn": "两次输入的密码不一致",
        "en": "The passwords entered twice do not match"
    },
    "密码": {
        "cn": "密码",
        "en": "Password"
    },
    "确认密码": {
        "cn": "确认密码",
        "en": "Confirm Password"
    },
    "编辑备注": {
        "cn": "编辑备注",
        "en": "Edit Remarks"
    },
    "用户备注": {
        "cn": "用户备注",
        "en": "User Remarks"
    },
    "请输入用户备注": {
        "cn": "请输入用户备注",
        "en": "Please enter user remarks"
    },
    "密码不能少于8位": {
        "cn": "密码不能少于8位",
        "en": "Password cannot be less than 8 characters"
    },
    "重置成功": {
        "cn": "重置成功",
        "en": "Reset successfully"
    },
    "请输入重置后的密码": {
        "cn": "请输入重置后的密码",
        "en": "Please enter the reset password"
    },
    "确认删除用户": {
        "cn": "确认删除用户",
        "en": "Confirm user deletion"
    },
    "攻击记录": {
        "cn": "攻击记录",
        "en": "Attack Record"
    },
    "封禁期间尝试攻击次数": {
        "cn": "封禁期间尝试攻击次数",
        "en": "Number of attempted attacks during the ban period"
    },
    "封禁时间": {
        "cn": "封禁时间",
        "en": "Ban Time"
    },
    "封禁时长": {
        "cn": "封禁时长",
        "en": "Ban Duration"
    },
    '操作成功':{
        en:"SUCCESS",
        cn:"操作成功"
    },
    "确认解除该IP的封禁状态": {
        "cn": "确认解除该IP的封禁状态",
        "en": "Confirm to解除 the ban status of this IP"
    },
    "解除封禁": {
        "cn": "解除封禁",
        "en": "Lift the ban"
    },
    "拦截监察数": {
        "cn": "拦截监察数",
        "en": "Intercepted inspections"
    },
    '实时监察拦截':{
        cn:'实时监察拦截',
        en:"Monitor Interception"
    },
    "拦截攻击数": {
        "cn": "拦截攻击数",
        "en": "Intercepted attacks"
    },
    "访问次数": {
        "cn": "访问次数",
        "en": "Visits"
    },
    "监察攻击情况": {
        "cn": "监察攻击情况",
        "en": "Monitor situation"
    },
    "全部": {
        "cn": "全部",
        "en": "All"
    },
    "删除用户": {
        "cn": "删除用户",
        "en": "Delete user"
    },
    "删除成功": {
        "cn": "删除成功",
        "en": "Delete successfully"
    },
    "添加账号": {
        "cn": "添加账号",
        "en": "Add account"
    },
    
    "SQL注入": {
        "cn": "SQL注入",
        "en": "SQL Injection"
    },
    "文件上传": {
        "cn": "文件上传",
        "en": "File Upload"
    },
    "XSS检测": {
        "cn": "XSS检测",
        "en": "XSS Detection"
    },
    "XSS攻击": {
        cn: 'XSS攻击',
        en: "XSS"
    },
    "OWASP": {
        "cn": "OWASP",
        "en": "OWASP"
    },
    "文件上传检测": {
        "cn": "文件上传检测",
        "en": "File Upload Detection"
    },
    "文件包含检测": {
        "cn": "文件包含检测",
        "en": "File Inclusion Detection"
    },
    "命令注入检测": {
        "cn": "命令注入检测",
        "en": "Command Injection Detection"
    },
    "JAVA代码注入检测": {
        "cn": "JAVA代码注入检测",
        "en": "Java Code Injection Detection"
    },
    "JAVA反序列化检测": {
        "cn": "JAVA反序列化检测",
        "en": "Java Deserialization Detection"
    },
    "PHP反序列化检测": {
        "cn": "PHP反序列化检测",
        "en": "PHP Deserialization Detection"
    },
    "PHP代码注入检测": {
        "cn": "PHP代码注入检测",
        "en": "PHP Code Injection Detection"
    },
    "ASP代码注入检测": {
        "cn": "ASP代码注入检测",
        "en": "ASP Code Injection Detection"
    },
    "模板注入检测": {
        "cn": "模板注入检测",
        "en": "Template Injection Detection"
    },
    "CSRF检测": {
        "cn": "CSRF检测",
        "en": "CSRF Detection"
    },
    "SSRF检测": {
        "cn": "SSRF检测",
        "en": "SSRF Detection"
    },
    "畸形HTTP协议检测": {
        "cn": "畸形HTTP协议检测",
        "en": "Malformed HTTP Protocol Detection"
    },
    "仅观察": {
        "cn": "仅观察",
        "en": "Observation Only"
    },
    "开启拦截": {
        "cn": "开启拦截",
        "en": "Enable Interception"
    },
    "请输入域名匹配规则": {
        "cn": "请输入域名匹配规则",
        "en": "Please Enter Domain Matching Rules"
    },
    "确认删除此端口": {
        "cn": "确认删除此端口",
        "en": "Confirm Deletion of This Port"
    },
    "请输入服务端口域名": {
        "cn": "请输入服务端口域名",
        "en": "Please Enter Service Port Domain"
    },
    "服务端口": {
        "cn": "服务端口",
        "en": "Service Port"
    },
    "确认删除此节点": {
        "cn": "确认删除此节点",
        "en": "Confirm Deletion of This Node"
    },
    "存在未填的项，无法新增": {
        "cn": "存在未填的项，无法新增",
        "en": "There are incomplete fields, unable to add"
    },
    "创建成功": {
        "cn": "创建成功",
        "en": "Created Successfully"
    },
    "平衡防护": {
        "cn": "平衡防护",
        "en": "Balanced Protection"
    },
    "高强度防护": {
        "cn": "高强度防护",
        "en": "High-Strength Protection"
    },
    "通用配置": {
        "cn": "通用配置",
        "en": "General Configuration"
    },
    "IP组": {
        "cn": "IP组",
        "en": "IP Group"
    },
    "请输入IP组名称": {
        "cn": "请输入IP组名称",
        "en": "Enter IP Group Name"
    },
    "数据来源": {
        "cn": "数据来源",
        "en": "Data Source"
    },
    "文本填写": {
        "cn": "文本填写",
        "en": "Text Entry"
    },
    "在线订阅": {
        "cn": "在线订阅",
        "en": "Online Subscription"
    },
    "订阅地址": {
        "cn": "订阅地址",
        "en": "Subscription Address"
    },
    "请输入在线订阅地址": {
        "cn": "请输入在线订阅地址",
        "en": "Please Enter Online Subscription Address"
    },
    "请输入IP内容，每行一个IP，'//'用于注释": {
        "cn": "请输入IP内容，每行一个IP，'//'用于注释",
        "en": "Please Enter IP Content, One IP per Line, '//' for Comments"
    },
    "更新": {
        "cn": "更新",
        "en": "Update"
    },
    "添加IP组": {
        "cn": "添加IP组",
        "en": "Add IP Group"
    },
    "编辑IP组": {
        "cn": "编辑IP组",
        "en": "Edit IP Group"
    },
    "内容": {
        "cn": "内容",
        "en": "Content"
    },
    "拦截页面": {
        "cn": "拦截页面",
        "en": "Intercept Page"
    },
    "默认提示文字": {
        "cn": "默认提示文字",
        "en": "Default Prompt Text"
    },
    "攻击检测与黑名单": {
        "cn": "攻击检测与黑名单",
        "en": "Attack Detection and Blacklist"
    },
    "网站不存在": {
        "cn": "网站不存在",
        "en": "Website Not Found"
    },
    "网关错误": {
        "cn": "网关错误",
        "en": "Gateway Error"
    },
    "站点维护": {
        "cn": "站点维护",
        "en": "Site Maintenance"
    },
    "网关超时": {
        "cn": "网关超时",
        "en": "Gateway Timeout"
    },
    "页面不存在": {
        "cn": "页面不存在",
        "en": "Page Not Found"
    },
    "身份认证": {
        "cn": "身份认证",
        "en": "Identity Authentication"
    },
    "人机认证": {
        "cn": "人机认证",
        "en": "CAPTCHA Verification"
    },
    "页面配色": {
        "cn": "页面配色",
        "en": "Page Color Scheme"
    },
    "自定义HTML": {
        "cn": "自定义HTML",
        "en": "Custom HTML"
    },
    "主要用于人机验证、等候室、身份认证等场景": {
        "cn": "主要用于人机验证、等候室、身份认证等场景",
        "en": "Primarily used for CAPTCHA verification, waiting rooms, and identity authentication scenarios"
    },
    "主要用于攻击拦截、频率限制、失败报错等场景": {
        "cn": "主要用于攻击拦截、频率限制、失败报错等场景",
        "en": "Primarily used for attack interception, rate limiting, and error handling scenarios"
    },
    "威胁情报共享计划": {
        "cn": "威胁情报共享计划",
        "en": "Threat Intelligence Sharing Program"
    },
    "敏感词库": {
        "cn": "敏感词库",
        "en": "Sensitive Word Library"
    },
    "共享恶意IP信息": {
        "cn": "共享恶意IP信息",
        "en": "Share Malicious IP Information"
    },
    "加入后将共享攻击 IP 信息到社区，并可使用 IP 组 “谋乐恶意IP情报”，内容为社区共享的最具威胁的攻击 IP，每日自动更新。共享内容为设备每日检测到的公网攻击 IP、攻击次数、攻击类型。": {
        "cn": "加入后将共享攻击 IP 信息到社区，并可使用 IP 组 “谋乐恶意IP情报”，内容为社区共享的最具威胁的攻击 IP，每日自动更新。共享内容为设备每日检测到的公网攻击 IP、攻击次数、攻击类型。",
        "en": "After joining, attack IP information will be shared with the community, and the IP group \"Moloch Malicious IP Intelligence\" will be available. It contains the most threatening attack IPs shared by the community, updated daily. Shared content includes public attack IPs detected daily by devices, attack counts, and attack types."
    },
    "开启此选项后，谋乐WAF将在攻击拦截页面中通过 js 获取攻击者的浏览器指纹信息。此信息将用于计算攻击者画像，提升威胁情报的数据准确性。": {
        "cn": "开启此选项后，谋乐WAF将在攻击拦截页面中通过 js 获取攻击者的浏览器指纹信息。此信息将用于计算攻击者画像，提升威胁情报的数据准确性。",
        "en": "When this option is enabled, Moloch WAF will use JavaScript on the attack interception page to collect the attacker’s browser fingerprint information. This data will be used to build attacker profiles and improve the accuracy of threat intelligence."
    },
    "开启等候室后，当网站同时访问人数过多时可以起到有效的削峰作用。详情请查看": {
        "cn": "开启等候室后，当网站同时访问人数过多时可以起到有效的削峰作用。详情请查看",
        "en": "After enabling the waiting room, it can effectively mitigate traffic spikes when the number of concurrent visitors to the website is too high. For details, please refer to"
    },
    "开启频率限制后，严格限制每个 IP 的访问频率，阻止超限的 IP。详情请查看": {
        "cn": "开启频率限制后，严格限制每个 IP 的访问频率，阻止超限的 IP。详情请查看",
        "en": "After enabling the rate limit, it strictly limits the access frequency of each IP and blocks exceeding IPs. For details, please refer to"
    },
    "开启人机验证后，被自动化程序控制的浏览器将会被阻止": {
        "cn": "开启人机验证后，被自动化程序控制的浏览器将会被阻止",
        "en": "After enabling CAPTCHA, browsers controlled by automated programs will be blocked"
    },
    "开启动态防护后，网页内容将会被动态加密保护": {
        "cn": "开启动态防护后，网页内容将会被动态加密保护",
        "en": "After enabling dynamic protection, the webpage content will be dynamically encrypted and protected"
    },
    "开启请求防重放后，通过抓包获得的请求将无法重复提交": {
        "cn": "开启请求防重放后，通过抓包获得的请求将无法重复提交",
        "en": "After enabling request replay prevention, requests obtained through packet capture will not be able to be resubmitted"
    },
    "动态防护": {
        "cn": "动态防护",
        "en": "Dynamic Protection"
    },
    "请求防重放": {
        "cn": "请求防重放",
        "en": "Request Replay Prevention"
    },
    "共享浏览器指纹信息": {
        "cn": "共享浏览器指纹信息",
        "en": "Share Browser Fingerprint Information"
    },
    "规则定义": {
        "cn": "规则定义",
        "en": "Rule Definition"
    },
    "规则名称": {
        "cn": "规则名称",
        "en": "Rule Name"
    },
    "添加一个OR条件": {
        "cn": "添加一个OR条件",
        "en": "Add an OR Condition"
    },
    "添加一个AND条件": {
        "cn": "添加一个AND条件",
        "en": "Add an AND Condition"
    },
    "删除此条件组": {
        "cn": "删除此条件组",
        "en": "Delete This Condition Group"
    },
    "删除此条件": {
        "cn": "删除此条件",
        "en": "Delete This Condition"
    },
    "匹配目标": {
        "cn": "匹配目标",
        "en": "Matching Target"
    },
    "匹配内容": {
        "cn": "匹配内容",
        "en": "Matching Content"
    },
    "源 IP": {
        "cn": "源 IP",
        "en": "Source IP"
    },
    "应用": {
        "cn": "应用",
        "en": "Application"
    },
    "URL": {
        "cn": "URL",
        "en": "URL"
    },
    "URL路径": {
        "cn": "URL路径",
        "en": "URL Path"
    },
    "Host": {
        "cn": "Host",
        "en": "Host"
    },
    "Header": {
        "cn": "Header",
        "en": "Header"
    },
    "GET参数": {
        "cn": "GET参数",
        "en": "GET Parameters"
    },
    "POST参数": {
        "cn": "POST参数",
        "en": "POST Parameters"
    },
    "Body": {
        "cn": "Body",
        "en": "Body"
    },
    "请求方法": {
        "cn": "请求方法",
        "en": "Request Method"
    },
    "等于": {
        "cn": "等于",
        "en": "Equals"
    },
    "不等于": {
        "cn": "不等于",
        "en": "Not Equals"
    },
    "模糊匹配": {
        "cn": "模糊匹配",
        "en": "Fuzzy Match"
    },
    "属于网段": {
        "cn": "属于网段",
        "en": "Belongs to Subnet"
    },
    "不属于网段": {
        "cn": "不属于网段",
        "en": "Does Not Belong to Subnet"
    },
    "属于IP组": {
        "cn": "属于IP组",
        "en": "Belongs to IP Group"
    },
    "不属于IP组": {
        "cn": "不属于IP组",
        "en": "Does Not Belong to IP Group"
    },
    "属于地理位置": {
        "cn": "属于地理位置",
        "en": "Belongs to Geolocation"
    },
    "不属于地理位置": {
        "cn": "不属于地理位置",
        "en": "Does Not Belong to Geolocation"
    },
    "包含": {
        "cn": "包含",
        "en": "Contains"
    },
    "不包含": {
        "cn": "不包含",
        "en": "Does Not Contain"
    },
    "前缀关键字": {
        "cn": "前缀关键字",
        "en": "Prefix Keyword"
    },
    "正则表达式": {
        "cn": "正则表达式",
        "en": "Regular Expression"
    },
    "存在": {
        "cn": "存在",
        "en": "Exists"
    },
    "不存在": {
        "cn": "不存在",
        "en": "Does Not Exist"
    },
    "保存": {
        "cn": "保存",
        "en": "Save"
    },
    "JS防护资源": {
        "cn": "JS防护资源",
        "en": "JS Protection Resources"
    },
    "HTML动态加密": {
        "cn": "HTML动态加密",
        "en": "HTML Dynamic Encryption"
    },
    "快速加解密（跳过解密页面）": {
        "cn": "快速加解密（跳过解密页面）",
        "en": "Fast Encryption/Decryption (Skip Decryption Page)"
    },
    "JS 动态混淆": {
        "cn": "JS 动态混淆",
        "en": "JS Dynamic Obfuscation"
    },
    "性能消耗较大，请充分测试再上线": {
        "cn": "性能消耗较大，请充分测试再上线",
        "en": "Performance consumption is high, please test thoroughly before going live"
    },
    "选择防护资源": {
        "cn": "选择防护资源",
        "en": "Select Protection Resources"
    },
    "图片动态水印": {
        "cn": "图片动态水印",
        "en": "Dynamic Watermark for Images"
    },
    "网站域名": {
        "cn": "网站域名",
        "en": "Website Domain"
    },
    "自定义文本": {
        "cn": "自定义文本",
        "en": "Custom Text"
    },
    "自定义图片": {
        "cn": "自定义图片",
        "en": "Custom Image"
    },
    "水印文本内容": {
        "cn": "水印文本内容",
        "en": "Watermark Text Content"
    },
    "点击或拖入此处上传": {
        "cn": "点击或拖入此处上传",
        "en": "Click or Drag to Upload Here"
    },
    "无交互验证": {
        "cn": "无交互验证",
        "en": "No Interaction Verification"
    },
    "滑动验证": {
        "cn": "滑动验证",
        "en": "Slide Verification"
    },
    "验证有效期": {
        "cn": "验证有效期",
        "en": "Verification Validity Period"
    },
    "该功能开启后，当用户访问您的网站时，需要进行身份认证，不在下方身份源中的用户将被拒之门外。": {
        "cn": "该功能开启后，当用户访问您的网站时，需要进行身份认证，不在下方身份源中的用户将被拒之门外。",
        "en": "Once this feature is enabled, users will be required to authenticate when accessing your website. Users not found in the identity sources below will be denied access."
    },
    "身份源": {
        "cn": "身份源",
        "en": "Identity Source"
    },
    "申请免费证书": {
        "cn": "申请免费证书",
        "en": "Apply for Free Certificate"
    },
    "上传已有证书": {
        "cn": "上传已有证书",
        "en": "Upload Existing Certificate"
    },
    "编辑证书": {
        "cn": "编辑证书",
        "en": "Edit Certificate"
    },
    "申请邮箱": {
        "cn": "申请邮箱",
        "en": "Apply Email"
    },
    "需要在线申请，使用": {
        "cn": "需要在线申请，使用",
        "en": "Requires online application, using "
    },
    "上传本地证书": {
        "cn": "上传本地证书",
        "en": "Upload Local Certificate"
    },
    "Let's Encrypt 的 HTTP-01 验证方法": {
        "cn": "Let's Encrypt 的 HTTP-01 验证方法",
        "en": "Let's Encrypt's HTTP-01 validation method"
    },
    "过期前 30 天自动续期": {
        "cn": "过期前 30 天自动续期",
        "en": "Automatic renewal 30 days before expiration"
    },
    "留空表示不修改": {
        "cn": "留空表示不修改",
        "en": "Leave empty to not modify"
    },
    "私钥": {
        "cn": "私钥",
        "en": "Private Key"
    },
    "成功": {
        "en": "SUCCESS",
        "cn": "成功"
    },
    "失败": {
        "cn": "失败",
        "en": "FAIL"
    },
    "状态": {
        "cn": "状态",
        "en": "Status"
    },
    "名称": {
        "cn": "名称",
        "en": "Name"
    },
    "操作确认": {
        "cn": "操作确认",
        "en": "Operation Confirmation"
    },
    "确认删除此条件组": {
        "cn": "确认删除此条件组",
        "en": "Confirm to Delete This Condition Group"
    },
    "确认删除此条件": {
        "cn": "确认删除此条件",
        "en": "Confirm to Delete This Condition"
    },
    "输入匹配内容": {
        "cn": "输入匹配内容",
        "en": "Enter Match Content"
    },
    "输入Host": {
        "cn": "输入Host",
        "en": "Enter Host"
    },
    "参数名": {
        "cn": "参数名",
        "en": "Parameter Name"
    },
    "输入参数名": {
        "cn": "输入参数名",
        "en": "Enter Parameter Name"
    },
    "输入参数": {
        "cn": "输入参数",
        "en": "Enter Parameter"
    },
    "选择请求方法": {
        "cn": "选择请求方法",
        "en": "Select Request Method"
    },
    "选择IP地理位置": {
        "cn": "选择IP地理位置",
        "en": "Select IP Geolocation"
    },
    "境内": {
        "cn": "境内",
        "en": "Domestic"
    },
    "境外/港澳台": {
        "cn": "境外/港澳台",
        "en": "Overseas/Hong Kong, Macau, and Taiwan"
    },
    "全球": {
        "cn": "全球",
        "en": "Global"
    },
    "配色方案": {
        "cn": "配色方案",
        "en": "Color Scheme"
    },
    "提示文本演示内容": {
        "cn": "提示文本演示内容",
        "en": "Prompt Text Demonstration"
    },
    "背景颜色": {
        "cn": "背景颜色",
        "en": "Background Color"
    },
    "文本颜色": {
        "cn": "文本颜色",
        "en": "Text Color"
    },
    "文字颜色": {
        "cn": "文字颜色",
        "en": "Text Color"
    },
    "提示文字": {
        "cn": "提示文字",
        "en": "Tip Text"
    },
    "这是文本": {
        "cn": "这是文本",
        "en": "This is Text Content"
    },
    "预设方案": {
        "cn": "预设方案",
        "en": "Preset Scheme"
    },
    "自定义HTML页面": {
        "cn": "自定义HTML页面",
        "en": "Custom HTML Page"
    },
    "预览模式": {
        "cn": "预览模式",
        "en": "Preview Mode"
    },
    "编辑模式": {
        "cn": "编辑模式",
        "en": "Edit Mode"
    },
    "账号管理": {
        "cn": "账号管理",
        "en": "Accounts"
    },
    "启用此规则": {
        "cn": "启用此规则",
        "en": "Enable This Rule"
    },
    "记录日志": {
        "cn": "记录日志",
        "en": "Log Recording"
    },
    '条规则': {
        cn: '条规则',
        en: 'Rules'
    },
    "确认关闭日志记录": {
        "cn": "确认关闭日志记录",
        "en": "Confirm to Disable Log Recording"
    },
    "确认开启日志记录": {
        "cn": "确认开启日志记录",
        "en": "Confirm to Enable Log Recording"
    },
    "确认关闭此规则": {
        "cn": "确认关闭此规则",
        "en": "Confirm to Disable This Rule"
    },
    "确认开启此规则": {
        "cn": "确认开启此规则",
        "en": "Confirm to Enable This Rule"
    }
}
/*let unEnList={}
for(let key in wordList){
    if(!wordList[key]?.en || wordList[key].en=='-'){
        unEnList[key]=wordList[key]
    }
}
console.log("unEn",unEnList)*/
export default wordList