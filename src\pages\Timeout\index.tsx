//系统过期
import { Input, message, Modal, Tooltip } from "antd"
import logo from "../../assets/logo.png"
import BugsButton from "../../components/BugsButton"
import { useEffect, useState } from "react"
import api from "../../api"
import { useNavigate } from "react-router-dom"
import { CopyOutlined } from "@ant-design/icons"
const TimeOut = () => {
    const [license_data, setLicenseData] = useState("")
    const [isError, setIsError] = useState(false)
    const navigate = useNavigate()
    const [fingerprint, setFinger] = useState("")
    useEffect(() => {
        api.license.getLicense()
            .then(res => {
                setIsError(false)
                if (res?.data?.data?.fingerprint) {
                    
                    setFinger(res.data.data.fingerprint)
                }
            })
            .catch(err => {
                message.error("无法获取设备License状态")
                setIsError(true)
            })
    }, [])
    const active = () => {
        if (!license_data) {
            message.error("请输入License")
            return
        }
        api.license.activeLicense({
            license_data
        })
            .then(res => {
                if (res?.data) {
                    if (res.data?.code === 0) {
                        message.success("续期成功")
                        setTimeout(() => {
                            navigate("/")
                        }, 2000)
                    } else {
                        message.error(res.data.message)
                    }
                }
            })

    }
    return <div style={{
        width: '100%',
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: "50px",
        flexDirection: 'column'
    }}>
        <img src={logo} style={{ width: "200px", height: '200px', marginTop: "-100px" }} />
        {
            isError
            ?<div style={{
                fontSize: '24px',
                fontWeight: "bold",
                fontFamily: 'AlimamaShuHeiTi'
            }}>无法连接服务器，获取设备License状态异常</div>
            : <>
                    <div style={{
                        fontSize: '24px',
                        fontWeight: "bold",
                        fontFamily: 'AlimamaShuHeiTi'
                    }}>您的系统使用期限已到期，请输入License进行续期</div>
                    <div style={{ color: "#999" }}>请向平台客服提供以下设备数据进行License生成</div>
                    <div style={{ marginTop: '-30px' }}>
                        <span style={{ color: "#cc1212" }}>

                            {
                                fingerprint || '-'
                            }
                        </span><span
                            style={{
                                cursor: "pointer"
                            }}
                            onClick={async () => {
                                if (!fingerprint) return
                                try {
                                    await navigator.clipboard.writeText(fingerprint);
                                    message.success("已复制")
                                } catch (error) {
                                    message.error("无法写入剪贴板")
                                }
                            }}
                        > <CopyOutlined></CopyOutlined></span>
                    </div>


                    <Input.TextArea

                        size="large"
                        value={license_data}
                        onChange={(e) => {
                            setLicenseData(e.target.value)
                        }}
                        placeholder="请输入License"
                        rows={5}
                        style={{
                            resize: 'none',
                            borderRadius: '10px !important',
                            background: "#fafafa",
                            overflowY: 'auto',
                            padding: '10px',
                            boxSizing: "border-box",
                            width: "520px"
                        }}></Input.TextArea>
                    <BugsButton size="large"
                        style={{ borderRadius: '5px', width: '300px', marginTop: '-30px' }} onClick={() => {
                            active()
                        }}>续期</BugsButton>

                </>
        }


    </div>
}
export default TimeOut